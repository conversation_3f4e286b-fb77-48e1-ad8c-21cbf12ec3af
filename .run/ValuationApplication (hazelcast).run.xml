<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ValuationApplication (hazelcast)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="dev,local" />
    <module name="com.solum.xplain.solum-xplain-api.xplain-valuation.app.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.solum.xplain.valuation.ValuationApplication" />
    <option name="VM_PARAMETERS" value="-Djdk.tracePinnedThreads=full" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
