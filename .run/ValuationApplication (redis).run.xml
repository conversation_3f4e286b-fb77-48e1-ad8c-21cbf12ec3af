<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ValuationApplication (redis)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="dev,local,redis" />
    <module name="com.solum.xplain.solum-xplain-api.xplain-valuation.app.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.solum.xplain.valuation.ValuationApplication" />
    <option name="VM_PARAMETERS" value="-Dspring.autoconfigure.exclude=org.springframework.boot.autoconfigure.hazelcast.HazelcastAutoConfiguration -Djdk.tracePinnedThreads=full" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
