package com.solum.xplain.shared.datagrid.impl.redis

import com.redis.testcontainers.RedisContainer
import jakarta.annotation.PreDestroy
import java.util.concurrent.atomic.AtomicBoolean
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.data.redis.connection.RedisClusterConfiguration
import org.springframework.data.redis.connection.RedisNode
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory
import org.testcontainers.containers.Network

/**
 * Spring test configuration that manages Redis cluster lifecycle and provides connection factory beans.
 * This integrates with Spring's test lifecycle for proper resource management.
 */
@TestConfiguration
class RedisClusterTestConfiguration {

  private static final String REDIS_IMAGE = "redis:7.4"
  private static final int REDIS_PORT = 6379

  private final Network redisNetwork = Network.newNetwork()
  private final List<RedisContainer> containers = createContainers()
  private final AtomicBoolean initialized = new AtomicBoolean(false)

  /**
   * Primary JedisConnectionFactory bean for Redis cluster.
   * This will be used by the application under test.
   */
  @Bean
  @Primary
  JedisConnectionFactory jedisConnectionFactory() {
    ensureClusterInitialized()
    return createClusterConnectionFactory()
  }

  /**
   * RedisClusterMappings bean for optimized lock key management.
   */
  @Bean
  RedisClusterMappings redisClusterMappings(JedisConnectionFactory connectionFactory) {
    return new RedisClusterMappings(connectionFactory)
  }

  /**
   * Ensures the Redis cluster is initialized exactly once.
   * This method is thread-safe and idempotent.
   */
  private synchronized void ensureClusterInitialized() {
    if (initialized.get()) {
      return
    }

    println "Initializing Redis test cluster..."

    try {
      // Start all containers
      containers.each { container ->
        container.start()
        println "Started Redis container: ${container.networkAliases[0]} -> ${container.host}:${container.firstMappedPort}"
      }

      // Initialize the cluster
      initializeRedisCluster()

      initialized.set(true)
      println "Redis test cluster initialized successfully"
    } catch (Exception e) {
      println "Failed to initialize Redis cluster: ${e.message}"
      throw new RuntimeException("Redis cluster initialization failed", e)
    }
  }

  /**
   * Cleanup method called by Spring when the application context is destroyed.
   */
  @PreDestroy
  void cleanup() {
    if (!initialized.get()) {
      return
    }

    println "Cleaning up Redis test cluster..."

    // Stop containers
    containers.each { container ->
      try {
        container.stop()
      } catch (Exception ignored) {
        // Ignore stop errors
      }
    }

    // Close network
    try {
      redisNetwork.close()
    } catch (Exception ignored) {
      // Ignore network cleanup errors
    }

    initialized.set(false)
    println "Redis test cluster cleanup completed"
  }

  private List<RedisContainer> createContainers() {
    def containerNames = ["redis1", "redis2", "redis3"]
    def network = redisNetwork
    return containerNames.collect { name ->
      new RedisContainer(REDIS_IMAGE)
        .withNetwork(network)
        .withNetworkAliases(name)
        .withCommand(
        "redis-server",
        "--cluster-enabled", "yes",
        "--cluster-config-file", "nodes.conf",
        "--cluster-node-timeout", "5000",
        "--appendonly", "yes",
        "--port", REDIS_PORT.toString()
        )
    }
  }

  private void initializeRedisCluster() {
    // Use internal network addresses for cluster initialization
    def nodeAddresses = containers.collect { container ->
      "${container.networkAliases[0]}:${REDIS_PORT}"
    }

    println "Initializing Redis cluster with internal network nodes: ${nodeAddresses}"

    // Use the first container to execute the cluster create command
    def firstContainer = containers[0]

    try {
      // Create the cluster using redis-cli with internal network addresses
      def clusterCreateCommand = [
        "redis-cli",
        "--cluster",
        "create",
        *nodeAddresses,
        "--cluster-replicas",
        "0",
        // No replicas for simplicity in tests
        "--cluster-yes"  // Auto-confirm
      ]

      def result = firstContainer.execInContainer(clusterCreateCommand as String[])
      println "Cluster creation result: ${result.stdout}"

      if (result.exitCode != 0) {
        throw new RuntimeException("Failed to create Redis cluster: ${result.stderr}")
      }

      // Wait a bit for cluster to stabilize
      Thread.sleep(2000)

      // Verify cluster status
      def statusResult = firstContainer.execInContainer("redis-cli", "cluster", "info")
      println "Cluster status: ${statusResult.stdout}"

      // Also check cluster nodes
      def nodesResult = firstContainer.execInContainer("redis-cli", "cluster", "nodes")
      println "Cluster nodes: ${nodesResult.stdout}"
    } catch (Exception e) {
      throw new RuntimeException("Failed to initialize Redis cluster", e)
    }
  }

  private JedisConnectionFactory createClusterConnectionFactory() {
    // Create a single cluster configuration with all nodes
    def clusterConfig = new RedisClusterConfiguration()

    containers.each { container ->
      clusterConfig.addClusterNode(new RedisNode(container.host, container.firstMappedPort))
    }

    clusterConfig.setMaxRedirects(3)

    def factory = new JedisConnectionFactory(clusterConfig)
    factory.afterPropertiesSet()

    return factory
  }

  /**
   * Provides access to the containers for debugging purposes.
   */
  List<RedisContainer> getContainers() {
    return Collections.unmodifiableList(containers)
  }
}
