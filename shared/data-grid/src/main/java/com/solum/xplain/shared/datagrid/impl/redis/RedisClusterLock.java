package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.FencingTokenService;
import com.solum.xplain.shared.datagrid.RenewableClusterLock;
import jakarta.annotation.PreDestroy;
import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.dao.CannotAcquireLockException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.retry.support.RetryTemplate;

/**
 * Implementation of Redis Redlock algorithm with fencing tokens.
 *
 * <p>This implementation provides a distributed locking mechanism across multiple Redis instances
 * locks are acquired only when a majority (quorum) of Redis instances agree. Each lock acquisition
 * generates a monotonic fencing token to prevent stale lock operations.
 *
 * <p>This implementation handles the following scenarios:
 *
 * <ul>
 *   <li>Redis instance is down or unreachable
 *   <li>Attempting to acquire the lock on all Redis instances
 *   <li>Lock is acquired only if we get a majority agreement of the Redis instances (quorum)
 *   <li>Validating that the lock acquisition time is within acceptable bounds
 *   <li>Cleaning up partial locks if the full lock cannot be acquired
 *   <li>Clean up locks on shutdown
 *   <li>Fencing token generation and validation for distributed lock safety
 *   <li>Stale lock detection and prevention
 * </ul>
 *
 * @see <a href="https://redis.io/docs/latest/develop/use/patterns/distributed-locks/">Redis
 *     Distributed Locks</a>
 */
@Slf4j
@NullMarked
class RedisClusterLock implements RenewableClusterLock {
  private static final Duration DEFAULT_LOCK_TTL = Duration.ofSeconds(10);
  private static final Duration DEFAULT_ACQUIRE_TIMEOUT = Duration.ofSeconds(2);
  private static final Duration CLOCK_DRIFT_MARGIN = Duration.ofMillis(200);

  private static final String LOCK_VALUE_PREFIX = "LOCK_VALUE__";

  private final Map<StringRedisTemplate, String> templateToNodeSpecificKey;
  private final String lockKey;
  private final Duration lockTtl;
  private final Duration acquireTimeout;
  private final int QUORUM;
  private final FencingTokenService fencingTokenService;

  private final ConcurrentHashMap<String, LockState> locks = new ConcurrentHashMap<>();

  private static final RedisScript<Boolean> LOCK_SCRIPT =
      new DefaultRedisScript<>(
          "if redis.call('set', KEYS[1], ARGV[1], 'NX', 'PX', ARGV[2]) then return 1 else return 0 end",
          Boolean.class);

  private static final RedisScript<Boolean> UNLOCK_SCRIPT =
      new DefaultRedisScript<>(
          "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end",
          Boolean.class);

  private static final RedisScript<Long> RENEW_SCRIPT =
      new DefaultRedisScript<>(
          "if redis.call('get', KEYS[1]) == ARGV[1] then "
              + "  return redis.call('pexpire', KEYS[1], ARGV[2]) "
              + "else return 0 end",
          Long.class);

  /** Internal class to hold lock state including value and fencing token */
  private static class LockState {
    final String lockValue;
    final Long fencingToken;
    final long acquisitionTime;

    LockState(String lockValue, Long fencingToken) {
      this.lockValue = lockValue;
      this.fencingToken = fencingToken;
      this.acquisitionTime = System.currentTimeMillis();
    }
  }

  public RedisClusterLock(
      RedisClusterMappings clusterMappings,
      String lockKey,
      FencingTokenService fencingTokenService) {
    this(clusterMappings, lockKey, DEFAULT_LOCK_TTL, DEFAULT_ACQUIRE_TIMEOUT, fencingTokenService);
  }

  public RedisClusterLock(
      RedisClusterMappings clusterMappings,
      String lockKey,
      Duration lockTtl,
      Duration acquireTimeout,
      FencingTokenService fencingTokenService) {

    // Use the enhanced connection factory for optimized key management
    List<StringRedisTemplate> redisTemplates = clusterMappings.getTemplates();
    this.lockKey = lockKey;
    this.lockTtl = lockTtl;
    this.acquireTimeout = acquireTimeout;
    this.QUORUM = redisTemplates.size() / 2 + 1;
    this.fencingTokenService = fencingTokenService;

    if (redisTemplates.size() < QUORUM) {
      throw new IllegalArgumentException(
          "We need at least " + QUORUM + " Redis instances, but got " + redisTemplates.size());
    }

    // Use pre-computed node-specific keys from the enhanced factory
    this.templateToNodeSpecificKey = clusterMappings.createNodeSpecificKeys(lockKey);
    log.debug(
        "Created RedisClusterLock for key '{}' with {} Redis instances and {} node-specific keys",
        lockKey,
        redisTemplates.size(),
        templateToNodeSpecificKey.size());
  }

  @Override
  public String getName() {
    return lockKey;
  }

  /** Gets the current fencing token for this lock instance. */
  @Nullable
  public Long getCurrentFencingToken() {
    LockState lockState = locks.get(lockKey);
    return lockState != null ? lockState.fencingToken : null;
  }

  /**
   * Gets the current fencing token for this lock from the shared counter. Gets latest token
   * generated across all instances.
   */
  public Long getSharedFencingToken() {
    return fencingTokenService.getCurrentToken(lockKey);
  }

  /**
   * Validates if the current lock held by this instance is still valid by comparing its fencing
   * token with the shared counter.
   */
  public boolean isCurrentLockValid() {
    LockState lockState = locks.get(lockKey);
    if (lockState == null) {
      return false;
    }
    return fencingTokenService.validateToken(lockKey, lockState.fencingToken);
  }

  public boolean isLockExpired() {
    LockState lockState = locks.get(lockKey);
    if (lockState == null) {
      return true;
    }

    long elapsed = System.currentTimeMillis() - lockState.acquisitionTime;
    return elapsed >= lockTtl.toMillis();
  }

  /**
   * Validates if a fencing token is valid for operations on this resource. This should be called
   * before performing any critical operations.
   */
  public boolean validateFencingToken(long token) {
    return fencingTokenService.validateToken(lockKey, token)
        && fencingTokenService.isValidToken(token);
  }

  @Override
  public void lock() {
    RetryTemplate.builder()
        .infiniteRetry()
        .uniformRandomBackoff(
            10, 100) // This will add jitter for retry so we don't get thundering herd
        .notRetryOn(InterruptedException.class)
        .build()
        .execute(
            context -> {
              if (!acquireLock()) {
                throw new CannotAcquireLockException("Lock not acquired");
              }
              return null;
            });
  }

  @Override
  public void lockInterruptibly() {
    try {
      RetryTemplate.builder()
          .withTimeout(acquireTimeout.toMillis())
          .uniformRandomBackoff(10, 100)
          .notRetryOn(InterruptedException.class)
          .build()
          .execute(
              context -> {
                if (!acquireLock()) {
                  throw new CannotAcquireLockException("Lock not acquired");
                }
                return null;
              });
    } catch (CannotAcquireLockException e) {
      throw new CannotAcquireLockException("Failed to acquire lock: " + lockKey);
    }
  }

  /*
   * Tries to acquire the lock once.
   */
  @Override
  public boolean tryLock() {
    try {
      return RetryTemplate.builder()
          .maxAttempts(1)
          .build()
          .execute(
              context -> {
                if (!acquireLock()) {
                  throw new CannotAcquireLockException("Lock not acquired");
                }
                return true;
              });
    } catch (CannotAcquireLockException e) {
      return false;
    }
  }

  @Override
  public boolean tryLock(long time, TimeUnit unit) {
    try {
      return RetryTemplate.builder()
          .withTimeout(unit.toMillis(time))
          .uniformRandomBackoff(10, 100)
          .notRetryOn(InterruptedException.class)
          .build()
          .execute(
              context -> {
                if (!acquireLock()) {
                  throw new CannotAcquireLockException("Lock not acquired");
                }
                return true;
              });
    } catch (CannotAcquireLockException e) {
      return false;
    }
  }

  @Override
  public void unlock() {
    LockState lockState = locks.remove(lockKey);
    if (lockState == null) {
      log.warn("No lock held for key: {}", lockKey);
      return;
    }

    // Validate that our lock is still current before unlocking
    if (!fencingTokenService.validateToken(lockKey, lockState.fencingToken)) {
      log.warn(
          "Attempting to unlock with stale fencing token {} for key: {}",
          lockState.fencingToken,
          lockKey);
      return;
    }

    try {
      // Use the pre-computed node-specific key mapping
      for (Map.Entry<StringRedisTemplate, String> entry : templateToNodeSpecificKey.entrySet()) {
        StringRedisTemplate redisTemplate = entry.getKey();
        String nodeSpecificKey = entry.getValue();

        try {
          boolean result =
              redisTemplate.execute(UNLOCK_SCRIPT, List.of(nodeSpecificKey), lockState.lockValue);
          if (result) {
            log.debug("Successfully unlocked key {} on Redis instance", nodeSpecificKey);
          } else {
            log.debug(
                "Lock was not present or already expired for key {} on Redis instance",
                nodeSpecificKey);
          }
        } catch (Exception e) {
          String errorMessage = e.getMessage();
          if (errorMessage != null && errorMessage.contains("Redirect:")) {
            log.debug(
                "Redis cluster redirect during unlock for key {}: {}",
                nodeSpecificKey,
                errorMessage);
          } else {
            log.warn(
                "Failed to unlock Redis instance for key {}: {}", nodeSpecificKey, errorMessage);
          }
        }
      }
    } catch (Exception e) {
      log.error("Error unlocking key {}: {}", lockKey, e.getMessage(), e);
    }
  }

  @Override
  public Condition newCondition() {
    throw new UnsupportedOperationException("Conditions are not supported");
  }

  private boolean acquireLock() {
    // Check if we already hold a valid lock
    if (isCurrentLockValid() && !isLockExpired()) {
      log.debug("Already holding valid lock for key: {}", lockKey);
      return true;
    }

    // Clean up any stale lock state
    if (locks.containsKey(lockKey)) {
      log.debug("Cleaning up stale lock state for key: {}", lockKey);
      locks.remove(lockKey);
    }

    String lockValue = LOCK_VALUE_PREFIX + UUID.randomUUID();

    try {
      long startTime = System.currentTimeMillis();
      int successCount = 0;
      Set<StringRedisTemplate> successfulInstances = new HashSet<>();

      for (Map.Entry<StringRedisTemplate, String> entry : templateToNodeSpecificKey.entrySet()) {
        StringRedisTemplate redisTemplate = entry.getKey();
        String nodeSpecificKey = entry.getValue();

        try {
          boolean result =
              redisTemplate.execute(
                  LOCK_SCRIPT,
                  List.of(nodeSpecificKey),
                  lockValue,
                  String.valueOf(lockTtl.toMillis()));
          if (result) {
            successCount++;
            successfulInstances.add(redisTemplate);
          }
        } catch (Exception e) {
          String errorMessage = e.getMessage();
          if (errorMessage != null && errorMessage.contains("Redirect:")) {
            // This is a normal Redis cluster redirect - the key doesn't map to the expected node
            log.debug("Redis cluster redirect for key {}: {}", nodeSpecificKey, errorMessage);
            // We could potentially follow the redirect, but for now just treat as a failed attempt
          } else {
            log.warn(
                "Failed to acquire lock from Redis instance for key {}: {}",
                nodeSpecificKey,
                errorMessage);
          }
        }
      }

      long elapsed = System.currentTimeMillis() - startTime;
      return validLock(lockValue, successCount, elapsed, successfulInstances);
    } catch (Exception e) {
      log.error("Unexpected error getting lock for key {}: {}", lockKey, e.getMessage(), e);
      return false;
    }
  }

  private boolean validLock(
      String lockValue,
      int successCount,
      long elapsed,
      Set<StringRedisTemplate> successfulInstances) {

    // remaining time the lock has left to live before it expires
    long validLockTime = lockTtl.toMillis() - CLOCK_DRIFT_MARGIN.toMillis() - elapsed;
    // lock is valid if we have a quorum and the lock has enough time to live
    boolean valid = successCount >= QUORUM && validLockTime > 0;

    if (valid) {
      // Lock acquired successfully - generate fencing token
      Long fencingToken = fencingTokenService.generateToken(lockKey);
      locks.put(lockKey, new LockState(lockValue, fencingToken));
      return true;
    } else {
      // Failed to acquire or took too long - cleanup partial locks
      if (successCount > 0) {
        log.debug(
            "Cleanup partial locks - success count: {}, validity time: {}ms",
            successCount,
            validLockTime);
        cleanupPartialLock(successfulInstances, lockValue);
      }
      return false;
    }
  }

  private void cleanupPartialLock(Set<StringRedisTemplate> instances, String lockValue) {
    try {
      // Use the pre-computed node-specific key mapping
      for (StringRedisTemplate redisTemplate : instances) {
        String nodeSpecificKey = templateToNodeSpecificKey.get(redisTemplate);
        if (nodeSpecificKey == null) {
          log.warn(
              "No node-specific key found for template during cleanup, using base key: {}",
              lockKey);
          nodeSpecificKey = lockKey;
        }

        try {
          redisTemplate.execute(UNLOCK_SCRIPT, List.of(nodeSpecificKey), lockValue);
          log.debug("Cleaned up partial lock for key {} on Redis instance", nodeSpecificKey);
        } catch (Exception e) {
          String errorMessage = e.getMessage();
          if (errorMessage != null && errorMessage.contains("Redirect:")) {
            log.debug(
                "Redis cluster redirect during cleanup for key {}: {}",
                nodeSpecificKey,
                errorMessage);
          } else {
            log.warn(
                "Failed to cleanup partial lock from Redis instance for key {}: {}",
                nodeSpecificKey,
                errorMessage);
          }
        }
      }
    } catch (Exception e) {
      log.error("Error during partial lock cleanup for key {}: {}", lockKey, e.getMessage(), e);
    }
  }

  /**
   * Renews the TTL (time-to-live) of the currently held distributed lock across all Redis
   * instances.
   *
   * <p>This method extends the expiration time of an existing lock by resetting its TTL to the
   * configured lock duration. The renewal is only successful if:
   *
   * <ul>
   *   <li>A lock is currently held by this instance for the configured lock key
   *   <li>The fencing token is still valid (not superseded by a newer lock acquisition)
   *   <li>A quorum of Redis instances successfully renew the lock
   *   <li>The renewal operation completes within the valid time window (accounting for clock drift)
   * </ul>
   *
   * <p>The method performs fencing token validation before attempting renewal to prevent stale lock
   * operations. If the fencing token is no longer valid, it indicates another instance has acquired
   * the lock, and the renewal will fail.
   *
   * <p>Lock renewal follows the same quorum-based approach as lock acquisition - the renewal is
   * considered successful only if at least {@code QUORUM} Redis instances successfully extend the
   * lock's TTL. This ensures consistency across the distributed system even if some Redis instances
   * are unavailable.
   *
   * <p>If renewal fails for any reason (insufficient quorum, stale fencing token, or timing
   * constraints), the local lock state is cleaned up and the method returns {@code false}.
   *
   * @return {@code true} if the lock was successfully renewed across a quorum of Redis instances,
   *     {@code false} if renewal failed due to no current lock, stale fencing token, insufficient
   *     quorum, or timing constraints
   * @see #acquireLock() for the initial lock acquisition logic
   * @see #validateFencingToken(long) for fencing token validation
   * @see #isCurrentLockValid() for checking current lock validity
   */
  public boolean renewLock() {
    LockState lockState = locks.get(lockKey);
    if (lockState == null) {
      log.warn("Cannot renew lock: no current lock held for key {}", lockKey);
      return false;
    }

    // Validate fencing token before renewal
    if (!fencingTokenService.validateToken(lockKey, lockState.fencingToken)) {
      log.warn(
          "Cannot renew lock: stale fencing token {} for key {}", lockState.fencingToken, lockKey);
      locks.remove(lockKey); // Clean up stale state
      return false;
    }

    int successCount = 0;
    long startTime = System.currentTimeMillis();

    try {
      // Use the pre-computed node-specific key mapping
      for (Map.Entry<StringRedisTemplate, String> entry : templateToNodeSpecificKey.entrySet()) {
        StringRedisTemplate redisTemplate = entry.getKey();
        String nodeSpecificKey = entry.getValue();

        try {
          Long result =
              redisTemplate.execute(
                  RENEW_SCRIPT,
                  List.of(nodeSpecificKey),
                  lockState.lockValue,
                  String.valueOf(lockTtl.toMillis()));

          if (result == 1L) {
            successCount++;
            log.debug("Successfully renewed lock for key {} on Redis instance", nodeSpecificKey);
          } else {
            log.debug(
                "Lock renewal failed for key {} on Redis instance (lock may have expired)",
                nodeSpecificKey);
          }
        } catch (Exception e) {
          String errorMessage = e.getMessage();
          if (errorMessage != null && errorMessage.contains("Redirect:")) {
            log.debug(
                "Redis cluster redirect during lock renewal for key {}: {}",
                nodeSpecificKey,
                errorMessage);
          } else {
            log.warn(
                "Failed to renew lock in Redis instance for key {}: {}",
                nodeSpecificKey,
                errorMessage);
          }
        }
      }
    } catch (Exception e) {
      log.error("Error during lock renewal for key {}: {}", lockKey, e.getMessage(), e);
    }

    long elapsed = System.currentTimeMillis() - startTime;
    long validLockTime = lockTtl.toMillis() - CLOCK_DRIFT_MARGIN.toMillis() - elapsed;

    boolean renewed = successCount >= QUORUM && validLockTime > 0;

    if (renewed) {
      locks.put(lockKey, new LockState(lockState.lockValue, lockState.fencingToken));
      log.debug(
          "Lock renewed successfully for key: {} with {} successful instances",
          lockKey,
          successCount);
    } else {
      log.warn(
          "Failed to renew lock for key: {}. Success count: {}, Valid time: {}ms",
          lockKey,
          successCount,
          validLockTime);
      locks.remove(lockKey);
    }

    return renewed;
  }

  /**
   * Cleanup all locks on shutdown. This method validates fencing tokens before unlocking to prevent
   * stale unlocks.
   */
  public void unlockAll() {
    Set<String> currentLocks = new HashSet<>(locks.keySet());
    for (String lockKey : currentLocks) {
      try {
        LockState lockState = locks.remove(lockKey);
        if (lockState != null) {
          // Validate fencing token before unlocking
          boolean shouldUnlock;
          shouldUnlock = fencingTokenService.validateToken(lockKey, lockState.fencingToken);
          if (!shouldUnlock) {
            log.warn(
                "Skipping unlock of stale lock for key: {} with token: {}",
                lockKey,
                lockState.fencingToken);
            continue;
          }

          // Unlock this specific lock on all Redis instances using node-specific keys
          try {
            for (Map.Entry<StringRedisTemplate, String> entry :
                templateToNodeSpecificKey.entrySet()) {
              StringRedisTemplate redisTemplate = entry.getKey();
              String nodeSpecificKey = entry.getValue();

              try {
                redisTemplate.execute(UNLOCK_SCRIPT, List.of(nodeSpecificKey), lockState.lockValue);
                log.debug("Unlocked key {} on Redis instance during shutdown", nodeSpecificKey);
              } catch (Exception e) {
                String errorMessage = e.getMessage();
                if (errorMessage != null && errorMessage.contains("Redirect:")) {
                  log.debug(
                      "Redis cluster redirect during shutdown unlock for key {}: {}",
                      nodeSpecificKey,
                      errorMessage);
                } else {
                  log.warn(
                      "Failed to unlock Redis instance for key {} during shutdown: {}",
                      nodeSpecificKey,
                      errorMessage);
                }
              }
            }
          } catch (Exception e) {
            log.error(
                "Error getting node-specific keys during shutdown unlock for key {}: {}",
                lockKey,
                e.getMessage(),
                e);
          }
          log.debug("Released lock: {} with fencing token: {}", lockKey, lockState.fencingToken);
        }
      } catch (Exception e) {
        log.warn("Failed to unlock {} during shutdown: {}", lockKey, e.getMessage());
      }
    }
  }

  /** Automatically unlock all locks on shutdown. */
  @PreDestroy
  public void shutdown() {
    unlockAll();
  }
}
