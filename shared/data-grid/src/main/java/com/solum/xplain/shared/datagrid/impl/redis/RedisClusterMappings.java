package com.solum.xplain.shared.datagrid.impl.redis;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.StreamSupport;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.redis.connection.RedisClusterConnection;
import org.springframework.data.redis.connection.RedisClusterNode;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import redis.clients.jedis.util.JedisClusterCRC16;

/**
 * Enhanced Redis connection factory that pre-computes and caches hash keys for cluster nodes. This
 * eliminates the need to recompute node-specific keys for every lock instance.
 */
@Slf4j
@NullMarked
class RedisClusterMappings {

  private final JedisConnectionFactory connectionFactory;
  private final List<StringRedisTemplate> templates;
  private final Map<StringRedisTemplate, String> templateToHashKey;

  public RedisClusterMappings(RedisConnectionFactory connectionFactory) {
    if (!(connectionFactory instanceof JedisConnectionFactory jedisConnectionFactory)) {
      throw new IllegalArgumentException("Only Jedis connection factory is supported");
    }
    if (!jedisConnectionFactory.isRedisClusterAware()) {
      log.warn("Redis cluster is not enabled - Redlock will not work resiliently.");
    }
    this.connectionFactory = jedisConnectionFactory;
    this.templates = createTemplatesForClusterNodes();
    this.templateToHashKey = createTemplateToHashKeyMapping();

    log.info(
        "Created RedisClusterMappings with {} Redis templates and {} hash key mappings",
        templates.size(),
        templateToHashKey.size());
  }

  /** Returns the list of Redis templates for cluster nodes. */
  public List<StringRedisTemplate> getTemplates() {
    return Collections.unmodifiableList(templates);
  }

  /** Creates node-specific keys for a given lock key using the pre-computed hash keys. */
  public Map<StringRedisTemplate, String> createNodeSpecificKeys(String lockKey) {
    Map<StringRedisTemplate, String> result = new HashMap<>();

    for (Map.Entry<StringRedisTemplate, String> entry : templateToHashKey.entrySet()) {
      StringRedisTemplate template = entry.getKey();
      String hashKey = entry.getValue();
      String nodeSpecificKey = lockKey + "{" + hashKey + "}";
      result.put(template, nodeSpecificKey);
    }

    log.debug("Created {} node-specific keys for lock key '{}'", result.size(), lockKey);
    return result;
  }

  private List<StringRedisTemplate> createTemplatesForClusterNodes() {
    if (!connectionFactory.isRedisClusterAware()) {
      return List.of(new StringRedisTemplate(connectionFactory));
    }

    try (RedisClusterConnection clusterConnection = connectionFactory.getClusterConnection()) {
      return StreamSupport.stream(clusterConnection.clusterGetNodes().spliterator(), false)
          .filter(RedisNode::isMaster)
          .filter(node -> !node.isMarkedAsFail())
          .map(this::createTemplateForNode)
          .toList();
    }
  }

  private StringRedisTemplate createTemplateForNode(RedisClusterNode node) {
    assert node.getHost() != null && node.getPort() != null;
    RedisStandaloneConfiguration config =
        new RedisStandaloneConfiguration(node.getHost(), node.getPort());
    JedisConnectionFactory nodeFactory = new JedisConnectionFactory(config);
    nodeFactory.afterPropertiesSet();
    return new StringRedisTemplate(nodeFactory);
  }

  private Map<StringRedisTemplate, String> createTemplateToHashKeyMapping() {
    if (!connectionFactory.isRedisClusterAware()) {
      return Map.of(templates.get(0), "standalone");
    }

    Map<StringRedisTemplate, String> mapping = new HashMap<>();

    try (RedisClusterConnection clusterConnection = connectionFactory.getClusterConnection()) {
      var clusterNodes = clusterConnection.clusterGetNodes();
      log.debug(
          "Creating hash key mappings for master cluster nodes, {} templates available",
          templates.size());

      int templateIndex = 0;
      int nodeCount = 0;

      for (var node : clusterNodes) {
        nodeCount++;

        // Only process master nodes for lock operations
        if (!node.isMaster()) {
          log.debug(
              "Skipping replica node {}:{} for hash key generation",
              node.getHost(),
              node.getPort());
          continue;
        }

        // Skip failed nodes
        if (node.isMarkedAsFail()) {
          log.debug(
              "Skipping failed node {}:{} for hash key generation", node.getHost(), node.getPort());
          continue;
        }

        if (templateIndex >= templates.size()) {
          log.warn(
              "More master cluster nodes than available templates ({}). Some nodes will be skipped.",
              templates.size());
          break;
        }

        StringRedisTemplate template = templates.get(templateIndex);
        String hashKey = findHashKeyForNode(node);
        mapping.put(template, hashKey);

        log.debug(
            "Mapped template {} to master node {}:{} with hash key: {}",
            templateIndex,
            node.getHost(),
            node.getPort(),
            hashKey);
        templateIndex++;
      }

      log.debug(
          "Successfully created hash key mappings for {} master nodes out of {} total cluster nodes",
          templateIndex,
          nodeCount);
    } catch (Exception e) {
      log.error("Failed to create template to hash key mapping: {}", e.getMessage(), e);
    }

    return mapping;
  }

  private String findHashKeyForNode(RedisClusterNode node) {
    var slotRanges = node.getSlotRange();
    if (slotRanges.getSlotsArray().length == 0) {
      log.warn(
          "Node {}:{} has no slots assigned. Using fallback hash key.",
          node.getHost(),
          node.getPort());
      return "no-slots-" + node.getHost() + ":" + node.getPort();
    }

    log.debug(
        "Finding hash key for node {}:{} with {} slots",
        node.getHost(),
        node.getPort(),
        slotRanges.getSlotsArray().length);

    return findHashKeyForSlots(node.getPort(), slotRanges.getSlots());
  }

  private String findHashKeyForSlots(int port, Set<Integer> slots) {
    // Use a unique identifier to avoid collisions across different instances
    String baseKey = "slot-" + port + "-" + System.identityHashCode(this);

    for (int i = 0; i < 10000; i++) {
      var hashKey = baseKey + "-" + i;
      int calculatedSlot = calculateSlot(hashKey);
      if (slots.contains(calculatedSlot)) {
        // Validate the hash key mapping
        if (validateHashKeyMapping(hashKey, slots)) {
          log.debug(
              "Found hash key '{}' that maps to slot {} for port {}",
              hashKey,
              calculatedSlot,
              port);
          return hashKey;
        }
      }
    }

    // If we can't find a matching slot, use the first available slot
    if (!slots.isEmpty()) {
      int firstSlot = slots.iterator().next();
      log.warn(
          "Could not find hash key for port {} that maps to any of its slots {}. Using fallback for slot {}",
          port,
          slots,
          firstSlot);

      // Try to find a hash key that maps to the first slot
      for (int i = 0; i < 10000; i++) {
        var hashKey = "fallback-" + firstSlot + "-" + i;
        if (calculateSlot(hashKey) == firstSlot) {
          if (validateHashKeyMapping(hashKey, Set.of(firstSlot))) {
            return hashKey;
          }
        }
      }
    }

    // Final fallback
    log.error(
        "Could not find any suitable hash key for port {} with slots {}. Using basic fallback.",
        port,
        slots);
    return "slot-" + port;
  }

  private int calculateSlot(String key) {
    return JedisClusterCRC16.getCRC16(key.getBytes()) % 16384;
  }

  /**
   * Validates that a hash key maps to one of the expected slots. This helps debug issues with key
   * generation in Redis cluster environments.
   */
  private boolean validateHashKeyMapping(String hashKey, Set<Integer> expectedSlots) {
    int actualSlot = calculateSlot(hashKey);
    boolean isValid = expectedSlots.contains(actualSlot);

    if (!isValid) {
      log.warn(
          "Hash key '{}' maps to slot {} but expected one of: {}",
          hashKey,
          actualSlot,
          expectedSlots);
    } else {
      log.debug("Hash key '{}' correctly maps to slot {}", hashKey, actualSlot);
    }

    return isValid;
  }
}
