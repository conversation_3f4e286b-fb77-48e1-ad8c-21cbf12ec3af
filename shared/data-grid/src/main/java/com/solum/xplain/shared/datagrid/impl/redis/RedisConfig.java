package com.solum.xplain.shared.datagrid.impl.redis;

import static com.solum.xplain.shared.datagrid.impl.redis.RedisCacheMessagePublisher.INVALIDATION_TOPIC;
import static org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration.APPLICATION_TASK_EXECUTOR_BEAN_NAME;
import static org.springframework.data.redis.serializer.RedisSerializationContext.SerializationPair.fromSerializer;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.serializers.JavaSerializer;
import com.esotericsoftware.kryo.util.DefaultInstantiatorStrategy;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.solum.xplain.shared.datagrid.FencingTokenService;
import com.solum.xplain.shared.datagrid.KeyValueCache;
import com.solum.xplain.shared.datagrid.LocalCacheReplica;
import com.solum.xplain.shared.datagrid.LockManager;
import com.solum.xplain.shared.datagrid.PubSubTopic;
import com.solum.xplain.shared.datagrid.SharedCacheTtl;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.StreamSupport;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.objenesis.strategy.StdInstantiatorStrategy;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.cache.RedisCacheManagerBuilderCustomizer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

@Configuration
@ConditionalOnClass(RedisTemplate.class)
@ConditionalOnProperty(
    prefix = "app.data-grid.redis",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = true)
@ConditionalOnMissingBean(
    type = {"com.hazelcast.config.Config", "com.hazelcast.client.config.ClientConfig"})
@Slf4j
@NullMarked
public class RedisConfig {
  public RedisConfig() {
    log.info("Redis: configuring for local client");
  }

  @Bean
  StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
    log.info(
        "Redis: Configuring StringRedisTemplate with connection factory: {}",
        redisConnectionFactory);
    return new StringRedisTemplate(redisConnectionFactory);
  }

  @Bean
  RedisTemplate<String, Object> redisTemplate(
      RedisConnectionFactory redisConnectionFactory, RedisSerializer<Object> redisSerializer) {
    log.info("Redis: Configuring RedisTemplate");
    var redisTemplate = new RedisTemplate<String, Object>();
    redisTemplate.setConnectionFactory(redisConnectionFactory);
    // TODO: Protostuff serializer?
    redisTemplate.setDefaultSerializer(redisSerializer);
    redisTemplate.setKeySerializer(RedisSerializer.string());
    redisTemplate.setHashKeySerializer(RedisSerializer.string());
    return redisTemplate;
  }

  @Bean
  RedisMessageListenerContainer redisMessageListenerContainer(
      RedisConnectionFactory redisConnectionFactory,
      @Qualifier(APPLICATION_TASK_EXECUTOR_BEAN_NAME) TaskExecutor taskExecutor) {
    RedisMessageListenerContainer container = new RedisMessageListenerContainer();
    container.setConnectionFactory(redisConnectionFactory);
    container.setTaskExecutor(taskExecutor);
    return container;
  }

  @Bean
  public CacheManager cacheManager(
      List<LocalCacheReplica> localCacheReplicas,
      RedisConnectionFactory redisConnectionFactory,
      RedisCacheManagerBuilderCustomizer redisCacheManagerBuilderCustomizer,
      RedisTemplate<String, Object> redisTemplate,
      RedisMessageListenerContainer redisMessageListenerContainer,
      RedisSerializer<Object> redisSerializer) {

    log.info("Redis: Configuring unified cache manager");

    RedisSerializationContext.SerializationPair<Object> serializationPair =
        fromSerializer(redisSerializer);

    RedisCacheConfiguration defaultCacheConfig =
        RedisCacheConfiguration.defaultCacheConfig().serializeValuesWith(serializationPair);

    RedisCacheManager.RedisCacheManagerBuilder builder =
        RedisCacheManager.builder(redisConnectionFactory).cacheDefaults(defaultCacheConfig);

    redisCacheManagerBuilderCustomizer.customize(builder);
    RedisCacheManager redisCacheManager = builder.build();

    PubSubTopic<RedisCacheInvalidationMessage> topic =
        new RedisPubSubTopic<>(redisTemplate, redisMessageListenerContainer, INVALIDATION_TOPIC);

    RedisCacheMessagePublisher redisCacheMessagePublisher = new RedisCacheMessagePublisher(topic);
    return new CaffeineRedisBackedCacheManager(
        localCacheReplicas, redisCacheManager, redisCacheMessagePublisher);
  }

  @Bean
  public RedisCacheInvalidationMessageListener cacheMessageListener(
      RedisDataGrid dataGrid, CacheManager cacheManager) {
    return new RedisCacheInvalidationMessageListener(dataGrid, cacheManager);
  }

  @Bean
  RedisCacheManagerBuilderCustomizer redisCacheManagerBuilderCustomizer(
      List<SharedCacheTtl> sharedCacheTtls, RedisSerializer<Object> redisSerializer) {
    RedisSerializationContext.SerializationPair<Object> cacheSerializer =
        fromSerializer(redisSerializer);

    return builder -> {
      for (SharedCacheTtl sharedCacheTtl : sharedCacheTtls) {
        log.info(
            "Redis: Configuring shared cache {} with TTL {}",
            sharedCacheTtl.name(),
            sharedCacheTtl.ttl());
        builder =
            builder.withCacheConfiguration(
                sharedCacheTtl.name(),
                RedisCacheConfiguration.defaultCacheConfig()
                    .serializeValuesWith(cacheSerializer)
                    .entryTtl(sharedCacheTtl.ttl()));
      }
      log.info("Redis: Configuring cache manager");
      builder.cacheDefaults().serializeValuesWith(cacheSerializer);
    };
  }

  @Bean
  RedisSerializer<Object> redisSerializer(
      List<KryoSerializationConfigurer> serializationConfigurers) {
    return new KryoRedisSerializer(serializationConfigurers);
  }

  @Bean
  RedisDataGrid redisDataGrid(
      List<LocalCacheReplica> localCacheReplicas,
      RedisTemplate<String, Object> redisTemplate,
      RedisMessageListenerContainer redisMessageListenerContainer,
      StringRedisTemplate stringRedisTemplate,
      RedisConnectionFactory redisConnectionFactory,
      FencingTokenService fencingTokenService,
      LockManager<RedisClusterLock> lockManager) {
    log.info("Redis: Creating RedisDataGrid bean");

    RedisCacheMessagePublisher redisCacheMessagePublisher =
        new RedisCacheMessagePublisher(
            new RedisPubSubTopic<>(
                redisTemplate, redisMessageListenerContainer, INVALIDATION_TOPIC));

    Map<String, KeyValueCache<?, ?>> cacheMap = new HashMap<>();

    localCacheReplicas.forEach(
        localCacheReplica -> {
          log.info("Redis: Configuring dataGrid keyValue cache {}", localCacheReplica.name());

          RedisKeyValueCache<?, ?> redisCache =
              new RedisKeyValueCache<>(redisTemplate.boundHashOps(localCacheReplica.name()));

          cacheMap.put(
              localCacheReplica.name(),
              new CaffeineRedisBackedKeyValueCache<>(
                  localCacheReplica, redisCache, redisCacheMessagePublisher));
        });

    return new RedisDataGrid(
        redisTemplate,
        redisMessageListenerContainer,
        stringRedisTemplate,
        redisConnectionFactory,
        fencingTokenService,
        lockManager,
        Collections.unmodifiableMap(cacheMap));
  }

  @Bean
  public FencingTokenService fencingTokenService(StringRedisTemplate stringRedisTemplate) {
    log.info("Redis: Creating FencingTokenService bean");
    return new FencingTokenService(stringRedisTemplate);
  }

  @Bean
  public RedisLockManager redisLockManager() {
    log.info("Redis: Creating LockManager bean");
    return new RedisLockManager();
  }

  // Can not use Protostuff as no Java record support
  // https://github.com/protostuff/protostuff/issues/313
  // Maybe shift the Kafka serialization to use Kryo too so we don't have too much proliferation of
  // serialization tech
  private static class KryoRedisSerializer implements RedisSerializer<Object> {
    private KryoRedisSerializer(Iterable<KryoSerializationConfigurer> serializationConfigurers) {
      if (log.isDebugEnabled()) {
        log.debug(
            "Redis: configuring Kryo serialization with {}",
            StreamSupport.stream(serializationConfigurers.spliterator(), false)
                .map(c -> c.getClass().getSimpleName())
                .toList());
      }

      this.kryoThreadLocal =
          ThreadLocal.withInitial(
              () -> {
                Kryo kryo = new Kryo();
                // Use JDK serializer for these Guava classes which use writeReplace() and cannot
                // use
                // MapSerializer etc
                kryo.addDefaultSerializer(ImmutableMap.class, JavaSerializer.class);
                kryo.addDefaultSerializer(ImmutableList.class, JavaSerializer.class);
                kryo.addDefaultSerializer(ImmutableSet.class, JavaSerializer.class);
                kryo.setRegistrationRequired(false);
                kryo.setInstantiatorStrategy(
                    new DefaultInstantiatorStrategy(new StdInstantiatorStrategy()));
                for (KryoSerializationConfigurer serializationConfigurer :
                    serializationConfigurers) {
                  serializationConfigurer.configure(
                      new KryoSerializationConfigurer.SerializationConfig(kryo));
                }
                log.debug("Redis: Created Kryo instance, should only see this once per thread");
                return kryo;
              });
    }

    // Use ThreadLocal to reuse Kryo instances per thread
    private final ThreadLocal<Kryo> kryoThreadLocal;

    private Kryo redisKryo() {
      return kryoThreadLocal.get();
    }

    /**
     * Provides serialization for Redis
     *
     * @param value object to serialize. Should not be null
     * @return non-null byte array representing value, or throws exception if null
     */
    @Override
    public byte[] serialize(@Nullable Object value) throws SerializationException {
      if (value == null) {
        throw new SerializationException("Cannot serialize null value");
      }
      Output output = new Output(1024, -1);

      redisKryo().writeClassAndObject(output, value);
      output.flush();
      // return only the bytes written and not the whole buffer
      return output.toBytes();
    }

    @Override
    public @Nullable Object deserialize(byte @Nullable [] bytes) throws SerializationException {
      if (bytes == null) {
        log.warn("Redis: Unexpectedly deserializing null bytes");
        return null;
        // TODO: SXSD-10262 We should not get here so should throw an exception below
        // Kryo serializes null as a specific entry, so we will always have bytes
        //   throw new SerializationException("Must have bytes to deserialize");
      }
      Input input = new Input(bytes);
      return redisKryo().readClassAndObject(input);
    }
  }
}
