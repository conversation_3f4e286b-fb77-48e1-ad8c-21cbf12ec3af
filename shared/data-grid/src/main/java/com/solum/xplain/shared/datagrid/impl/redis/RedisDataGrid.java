package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.AtomicCounter;
import com.solum.xplain.shared.datagrid.ClusterLock;
import com.solum.xplain.shared.datagrid.DataGrid;
import com.solum.xplain.shared.datagrid.FencingTokenService;
import com.solum.xplain.shared.datagrid.KeyValueCache;
import com.solum.xplain.shared.datagrid.LockManager;
import com.solum.xplain.shared.datagrid.ManagedClusterLock;
import com.solum.xplain.shared.datagrid.PubSubTopic;
import com.solum.xplain.shared.datagrid.ValueSet;
import jakarta.annotation.PostConstruct;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@NullMarked
public class RedisDataGrid implements DataGrid {
  public static final String LOCK_PREFIX = "LOCK__";
  private final RedisTemplate redisTemplate;
  private final RedisMessageListenerContainer redisMessageListenerContainer;
  private final StringRedisTemplate stringRedisTemplate;
  private final RedisClusterMappings enhancedConnectionFactory;
  private final FencingTokenService fencingTokenService;
  private final LockManager<RedisClusterLock> lockManager;
  private final Map<String, KeyValueCache<?, ?>> cacheMap;

  public RedisDataGrid(
      RedisTemplate redisTemplate,
      RedisMessageListenerContainer redisMessageListenerContainer,
      StringRedisTemplate stringRedisTemplate,
      RedisConnectionFactory redisConnectionFactory,
      FencingTokenService fencingTokenService,
      LockManager<RedisClusterLock> lockManager,
      Map<String, KeyValueCache<?, ?>> cacheMap) {

    this.redisTemplate = redisTemplate;
    this.redisMessageListenerContainer = redisMessageListenerContainer;
    this.stringRedisTemplate = stringRedisTemplate;
    this.fencingTokenService = fencingTokenService;
    this.lockManager = lockManager;
    this.cacheMap = cacheMap;

    // Create the enhanced connection factory for optimized lock key management
    this.enhancedConnectionFactory = new RedisClusterMappings(redisConnectionFactory);
  }

  @PostConstruct
  void init() {
    log.debug("RedisDataGrid initialized");
  }

  @Override
  public <K, V> KeyValueCache<K, V> getKeyValueCache(String name) {
    return cacheMap.get(name) == null
        ? new RedisKeyValueCache<>(redisTemplate.boundHashOps("h_" + name))
        : (KeyValueCache<K, V>) cacheMap.get(name);
  }

  @Override
  public <V> ValueSet<V> getValueSet(String name) {
    return new RedisValueSet<>(redisTemplate.boundSetOps("s_" + name));
  }

  @Override
  public AtomicCounter getAtomicCounter(String name) {
    return new RedisAtomicCounter(stringRedisTemplate.boundValueOps("v_" + name));
  }

  @Override
  public <T> PubSubTopic<T> getPubSubTopic(String name) {
    return new RedisPubSubTopic<>(redisTemplate, redisMessageListenerContainer, "t_" + name);
  }

  @Override
  public ClusterLock getClusterLock(String name) {
    RedisClusterLock rawLock =
        new RedisClusterLock(enhancedConnectionFactory, LOCK_PREFIX + name, fencingTokenService);

    return new ManagedClusterLock<>(rawLock, lockManager);
  }
}
