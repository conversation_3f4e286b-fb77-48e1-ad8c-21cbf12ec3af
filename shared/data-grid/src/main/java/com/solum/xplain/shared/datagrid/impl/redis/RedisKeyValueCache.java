package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.KeyValueCache;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import redis.clients.jedis.JedisCluster;

@RequiredArgsConstructor
class RedisKeyValueCache<K, V> implements KeyValueCache<K, V> {
  private final BoundHashOperations<String, K, V> hashOps;

  @Override
  public V get(K key) {
    return hashOps.get(key);
  }

  boolean exists(K key) {
    return Boolean.TRUE.equals(hashOps.hasKey(key));
  }

  @Override
  public void set(K key, V value) {
    hashOps.put(key, value);
  }

  @Override
  public void set(K key, V value, Duration ttl) {
    if (key instanceof String keyString) {
      set(key, value);
      // Requires Redis 7.4+ - not yet implemented in Valkey
      // https://github.com/valkey-io/valkey/issues/640
      // Send low level command: HEXPIRE <hashname> <ttl-seconds> FIELDS 1 <key>
      hashOps
          .getOperations()
          .execute((RedisCallback<Object>) connection -> hExpire(connection, keyString, ttl));
    } else {
      throw new IllegalArgumentException("Redis: Key must be a string when set with a TTL");
    }
  }

  void setKeyExpiry(K key, Duration ttl) {
    if (!(key instanceof String keyString)) {
      throw new IllegalArgumentException("Redis: Key must be a string when set with a TTL");
    }
    hashOps
        .getOperations()
        .execute((RedisCallback<Object>) connection -> hExpire(connection, keyString, ttl));
  }

  private List<Long> hExpire(RedisConnection connection, String key, Duration ttl) {
    return ((JedisCluster) connection.getNativeConnection())
        .hexpire(hashOps.getKey(), ttl.getSeconds(), key);
  }

  @Override
  public void merge(K key, V value, BiFunction<V, V, V> remappingFunction) {
    hashOps
        .getOperations()
        .execute(new MergeOperation<>(hashOps.getKey(), key, value, remappingFunction));
  }

  @Override
  public void merge(K key, V value, BiFunction<V, V, V> remappingFunction, Duration ttl) {
    if (!(key instanceof String keyString)) {
      throw new IllegalArgumentException("Redis: Key must be a string when set with a TTL");
    }

    V result =
        hashOps
            .getOperations()
            .execute(new MergeOperation<>(hashOps.getKey(), key, value, remappingFunction));

    // Apply TTL *only* if result is non-null (i.e., not deleted)
    if (result != null) {
      hashOps
          .getOperations()
          .execute((RedisCallback<Object>) connection -> hExpire(connection, keyString, ttl));
    }
  }

  @Override
  public void setAll(Map<K, V> values) {
    hashOps.putAll(values);
  }

  @Override
  public Collection<K> keySet() {
    return hashOps.keys();
  }

  @Override
  public Collection<V> values() {
    return hashOps.values();
  }

  @Override
  public Collection<Map.Entry<K, V>> entrySet() {
    return hashOps.entries().entrySet();
  }

  @Override
  public void delete(K key) {
    hashOps.delete(key);
  }

  @Override
  public void removeAll(Predicate<V> predicate) {
    Map<K, V> entries = hashOps.entries();
    for (Map.Entry<K, V> entry : entries.entrySet()) {
      if (predicate.test(entry.getValue())) {
        hashOps.delete(entry.getKey());
      }
    }
  }

  @RequiredArgsConstructor
  static class MergeOperation<K, V> implements SessionCallback<V> {
    private final String hashKey;
    private final K key;
    private final V newValue;
    private final BiFunction<V, V, V> remappingFunction;

    @Override
    public <K1, V1> V execute(RedisOperations<K1, V1> operations) throws DataAccessException {
      // See https://redis.io/docs/latest/develop/interact/transactions/
      operations.watch((K1) hashKey);
      BoundHashOperations<K1, K, V> hashOpsTx = operations.boundHashOps((K1) hashKey);
      V oldValue = hashOpsTx.get(key);
      operations.multi();
      V newValue =
          (oldValue == null) ? this.newValue : remappingFunction.apply(oldValue, this.newValue);
      if (newValue == null) {
        hashOpsTx.delete(key);
      } else {
        hashOpsTx.put(key, newValue);
      }
      operations.exec();
      return newValue;
    }
  }
}
