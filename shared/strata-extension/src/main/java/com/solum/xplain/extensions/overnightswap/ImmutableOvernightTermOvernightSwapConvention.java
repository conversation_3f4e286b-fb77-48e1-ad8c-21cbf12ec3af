package com.solum.xplain.extensions.overnightswap;

import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.opengamma.strata.product.TradeInfo;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.Swap;
import com.opengamma.strata.product.swap.SwapLeg;
import com.opengamma.strata.product.swap.SwapTrade;
import com.opengamma.strata.product.swap.type.OvernightRateSwapLegConvention;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.time.LocalDate;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

@EqualsAndHashCode
@Builder
@NullMarked
@Getter
public class ImmutableOvernightTermOvernightSwapConvention
    implements OvernightTermOvernightSwapConvention {

  private final String name;
  private final TermOvernightSwapLegConvention termOvernightLeg;
  private final OvernightRateSwapLegConvention overnightLeg;
  private final DaysAdjustment spotDateOffset;

  public static ExtendedEnum<OvernightTermOvernightSwapConvention> extendedEnum() {
    return ExtendedEnum.of(OvernightTermOvernightSwapConvention.class);
  }

  public static OvernightTermOvernightSwapConvention of(String uniqueName) {
    return extendedEnum().lookup(uniqueName);
  }

  public OvernightTermIndex getIndex() {
    return termOvernightLeg.getIndex();
  }

  @Override
  public SwapTrade toTrade(
      TradeInfo tradeInfo,
      LocalDate startDate,
      LocalDate endDate,
      BuySell buySell,
      double notional,
      double spread) {
    SwapLeg leg1 =
        termOvernightLeg.toLeg(
            startDate, endDate, PayReceive.ofPay(buySell.isBuy()), notional, spread);
    SwapLeg leg2 =
        overnightLeg.toLeg(startDate, endDate, PayReceive.ofPay(buySell.isSell()), notional);
    return SwapTrade.builder().info(tradeInfo).product(Swap.of(leg1, leg2)).build();
  }
}
