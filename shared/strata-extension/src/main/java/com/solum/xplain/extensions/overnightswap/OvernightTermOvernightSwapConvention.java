package com.solum.xplain.extensions.overnightswap;

import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.TradeInfo;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.swap.SwapTrade;
import com.opengamma.strata.product.swap.type.OvernightRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.SingleCurrencySwapConvention;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.time.LocalDate;
import org.joda.convert.FromString;

public interface OvernightTermOvernightSwapConvention extends SingleCurrencySwapConvention, Named {

  @FromString
  static OvernightTermOvernightSwapConvention of(String uniqueName) {
    return extendedEnum().lookup(uniqueName);
  }

  static ExtendedEnum<OvernightTermOvernightSwapConvention> extendedEnum() {
    return ExtendedEnum.of(OvernightTermOvernightSwapConvention.class);
  }

  SwapTrade toTrade(
      TradeInfo tradeInfo,
      LocalDate startDate,
      LocalDate endDate,
      BuySell buySell,
      double notional,
      double spread);

  String getName();

  TermOvernightSwapLegConvention getTermOvernightLeg();

  OvernightRateSwapLegConvention getOvernightLeg();

  OvernightTermIndex getIndex();

  DaysAdjustment getSpotDateOffset();
}
