package com.solum.xplain.extensions.overnightswap;

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarIds;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.RollConventions;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.FixingRelativeTo;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.type.OvernightRateSwapLegConvention;
import com.solum.xplain.extensions.index.ExtendedOvernightIndices;
import com.solum.xplain.extensions.index.OvernightTermIndex;

public final class OvernightTermOvernightSwapConventions {
  public static final OvernightTermOvernightSwapConvention USD_SOFR_OIS_SOFR_3M;
  public static final OvernightTermOvernightSwapConvention USD_SOFR_OIS_SOFR_6M;

  static {
    USD_SOFR_OIS_SOFR_3M =
        makeConvention(
            "USD-SOFR-OIS-SOFR-3M",
            ExtendedOvernightIndices.USD_SOFR_3M,
            Frequency.P3M,
            Frequency.P3M);
    USD_SOFR_OIS_SOFR_6M =
        makeConvention(
            "USD-SOFR-OIS-SOFR-6M",
            ExtendedOvernightIndices.USD_SOFR_6M,
            Frequency.P6M,
            Frequency.P6M);
  }

  private OvernightTermOvernightSwapConventions() {}

  private static OvernightTermOvernightSwapConvention makeConvention(
      String name,
      OvernightTermIndex index,
      Frequency accrualFrequency,
      Frequency paymentFrequency) {
    return ImmutableOvernightTermOvernightSwapConvention.builder()
        .name(name)
        .termOvernightLeg(
            TermOvernightSwapLegConvention.applyDefaults(TermOvernightSwapLegConvention.builder())
                .index(index)
                .currency(Currency.of("USD"))
                .accrualFrequency(accrualFrequency)
                .dayCount(DayCounts.ACT_360)
                .accrualBusinessDayAdjustment(
                    BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
                .compoundingMethod(CompoundingMethod.NONE)
                .accrualMethod(OvernightAccrualMethod.COMPOUNDED)
                .rollConvention(RollConventions.EOM)
                .paymentFrequency(paymentFrequency)
                .stubConvention(StubConvention.SMART_INITIAL)
                .startDateBusinessDayAdjustment(
                    BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
                .endDateBusinessDayAdjustment(
                    BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
                .rateCutOffDays(1)
                .paymentDateOffset(DaysAdjustment.ofBusinessDays(2, HolidayCalendarIds.USGS))
                .fixingDateOffset(DaysAdjustment.ofBusinessDays(-2, HolidayCalendarIds.USGS))
                .fixingRelativeTo(FixingRelativeTo.PERIOD_START)
                .build())
        .overnightLeg(
            OvernightRateSwapLegConvention.builder()
                .index(OvernightIndex.of("USD-SOFR"))
                .currency(Currency.of("USD"))
                .accrualFrequency(accrualFrequency)
                .dayCount(DayCounts.ACT_360)
                .accrualBusinessDayAdjustment(
                    BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
                .compoundingMethod(CompoundingMethod.NONE)
                .accrualMethod(OvernightAccrualMethod.COMPOUNDED)
                .rollConvention(RollConventions.EOM)
                .paymentFrequency(paymentFrequency)
                .stubConvention(StubConvention.SMART_INITIAL)
                .startDateBusinessDayAdjustment(
                    BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
                .endDateBusinessDayAdjustment(
                    BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
                .rateCutOffDays(1)
                .paymentDateOffset(DaysAdjustment.ofBusinessDays(2, HolidayCalendarIds.USGS))
                .notionalExchange(false)
                .build())
        .spotDateOffset(DaysAdjustment.ofBusinessDays(2, HolidayCalendarIds.USGS))
        .build();
  }
}
