package com.solum.xplain.extensions.overnightswap;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.ObservableId;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.CurveNode;
import com.opengamma.strata.market.curve.CurveNodeDate;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.market.param.DatedParameterMetadata;
import com.opengamma.strata.market.param.TenorDateParameterMetadata;
import com.opengamma.strata.product.ResolvedTrade;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.swap.ResolvedSwap;
import com.opengamma.strata.product.swap.SwapTrade;
import java.time.LocalDate;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

@NullMarked
@Builder
public class OvernightTermOvernightSwapCurveNode implements CurveNode {

  private final OvernightTermOvernightSwapTradeTemplate template;
  @Getter private final String label;
  private final ObservableId rateId;
  private final double additionalSpread;
  private final CurveNodeDate date;
  @Getter private final CurveNodeDateOrder dateOrder;

  @Override
  public Set<ObservableId> requirements() {
    return Set.of(rateId);
  }

  @Override
  public LocalDate date(LocalDate valuationDate, ReferenceData refData) {
    return this.date.calculate(
        () -> this.calculateEnd(valuationDate, refData),
        () -> this.calculateLastFixingDate(valuationDate, refData));
  }

  private LocalDate calculateLastFixingDate(LocalDate valuationDate, ReferenceData refData) {
    SwapTrade trade = this.template.createTrade(valuationDate, BuySell.BUY, 0.0, 0.0, refData);
    ResolvedSwap resolvedTrade = trade.getProduct().resolve(refData);
    return resolvedTrade.getStartDate();
  }

  private LocalDate calculateEnd(LocalDate valuationDate, ReferenceData refData) {
    SwapTrade trade = this.template.createTrade(valuationDate, BuySell.BUY, 0.0, 0.0, refData);
    ResolvedSwap resolvedTrade = trade.getProduct().resolve(refData);
    return resolvedTrade.getEndDate();
  }

  @Override
  public DatedParameterMetadata metadata(LocalDate valuationDate, ReferenceData refData) {
    LocalDate nodeDate = date(valuationDate, refData);
    return TenorDateParameterMetadata.of(nodeDate, template.tenor(), label);
  }

  public SwapTrade trade(double quantity, MarketData marketData, ReferenceData refData) {
    double fixedRate = marketData.getValue(this.rateId) + this.additionalSpread;
    BuySell buySell = quantity > (double) 0.0F ? BuySell.SELL : BuySell.BUY;
    return this.template.createTrade(
        marketData.getValuationDate(), buySell, Math.abs(quantity), fixedRate, refData);
  }

  @Override
  public ResolvedTrade resolvedTrade(
      double quantity, MarketData marketData, ReferenceData refData) {
    return this.trade(quantity, marketData, refData).resolve(refData);
  }

  @Override
  public double initialGuess(MarketData marketData, ValueType valueType) {
    return marketData.getValue(this.rateId);
  }
}
