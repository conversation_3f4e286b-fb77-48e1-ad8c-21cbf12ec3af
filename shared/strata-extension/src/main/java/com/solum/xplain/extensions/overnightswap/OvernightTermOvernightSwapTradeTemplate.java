package com.solum.xplain.extensions.overnightswap;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.product.TradeTemplate;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.swap.SwapTrade;
import com.opengamma.strata.product.swap.type.SingleCurrencySwapConvention;
import java.time.LocalDate;

public record OvernightTermOvernightSwapTradeTemplate(
    Tenor tenor, SingleCurrencySwapConvention convention) implements TradeTemplate {

  public static OvernightTermOvernightSwapTradeTemplate of(
      Tenor p, SingleCurrencySwapConvention of) {
    return new OvernightTermOvernightSwapTradeTemplate(p, of);
  }

  public SwapTrade createTrade(
      LocalDate valuationDate,
      BuySell buySell,
      double abs,
      double fixedRate,
      ReferenceData refData) {
    return convention.createTrade(valuationDate, tenor, buySell, abs, fixedRate, refData);
  }
}
