package com.solum.xplain.extensions.overnightswap;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.PeriodicSchedule;
import com.opengamma.strata.basics.schedule.RollConvention;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.FixingRelativeTo;
import com.opengamma.strata.product.swap.NegativeRateMethod;
import com.opengamma.strata.product.swap.NotionalSchedule;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.PaymentSchedule;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.opengamma.strata.product.swap.type.SwapLegConvention;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.time.LocalDate;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jspecify.annotations.NullMarked;

@EqualsAndHashCode
@Builder
@Data
@NullMarked
public class TermOvernightSwapLegConvention implements SwapLegConvention {

  public static TermOvernightSwapLegConventionBuilder applyDefaults(
      TermOvernightSwapLegConventionBuilder builder) {
    return builder.notionalExchange(false);
  }

  private final OvernightTermIndex index;
  private final Currency currency;
  private final Frequency accrualFrequency;
  private final DayCount dayCount;
  private final BusinessDayAdjustment accrualBusinessDayAdjustment;
  private final CompoundingMethod compoundingMethod;
  private final OvernightAccrualMethod accrualMethod;
  private final RollConvention rollConvention;
  private final Frequency paymentFrequency;
  private final StubConvention stubConvention;
  private final BusinessDayAdjustment startDateBusinessDayAdjustment;
  private final BusinessDayAdjustment endDateBusinessDayAdjustment;
  private final int rateCutOffDays;
  private final DaysAdjustment paymentDateOffset;
  private final DaysAdjustment fixingDateOffset;
  private final FixingRelativeTo fixingRelativeTo;
  private final boolean notionalExchange;

  public RateCalculationSwapLeg toLeg(
      LocalDate startDate,
      LocalDate endDate,
      PayReceive payReceive,
      double notional,
      double spread) {
    return RateCalculationSwapLeg.builder()
        .payReceive(payReceive)
        .accrualSchedule(
            PeriodicSchedule.builder()
                .startDate(startDate)
                .endDate(endDate)
                .frequency(this.getAccrualFrequency())
                .businessDayAdjustment(this.getAccrualBusinessDayAdjustment())
                .startDateBusinessDayAdjustment(this.startDateBusinessDayAdjustment)
                .endDateBusinessDayAdjustment(this.endDateBusinessDayAdjustment)
                .stubConvention(this.stubConvention)
                .rollConvention(this.rollConvention)
                .build())
        .paymentSchedule(
            PaymentSchedule.builder()
                .paymentFrequency(this.getPaymentFrequency())
                .paymentDateOffset(this.getPaymentDateOffset())
                .compoundingMethod(this.getCompoundingMethod())
                .build())
        .notionalSchedule(
            NotionalSchedule.builder()
                .currency(this.getCurrency())
                .finalExchange(this.notionalExchange)
                .initialExchange(this.notionalExchange)
                .amount(ValueSchedule.of(notional))
                .build())
        .calculation(
            OvernightRateCalculation.builder()
                .dayCount(this.getDayCount())
                .index(this.getIndex())
                .spread(ValueSchedule.of(spread))
                .negativeRateMethod(NegativeRateMethod.ALLOW_NEGATIVE)
                .accrualMethod(this.getAccrualMethod())
                .rateCutOffDays(this.getRateCutOffDays())
                .build())
        .build();
  }
}
