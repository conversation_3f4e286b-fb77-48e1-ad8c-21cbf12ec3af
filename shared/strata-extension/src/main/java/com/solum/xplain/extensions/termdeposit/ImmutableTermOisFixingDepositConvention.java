package com.solum.xplain.extensions.termdeposit;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.BusinessDayConventions;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.product.TradeInfo;
import com.opengamma.strata.product.common.BuySell;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.time.LocalDate;
import java.time.Period;
import java.util.Optional;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

@EqualsAndHashCode
@Builder
@NullMarked
public class ImmutableTermOisFixingDepositConvention implements TermOisFixingDepositConvention {

  @Getter private final OvernightTermIndex index;
  private final @Nullable String name;
  private final @Nullable Currency currency;
  private final @Nullable DayCount dayCount;
  @Getter private final DaysAdjustment paymentDateOffset;
  private final @Nullable BusinessDayAdjustment businessDayAdjustment;
  private final @Nullable DaysAdjustment fixingDateOffset;

  public static ImmutableTermOisFixingDepositConventionBuilder of(OvernightTermIndex index) {
    return ImmutableTermOisFixingDepositConvention.builder().index(index);
  }

  @Override
  public String getName() {
    return this.name != null ? this.name : this.index.getName();
  }

  public Currency getCurrency() {
    return this.currency != null ? this.currency : this.index.getCurrency();
  }

  public DayCount getDayCount() {
    return this.dayCount != null ? this.dayCount : this.index.getDayCount();
  }

  public BusinessDayAdjustment getBusinessDayAdjustment() {
    return this.businessDayAdjustment != null
        ? this.businessDayAdjustment
        : BusinessDayAdjustment.of(
            BusinessDayConventions.MODIFIED_FOLLOWING, this.index.getFixingCalendar());
  }

  public DaysAdjustment getFixingDateOffset() {
    return this.fixingDateOffset != null ? this.fixingDateOffset : this.index.getFixingDateOffset();
  }

  @Override
  public TermOisFixingDepositTrade createTrade(
      LocalDate tradeDate,
      Period depositPeriod,
      BuySell buySell,
      double notional,
      double fixedRate,
      ReferenceData refData) {
    LocalDate startDate = this.calculateSpotDateFromTradeDate(tradeDate, refData);
    LocalDate endDate = startDate.plus(depositPeriod);
    return this.toTrade(tradeDate, startDate, endDate, buySell, notional, fixedRate);
  }

  @Override
  public TermOisFixingDepositTrade toTrade(
      TradeInfo tradeInfo,
      LocalDate startDate,
      LocalDate endDate,
      BuySell buySell,
      double notional,
      double fixedRate) {
    Optional<LocalDate> tradeDate = tradeInfo.getTradeDate();
    tradeDate.ifPresent(
        localDate -> ArgChecker.inOrderOrEqual(localDate, startDate, "tradeDate", "startDate"));
    return TermOisFixingDepositTrade.builder()
        .info(tradeInfo)
        .product(
            TermOisFixingDeposit.builder()
                .buySell(buySell)
                .currency(this.getCurrency())
                .notional(notional)
                .startDate(startDate)
                .endDate(endDate)
                .fixedRate(fixedRate)
                .index(this.index)
                .fixingDateOffset(this.getFixingDateOffset())
                .dayCount(this.getDayCount())
                .build())
        .build();
  }
}
