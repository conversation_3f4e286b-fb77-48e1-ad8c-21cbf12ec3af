package com.solum.xplain.extensions.termdeposit;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.OvernightIndexObservation;
import com.opengamma.strata.product.ResolvedProduct;
import com.opengamma.strata.product.rate.OvernightRateComputation;
import lombok.Builder;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

@Builder
@Getter
@NullMarked
public class ResolvedTermOisFixingDeposit implements ResolvedProduct {

  private final Currency currency;
  private final double notional;
  private final OvernightIndexObservation observation;
  private final OvernightRateComputation floatingRate;
  private final double fixedRate;
}
