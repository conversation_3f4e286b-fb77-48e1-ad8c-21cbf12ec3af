package com.solum.xplain.extensions.termdeposit;

import com.opengamma.strata.product.ResolvedTrade;
import com.opengamma.strata.product.TradeInfo;
import lombok.Builder;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

@Builder
@Getter
@NullMarked
public class ResolvedTermOisFixingDepositTrade implements ResolvedTrade {

  private final TradeInfo info;
  private final ResolvedTermOisFixingDeposit product;

  public static ResolvedTermOisFixingDepositTrade of(
      TradeInfo info, ResolvedTermOisFixingDeposit product) {
    return new ResolvedTermOisFixingDepositTrade(info, product);
  }
}
