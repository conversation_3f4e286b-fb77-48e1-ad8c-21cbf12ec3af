package com.solum.xplain.extensions.termdeposit;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.TradeConvention;
import com.opengamma.strata.product.TradeInfo;
import com.opengamma.strata.product.common.BuySell;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.time.LocalDate;
import java.time.Period;
import org.joda.convert.FromString;

public interface TermOisFixingDepositConvention extends TradeConvention, Named {

  @FromString
  static TermOisFixingDepositConvention of(String uniqueName) {
    return extendedEnum().lookup(uniqueName);
  }

  static ExtendedEnum<TermOisFixingDepositConvention> extendedEnum() {
    return ExtendedEnum.of(TermOisFixingDepositConvention.class);
  }

  DaysAdjustment getPaymentDateOffset();

  OvernightTermIndex getIndex();

  TermOisFixingDepositTrade createTrade(
      LocalDate tradeDate,
      Period depositPeriod,
      BuySell buySell,
      double notional,
      double fixedRate,
      ReferenceData refData);

  default TermOisFixingDepositTrade toTrade(
      LocalDate tradeDate,
      LocalDate startDate,
      LocalDate endDate,
      BuySell buySell,
      double notional,
      double fixedRate) {
    TradeInfo tradeInfo = TradeInfo.of(tradeDate);
    return this.toTrade(tradeInfo, startDate, endDate, buySell, notional, fixedRate);
  }

  TermOisFixingDepositTrade toTrade(
      TradeInfo tradeInfo,
      LocalDate startDate,
      LocalDate endDate,
      BuySell buySell,
      double notional,
      double fixedRate);

  default LocalDate calculateSpotDateFromTradeDate(LocalDate tradeDate, ReferenceData refData) {
    return this.getPaymentDateOffset().adjust(tradeDate, refData);
  }
}
