package com.solum.xplain.extensions.termdeposit;

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING;
import static com.opengamma.strata.basics.date.DayCounts.ACT_360;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarIds;
import com.solum.xplain.extensions.index.ExtendedOvernightIndices;

public final class TermOisFixingDepositConventions {
  public static final ImmutableTermOisFixingDepositConvention USD_SOFR_3M_TERM_OIS;
  public static final ImmutableTermOisFixingDepositConvention USD_SOFR_6M_TERM_OIS;

  private TermOisFixingDepositConventions() {}

  static {
    USD_SOFR_3M_TERM_OIS =
        ImmutableTermOisFixingDepositConvention.of(ExtendedOvernightIndices.USD_SOFR_3M)
            .name("USD-SOFR-3M")
            .currency(Currency.USD)
            .dayCount(ACT_360)
            .paymentDateOffset(DaysAdjustment.ofBusinessDays(2, HolidayCalendarIds.USGS))
            .businessDayAdjustment(
                BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
            .fixingDateOffset(DaysAdjustment.ofBusinessDays(-2, HolidayCalendarIds.USGS))
            .build();
    USD_SOFR_6M_TERM_OIS =
        ImmutableTermOisFixingDepositConvention.of(ExtendedOvernightIndices.USD_SOFR_6M)
            .name("USD-SOFR-6M")
            .currency(Currency.USD)
            .dayCount(ACT_360)
            .paymentDateOffset(DaysAdjustment.ofBusinessDays(2, HolidayCalendarIds.USGS))
            .businessDayAdjustment(
                BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
            .fixingDateOffset(DaysAdjustment.ofBusinessDays(-2, HolidayCalendarIds.USGS))
            .build();
  }
}
