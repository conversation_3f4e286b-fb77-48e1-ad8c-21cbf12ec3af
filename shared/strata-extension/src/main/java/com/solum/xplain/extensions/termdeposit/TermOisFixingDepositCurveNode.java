package com.solum.xplain.extensions.termdeposit;

import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.ObservableId;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.CurveNode;
import com.opengamma.strata.market.curve.CurveNodeDate;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.market.param.DatedParameterMetadata;
import com.opengamma.strata.market.param.LabelDateParameterMetadata;
import com.opengamma.strata.market.param.TenorDateParameterMetadata;
import com.opengamma.strata.product.common.BuySell;
import java.time.LocalDate;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

@Builder
@Getter
@NullMarked
public class TermOisFixingDepositCurveNode implements CurveNode {

  private final TermOisFixingDepositNodeTemplate template;
  private final ObservableId rateId;
  private final double additionalSpread;
  private final String label;
  private final CurveNodeDate date = CurveNodeDate.END;
  private final CurveNodeDateOrder dateOrder = CurveNodeDateOrder.DEFAULT;

  public Set<ObservableId> requirements() {
    return ImmutableSet.of(this.rateId);
  }

  @Override
  public LocalDate date(LocalDate valuationDate, ReferenceData refData) {
    return this.date.calculate(
        () -> this.calculateEnd(valuationDate, refData),
        () -> this.calculateLastFixingDate(valuationDate, refData));
  }

  @Override
  public DatedParameterMetadata metadata(LocalDate valuationDate, ReferenceData refData) {
    LocalDate nodeDate = this.date(valuationDate, refData);
    if (this.date.isFixed()) {
      return LabelDateParameterMetadata.of(nodeDate, this.label);
    } else {
      Tenor tenor = Tenor.of(this.template.depositPeriod());
      return TenorDateParameterMetadata.of(nodeDate, tenor, this.label);
    }
  }

  @Override
  public TermOisFixingDepositTrade trade(
      double quantity, MarketData marketData, ReferenceData refData) {
    double fixedRate = marketData.getValue(this.rateId) + this.additionalSpread;
    BuySell buySell = quantity > 0.0 ? BuySell.BUY : BuySell.SELL;
    return this.template.createTrade(
        marketData.getValuationDate(), buySell, Math.abs(quantity), fixedRate, refData);
  }

  @Override
  public ResolvedTermOisFixingDepositTrade resolvedTrade(
      double quantity, MarketData marketData, ReferenceData refData) {
    return this.trade(quantity, marketData, refData).resolve(refData);
  }

  public double initialGuess(MarketData marketData, ValueType valueType) {
    if (ValueType.ZERO_RATE.equals(valueType) || ValueType.FORWARD_RATE.equals(valueType)) {
      return marketData.getValue(this.rateId);
    } else if (ValueType.DISCOUNT_FACTOR.equals(valueType)) {
      double approximateMaturity = this.template.depositPeriod().toTotalMonths() / 12.0;
      return Math.exp(-approximateMaturity * marketData.getValue(this.rateId));
    } else {
      return 0.0;
    }
  }

  private LocalDate calculateEnd(LocalDate valuationDate, ReferenceData refData) {
    // use a temporary trade to resolve the end date
    TermOisFixingDepositTrade trade =
        this.template.createTrade(valuationDate, BuySell.BUY, 0.0, 0.0, refData);
    ResolvedTermOisFixingDeposit deposit = trade.getProduct().resolve(refData);
    return deposit.getObservation().getMaturityDate();
  }

  private LocalDate calculateLastFixingDate(LocalDate valuationDate, ReferenceData refData) {
    // use a temporary trade to resolve the fixing date
    TermOisFixingDepositTrade trade =
        this.template.createTrade(valuationDate, BuySell.BUY, 0.0, 0.0, refData);
    ResolvedTermOisFixingDeposit deposit = trade.getProduct().resolve(refData);
    return deposit.getObservation().getFixingDate();
  }
}
