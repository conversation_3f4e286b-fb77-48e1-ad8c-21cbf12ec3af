package com.solum.xplain.extensions.termdeposit;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.product.TradeTemplate;
import com.opengamma.strata.product.common.BuySell;
import java.time.LocalDate;
import java.time.Period;

public record TermOisFixingDepositNodeTemplate(
    Period depositPeriod, TermOisFixingDepositConvention convention) implements TradeTemplate {

  public TermOisFixingDepositTrade createTrade(
      LocalDate tradeDate,
      BuySell buySell,
      double notional,
      double fixedRate,
      ReferenceData refData) {
    return this.convention.createTrade(
        tradeDate, depositPeriod, buySell, notional, fixedRate, refData);
  }
}
