package com.solum.xplain.extensions.termdeposit;

import com.opengamma.strata.basics.index.OvernightIndexObservation;
import com.opengamma.strata.market.sensitivity.PointSensitivities;
import com.opengamma.strata.pricer.DiscountFactors;
import com.opengamma.strata.pricer.rate.DiscountOvernightIndexRates;
import com.opengamma.strata.pricer.rate.RatesProvider;
import java.time.LocalDate;

public class TermOisFixingDepositPricer {

  public double parSpread(ResolvedTermOisFixingDeposit deposit, RatesProvider provider) {
    return this.forwardRate(deposit, provider) - deposit.getFixedRate();
  }

  public PointSensitivities parSpreadSensitivity(
      ResolvedTermOisFixingDeposit product, RatesProvider provider) {
    DiscountOvernightIndexRates rates =
        (DiscountOvernightIndexRates)
            provider.overnightIndexRates(product.getFloatingRate().getIndex());
    return rates.rateIgnoringFixingsPointSensitivity(product.getObservation()).build();
  }

  private double forwardRate(ResolvedTermOisFixingDeposit product, RatesProvider provider) {
    DiscountOvernightIndexRates rates =
        (DiscountOvernightIndexRates)
            provider.overnightIndexRates(product.getFloatingRate().getIndex());
    return rateIgnoringFixings(product.getObservation(), rates.getDiscountFactors());
  }

  private double rateIgnoringFixings(
      OvernightIndexObservation observation, DiscountFactors discountFactors) {
    LocalDate fixingStartDate = observation.getEffectiveDate();
    LocalDate fixingEndDate = observation.getMaturityDate();
    double accrualFactor = observation.getYearFraction();
    double dfStart = discountFactors.discountFactor(fixingStartDate);
    double dfEnd = discountFactors.discountFactor(fixingEndDate);
    return (dfStart / dfEnd - 1.0F) / accrualFactor;
  }
}
