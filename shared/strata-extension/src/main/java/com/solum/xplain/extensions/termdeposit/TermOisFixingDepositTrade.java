package com.solum.xplain.extensions.termdeposit;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.Resolvable;
import com.opengamma.strata.product.PortfolioItemInfo;
import com.opengamma.strata.product.ProductTrade;
import com.opengamma.strata.product.TradeInfo;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class TermOisFixingDepositTrade
    implements ProductTrade, Resolvable<ResolvedTermOisFixingDepositTrade> {

  private final TradeInfo info;
  private final TermOisFixingDeposit product;

  public static TermOisFixingDepositTrade of(TradeInfo info, TermOisFixingDeposit product) {
    return new TermOisFixingDepositTrade(info, product);
  }

  @Override
  public ResolvedTermOisFixingDepositTrade resolve(ReferenceData refData) {
    return ResolvedTermOisFixingDepositTrade.builder()
        .info(this.info)
        .product(this.product.resolve(refData))
        .build();
  }

  @Override
  public ProductTrade withInfo(PortfolioItemInfo info) {
    return new TermOisFixingDepositTrade(TradeInfo.from(info), this.product);
  }
}
