package com.solum.xplain.extensions.overnightswap

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.StandardId
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.date.HolidayCalendarIds
import com.opengamma.strata.basics.date.Tenor
import com.opengamma.strata.basics.index.OvernightIndex
import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.basics.schedule.RollConventions
import com.opengamma.strata.basics.schedule.StubConvention
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.market.curve.CurveNodeDate
import com.opengamma.strata.market.curve.CurveNodeDateOrder
import com.opengamma.strata.market.observable.QuoteId
import com.opengamma.strata.market.param.TenorDateParameterMetadata
import com.opengamma.strata.product.common.PayReceive
import com.opengamma.strata.product.swap.CompoundingMethod
import com.opengamma.strata.product.swap.FixingRelativeTo
import com.opengamma.strata.product.swap.OvernightAccrualMethod
import com.opengamma.strata.product.swap.Swap
import com.opengamma.strata.product.swap.type.OvernightRateSwapLegConvention
import com.solum.xplain.extensions.index.OvernightTermIndex
import java.time.LocalDate
import spock.lang.Specification

class OvernightTermOvernightSwapNodeTest extends Specification {

  static REF_DATA = ReferenceData.standard()
  static CONVENTION = OvernightTermOvernightSwapConvention.of("USD-SOFR-OIS-SOFR-3M")
  static TEMPLATE = new OvernightTermOvernightSwapTradeTemplate(Tenor.TENOR_3M, CONVENTION)
  static NODE = OvernightTermOvernightSwapCurveNode.builder()
  .template(TEMPLATE)
  .rateId(QuoteId.of(StandardId.of("XPL", "MDK")))
  .additionalSpread(0d)
  .label("LABEL")
  .date(CurveNodeDate.END)
  .dateOrder(CurveNodeDateOrder.DEFAULT)
  .build()

  def "should return correct requirements"() {
    expect:
    NODE.requirements() == [QuoteId.of(StandardId.of("XPL", "MDK"))] as Set
  }

  def "should return correct metadata"() {
    setup:
    def expected = TenorDateParameterMetadata.of(LocalDate.of(2025, 04, 8), Tenor.TENOR_3M, "LABEL")

    expect:
    NODE.metadata(LocalDate.of(2025, 01, 6), REF_DATA) == expected
  }

  def "should return OvernightTermOvernightSwap trade"() {
    setup:
    LocalDate tradeDate = LocalDate.of(2025, 1, 6)
    def quoteId = QuoteId.of(StandardId.of("XPL", "MDK"))

    def marketData = Mock(MarketData)
    marketData.getValuationDate() >> tradeDate
    marketData.getValue(quoteId) >> 0.01d

    def termOvernightLeg = TermOvernightSwapLegConvention.builder()
      .index(OvernightTermIndex.of("USD-SOFR-3M"))
      .notionalExchange(false)
      .currency(Currency.of("USD"))
      .accrualFrequency(Frequency.P3M)
      .dayCount(DayCounts.ACT_360)
      .accrualBusinessDayAdjustment(
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
      .compoundingMethod(CompoundingMethod.NONE)
      .accrualMethod(OvernightAccrualMethod.COMPOUNDED)
      .rollConvention(RollConventions.EOM)
      .paymentFrequency(Frequency.P3M)
      .stubConvention(StubConvention.SMART_INITIAL)
      .startDateBusinessDayAdjustment(
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
      .endDateBusinessDayAdjustment(
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
      .rateCutOffDays(1)
      .paymentDateOffset(DaysAdjustment.ofBusinessDays(2, HolidayCalendarIds.USGS))
      .fixingDateOffset(DaysAdjustment.ofBusinessDays(-2, HolidayCalendarIds.USGS))
      .fixingRelativeTo(FixingRelativeTo.PERIOD_START)
      .build()

    def overnightLeg = OvernightRateSwapLegConvention.builder()
      .index(OvernightIndex.of("USD-SOFR"))
      .currency(Currency.of("USD"))
      .accrualFrequency(Frequency.P3M)
      .dayCount(DayCounts.ACT_360)
      .accrualBusinessDayAdjustment(
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
      .compoundingMethod(CompoundingMethod.NONE)
      .accrualMethod(OvernightAccrualMethod.COMPOUNDED)
      .rollConvention(RollConventions.EOM)
      .paymentFrequency(Frequency.P3M)
      .stubConvention(StubConvention.SMART_INITIAL)
      .startDateBusinessDayAdjustment(
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
      .endDateBusinessDayAdjustment(
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
      .rateCutOffDays(1)
      .paymentDateOffset(DaysAdjustment.ofBusinessDays(2, HolidayCalendarIds.USGS))
      .notionalExchange(false)
      .build()

    LocalDate startDate = termOvernightLeg.getPaymentDateOffset().adjust(tradeDate, REF_DATA)
    LocalDate endDate = startDate.plus(Tenor.TENOR_3M.getPeriod())

    def leg1 = termOvernightLeg.toLeg(startDate, endDate, PayReceive.PAY, 0d, 0.01d)
    def leg2 = overnightLeg.toLeg(startDate, endDate, PayReceive.RECEIVE, 0d)
    def expected = Swap.of(leg1, leg2)

    when:
    def trade = NODE.trade(0d, marketData, REF_DATA)

    then:
    trade.product.getStartDate() == expected.getStartDate()
    trade.product.getEndDate() == expected.getEndDate()
    trade.product.getLegs() == expected.getLegs()
    trade.info.tradeDate == Optional.of(tradeDate)
  }

  def "should return OvernightTermOvernightSwap resolved trade"() {
    setup:
    def quoteId = QuoteId.of(StandardId.of("XPL", "MDK"))

    def marketData = Mock(MarketData)
    marketData.getValuationDate() >> LocalDate.of(2015, 5, 5)
    marketData.getValue(quoteId) >> 0.01d

    expect:
    NODE.trade(10d, marketData, REF_DATA).resolve(REF_DATA) != null
  }
}
