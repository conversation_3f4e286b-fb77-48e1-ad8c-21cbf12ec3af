package com.solum.xplain.extensions.product

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.AdjustableDate
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.BusinessDayConventions
import com.opengamma.strata.basics.date.HolidayCalendarIds
import com.opengamma.strata.basics.date.Tenor
import com.opengamma.strata.product.common.BuySell
import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.extensions.index.OvernightTermIndex
import com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConvention
import java.time.LocalDate
import spock.lang.Specification

class OvernightTermOvernightSwapConventionsTest extends Specification {

  def "should return USD-SOFR-OIS-SOFR-3M convention from extendedEnum"() {
    when:
    def convention = OvernightTermOvernightSwapConvention.of("USD-SOFR-OIS-SOFR-3M")

    then:
    convention != null
    convention.getName() == "USD-SOFR-OIS-SOFR-3M"
    convention.getIndex() == OvernightTermIndex.of("USD-SOFR-3M")
    convention.getSpotDateOffset().getDays() == 2
  }

  def "should be able to generate trade"() {
    when:
    def convention = OvernightTermOvernightSwapConvention.of("USD-SOFR-OIS-SOFR-3M")
    def trade = convention.createTrade(LocalDate.of(2022, 1, 1), Tenor.TENOR_3M, BuySell.BUY, 100, 1, ReferenceData.standard())

    then:
    trade != null
    //Adjust start + end dates with holiday calendar
    trade.getProduct().getStartDate() == AdjustableDate.of(LocalDate.of(2022, 1, 4), BusinessDayAdjustment.of(BusinessDayConventions.MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
    trade.getProduct().getEndDate() == AdjustableDate.of(LocalDate.of(2022, 4, 4), BusinessDayAdjustment.of(BusinessDayConventions.MODIFIED_FOLLOWING, HolidayCalendarIds.USGS))
    trade.getProduct().getLeg(PayReceive.PAY).get().getCurrency() == Currency.USD
  }
}
