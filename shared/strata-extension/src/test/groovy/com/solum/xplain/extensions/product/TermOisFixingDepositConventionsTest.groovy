package com.solum.xplain.extensions.product

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.product.common.BuySell
import com.solum.xplain.extensions.index.OvernightTermIndex
import com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConvention
import java.time.LocalDate
import java.time.Period
import spock.lang.Specification

class TermOisFixingDepositConventionsTest extends Specification {

  def "should return USD-SOFR-3M convention from extendedEnum"() {
    when:
    def convention = TermOisFixingDepositConvention.of("USD-SOFR-3M")

    then:
    convention != null
    convention.getName() == "USD-SOFR-3M"
    convention.getIndex() == OvernightTermIndex.of("USD-SOFR-3M")
    convention.getPaymentDateOffset().getDays() == 2
  }

  def "should be able to generate trade"() {
    when:
    def convention = TermOisFixingDepositConvention.of("USD-SOFR-3M")
    def trade = convention.createTrade(LocalDate.of(2022, 1, 1), Period.ofMonths(3), BuySell.BUY, 100, 1, ReferenceData.standard())

    then:
    trade != null
    //Adjust start + end dates with holiday calendar
    trade.getProduct().getStartDate() == LocalDate.of(2022, 1, 4)
    trade.getProduct().getEndDate() == LocalDate.of(2022, 4, 4)
    trade.getProduct().getFixedRate() == 1
    trade.getProduct().getCurrency() == Currency.USD
  }
}
