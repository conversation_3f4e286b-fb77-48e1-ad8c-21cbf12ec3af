package com.solum.xplain.extensions.termdeposit

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING
import static com.opengamma.strata.product.common.BuySell.BUY

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.StandardId
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.date.HolidayCalendarIds
import com.opengamma.strata.basics.date.Tenor
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.market.observable.QuoteId
import com.opengamma.strata.market.param.TenorDateParameterMetadata
import com.solum.xplain.extensions.index.OvernightTermIndex
import java.time.LocalDate
import java.time.Period
import spock.lang.Specification

class TermOisFixingDepositNodeTest extends Specification {

  static REF_DATA = ReferenceData.standard()
  static CONVENTION = TermOisFixingDepositConvention.of("USD-SOFR-3M")
  static TEMPLATE = new TermOisFixingDepositNodeTemplate(Period.ofMonths(3), CONVENTION)
  static NODE = TermOisFixingDepositCurveNode.builder()
  .template(TEMPLATE)
  .rateId(QuoteId.of(StandardId.of("XPL", "MDK")))
  .additionalSpread(0d)
  .label("LABEL")
  .build()

  def "should return correct requirements"() {
    expect:
    NODE.requirements() == [QuoteId.of(StandardId.of("XPL", "MDK"))] as Set
  }

  def "should return correct metadata"() {
    setup:
    def expected = TenorDateParameterMetadata.of(LocalDate.of(2025, 04, 8), Tenor.TENOR_3M, "LABEL")

    expect:
    NODE.metadata(LocalDate.of(2025, 01, 6), REF_DATA) == expected
  }

  def "should return TermOisFixingDeposit trade"() {
    setup:
    LocalDate tradeDate = LocalDate.of(2025, 1, 6)
    def quoteId = QuoteId.of(StandardId.of("XPL", "MDK"))

    def marketData = Mock(MarketData)
    marketData.getValuationDate() >> tradeDate
    marketData.getValue(quoteId) >> 0.01d

    //start + end dates adjusted with holiday calendar (+ 2 days) as per convention.
    TermOisFixingDeposit expected = TermOisFixingDeposit.builder()
      .buySell(BUY)
      .notional(10d)
      .startDate(LocalDate.of(2025, 1, 8))
      .endDate(LocalDate.of(2025, 4, 8))
      .fixedRate(0.01d)
      .index(OvernightTermIndex.of("USD-SOFR-3M"))
      .fixingDateOffset(DaysAdjustment.ofBusinessDays(-2, HolidayCalendarIds.USGS))
      .dayCount(DayCounts.ACT_360)
      .currency(Currency.USD)
      .build()

    when:
    def trade = NODE.trade(10d, marketData, REF_DATA)

    then:
    trade.product.getStartDate() == expected.getStartDate()
    trade.product.getEndDate() == expected.getEndDate()
    trade.product.getFixedRate() == expected.getFixedRate()
    trade.product.getIndex() == expected.getIndex()
    trade.product.getFixingDateOffset() == expected.getFixingDateOffset()
    trade.product.getDayCount() == expected.getDayCount()
    trade.product.getCurrency() == expected.getCurrency()
    trade.product.getBuySell() == expected.getBuySell()
    trade.product.getNotional() == expected.getNotional()
    trade.info.tradeDate == Optional.of(tradeDate)
  }

  def "should return TermOisFixingDeposit resolved trade"() {
    setup:
    def quoteId = QuoteId.of(StandardId.of("XPL", "MDK"))

    def marketData = Mock(MarketData)
    marketData.getValuationDate() >> LocalDate.of(2015, 5, 5)
    marketData.getValue(quoteId) >> 0.01d

    expect:
    NODE.trade(10d, marketData, REF_DATA).resolve(REF_DATA) != null
  }
}
