plugins {
  id "java-test-fixtures"
}

dependencies {
  compileOnly project(":shared:data-grid")
  compileOnly "com.hazelcast:hazelcast-spring:${hazelcastVersion}"
  compileOnly "io.micrometer:micrometer-core"
  implementation "io.protostuff:protostuff-core:${protostuffVersion}"
  implementation "io.protostuff:protostuff-runtime:${protostuffVersion}"
  implementation "org.mongodb:bson:5.5.1"
  implementation "org.projectlombok:lombok:${lombokVersion}"
  implementation "org.springframework.boot:spring-boot-starter-aop"
  implementation "org.springframework.data:spring-data-mongodb"
  implementation "org.springframework.kafka:spring-kafka"
  implementation 'jakarta.servlet:jakarta.servlet-api:6.1.0'
  implementation "org.reflections:reflections:${reflectionsVersions}"

  testFixturesImplementation "org.mongodb:bson:5.5.1"
  testFixturesCompileOnly "org.spockframework:spock-core:${spockVersion}"
  testFixturesCompileOnly "org.spockframework:spock-spring:${spockVersion}"
  testFixturesCompileOnly "org.jetbrains:annotations"
  testFixturesCompileOnly "org.springframework:spring-context"
  testFixturesCompileOnly "org.springframework:spring-test"
  testFixturesCompileOnly "org.projectlombok:lombok:${lombokVersion}"
  testFixturesAnnotationProcessor "org.projectlombok:lombok:${lombokVersion}"

  testImplementation project(":shared:data-grid")
  testImplementation "org.spockframework:spock-core:${spockVersion}"

  integrationTestImplementation "io.micrometer:micrometer-core"
  integrationTestImplementation "org.spockframework:spock-core:${spockVersion}"
  integrationTestImplementation 'org.springframework:spring-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-log4j2'

  annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
}

configurations {
  all*.exclude module: 'spring-boot-starter-logging'
}


tasks.register('sourceJar', Jar) {
  from sourceSets.main.allJava
}
