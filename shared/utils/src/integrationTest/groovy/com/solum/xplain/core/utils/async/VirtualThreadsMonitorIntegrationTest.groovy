package com.solum.xplain.core.utils.async

import com.solum.xplain.shared.utils.SharedUtilsAutoConfiguration
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import jakarta.annotation.Resource
import java.time.Duration
import java.util.concurrent.CountDownLatch
import jdk.jfr.consumer.RecordedEvent
import org.apache.logging.log4j.core.LogEvent
import org.apache.logging.log4j.core.LoggerContext
import org.apache.logging.log4j.core.appender.AbstractAppender
import org.apache.logging.log4j.core.config.Property
import org.slf4j.event.Level
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestPropertySource
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Timeout

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = [TestConfig, SharedUtilsAutoConfiguration])
@TestPropertySource(properties = [
  "debug=true",
  "app.virtual-threads-monitor.enabled=true",
  "app.virtual-threads-monitor.logging.enabled=true",
  "app.virtual-threads-monitor.metrics.enabled=true",
  "app.virtual-threads-monitor.metrics.metric-name=thread-pinning"
])
class VirtualThreadsMonitorTest extends Specification {
  @Resource VirtualThreadsMonitor monitor
  @Resource VirtualThreadsMonitorProperties properties
  @Resource VirtualThreadPinnedEventHandler.Logging loggingHandler
  @Resource MeterRegistry meterRegistry

  static CountDownLatch jfrEventLatch

  @TestConfiguration
  static class TestConfig {
    @Bean
    @Primary
    MeterRegistry meterRegistry() {
      return new SimpleMeterRegistry()
    }

    @Bean
    VirtualThreadPinnedEventHandler waitingHandler() {
      return new VirtualThreadPinnedEventHandler() {
          @Override
          void handle(RecordedEvent event) {
            jfrEventLatch.countDown()
          }
        }
    }
  }

  def setup() {
    jfrEventLatch = new CountDownLatch(1)
  }

  def "monitor properties should be loaded"() {
    expect:
    properties.enabled()
    properties.maxAge() == Duration.ofSeconds(10)
    properties.threshold() == null
  }

  def "monitor logging properties should be loaded"() {
    expect:
    properties.logging().enabled()
    properties.logging().level() == Level.INFO
    properties.logging().stackTraceMaxDepth() == 25
  }

  def "monitor metrics properties should be loaded"() {
    expect:
    properties.metrics().enabled()
    properties.metrics().metricName() == "thread-pinning"
  }

  def "monitor should be started by default"() {
    expect:
    monitor.isRunning()
  }

  def "should stop and start virtual threads monitor"() {
    when:
    monitor.stop()

    then:
    !monitor.isRunning()

    when:
    monitor.start()

    then:
    monitor.isRunning()
  }

  @Timeout(5)
  def "should record metrics when virtual thread is pinned"() {
    given:
    def initialCount = meterRegistry.timer(properties.metrics().metricName()).count()

    when:
    pinCarrierThread()
    jfrEventLatch.await() // wait for the JFR event to be handled
    Thread.sleep(100) // in case we're not the last handler

    then:
    meterRegistry.timer(properties.metrics().metricName()).count() > initialCount
  }

  @Timeout(5)
  def "should log when virtual thread is pinned"() {
    given:
    def logEvents = new LinkedList<LogEvent>()
    def logLatch = new CountDownLatch(1)
    def listAppender = new AbstractAppender("test", null, null, true, new Property[0]) {
        @Override
        void append(LogEvent event) {
          logEvents.add(event)
          logLatch.countDown()
        }
      }
    listAppender.start()
    def logger = LoggerContext.getContext(false).getLogger(VirtualThreadPinnedEventHandler.Logging.class.getName())
    logger.addAppender(listAppender)
    logger.setLevel(org.apache.logging.log4j.Level.ALL)

    when:
    pinCarrierThread()
    jfrEventLatch.await() // wait for the JFR event to be handled
    logLatch.await() // wait for the log appender to be called

    then:
    logEvents.size() == 1
    logEvents[0].level.name() == properties.logging().level().name()

    cleanup:
    logger.removeAppender(listAppender)
    listAppender.stop()
  }

  private void pinCarrierThread() {
    var joinLatch = new CountDownLatch(1)
    Thread.start(() -> {
      var joinLatch2 = new CountDownLatch(1)
      Thread.startVirtualThread(() -> {
        synchronized (this) {
          Thread.sleep(250)
        }
        joinLatch2.countDown()
      })
      joinLatch2.await()
      joinLatch.countDown()
    })
    joinLatch.await()
  }
}
