package com.solum.xplain.core.utils.async;

import io.micrometer.core.instrument.MeterRegistry;
import java.util.stream.Collectors;
import jdk.jfr.consumer.RecordedEvent;
import jdk.jfr.consumer.RecordedFrame;
import jdk.jfr.consumer.RecordedStackTrace;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface VirtualThreadPinnedEventHandler {
  void handle(jdk.jfr.consumer.RecordedEvent event);

  @RequiredArgsConstructor
  class Metrics implements VirtualThreadPinnedEventHandler {
    private final VirtualThreadsMonitorProperties.VirtualThreadsMonitorMetricsProperties properties;
    private final MeterRegistry meterRegistry;

    @Override
    public void handle(jdk.jfr.consumer.RecordedEvent event) {
      meterRegistry.timer(properties.metricName()).record(event.getDuration());
    }
  }

  @Slf4j
  @RequiredArgsConstructor
  class Logging implements VirtualThreadPinnedEventHandler {
    private final VirtualThreadsMonitorProperties.VirtualThreadsMonitorLoggingProperties properties;

    @Override
    public void handle(RecordedEvent event) {
      var thread = event.getThread() != null ? event.getThread().getJavaName() : "<unknown>";
      var duration = event.getDuration();
      var stackTrace = formatStackTrace(event.getStackTrace());
      log.atLevel(properties.level())
          .log(
              "Virtual thread '{}' pinned for: {} ms at {}, stacktrace: \n\t{}",
              thread,
              duration.toMillis(),
              event.getStartTime(),
              stackTrace);
    }

    private String formatStackTrace(RecordedStackTrace stackTrace) {
      if (stackTrace == null) {
        return null;
      }
      String formatted =
          stackTrace.getFrames().stream()
              .limit(properties.stackTraceMaxDepth())
              .map(this::formatStackTraceFrame)
              .collect(Collectors.joining("\n\t"));
      if (properties.stackTraceMaxDepth() < stackTrace.getFrames().size()) {
        return formatted + "\n\t...";
      }
      return formatted;
    }

    private String formatStackTraceFrame(RecordedFrame frame) {
      return frame.getMethod().getType().getName()
          + "."
          + frame.getMethod().getName()
          + "(): line "
          + frame.getLineNumber();
    }
  }
}
