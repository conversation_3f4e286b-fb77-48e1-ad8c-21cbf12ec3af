package com.solum.xplain.core.utils.async;

import java.util.Collection;
import jdk.jfr.consumer.RecordingStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.SmartLifecycle;

@RequiredArgsConstructor
@Slf4j
public class VirtualThreadsMonitor implements SmartLifecycle {
  private static final String VIRTUAL_THREAD_PINNED = "jdk.VirtualThreadPinned";
  private final VirtualThreadsMonitorProperties properties;
  private final Collection<VirtualThreadPinnedEventHandler> eventHandlers;
  private volatile boolean running = false;
  private RecordingStream recordingStream;

  @Override
  public void start() {
    if (!running) {
      log.info(
          "Starting virtual threads monitor with handler(s): {}",
          eventHandlers.stream().map(Object::getClass).map(Class::getSimpleName).toList());
      this.recordingStream = new RecordingStream();
      recordingStream
          .enable(VIRTUAL_THREAD_PINNED)
          .withStackTrace()
          .withThreshold(properties.threshold());
      recordingStream.onEvent(
          VIRTUAL_THREAD_PINNED,
          event -> {
            for (var handler : eventHandlers) {
              handler.handle(event);
            }
          });
      recordingStream.setMaxAge(properties.maxAge());
      recordingStream.startAsync();
      this.running = true;
    }
  }

  @Override
  public void stop() {
    if (running) {
      log.info("Stopping virtual threads monitor");
      this.recordingStream.close();
      this.running = false;
    }
  }

  @Override
  public boolean isRunning() {
    return this.running;
  }
}
