package com.solum.xplain.core.utils.async;

import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import org.slf4j.event.Level;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.DefaultValue;
import org.springframework.validation.annotation.Validated;

@Validated
@ConfigurationProperties(prefix = "app.virtual-threads-monitor")
public record VirtualThreadsMonitorProperties(
    Boolean enabled,
    @DefaultValue("PT10S") @NotNull Duration maxAge,
    Duration threshold,
    @DefaultValue VirtualThreadsMonitorMetricsProperties metrics,
    @DefaultValue VirtualThreadsMonitorLoggingProperties logging) {

  public record VirtualThreadsMonitorMetricsProperties(
      boolean enabled, @DefaultValue("jfr.thread.pinning") String metricName) {}

  public record VirtualThreadsMonitorLoggingProperties(
      boolean enabled,
      @DefaultValue("INFO") Level level,
      @DefaultValue("25") int stackTraceMaxDepth) {}
}
