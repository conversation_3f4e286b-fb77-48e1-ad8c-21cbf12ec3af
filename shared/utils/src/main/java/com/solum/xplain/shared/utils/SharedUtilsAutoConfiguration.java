package com.solum.xplain.shared.utils;

import com.hazelcast.core.HazelcastInstance;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.utils.async.VirtualThreadPinnedEventHandler;
import com.solum.xplain.core.utils.async.VirtualThreadsMonitor;
import com.solum.xplain.core.utils.async.VirtualThreadsMonitorProperties;
import com.solum.xplain.shared.datagrid.ClusterEventPublisher;
import com.solum.xplain.shared.datagrid.impl.hazelcast.HazelcastSerializationConfigurer;
import com.solum.xplain.shared.utils.cache.CacheUnpagedAspect;
import com.solum.xplain.shared.utils.cache.UnpagedCacheEventHandler;
import com.solum.xplain.shared.utils.cache.UnpagedCachingService;
import com.solum.xplain.shared.utils.ratelimit.RateLimitedOperationService;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@AutoConfiguration
@ComponentScan(basePackageClasses = {RateLimitedOperationService.class})
public class SharedUtilsAutoConfiguration {
  @Configuration
  @ConditionalOnClass(HazelcastInstance.class)
  static class HazelcastSerializationConfiguration {
    @Bean
    HazelcastSerializationConfigurer auditUserSerializationConfigurer() {
      return config -> config.getCompactSerializationConfig().addClass(AuditUser.class);
    }
  }

  @Configuration
  @ConditionalOnProperty(
      prefix = "app.unpaged-cache",
      name = "enabled",
      havingValue = "true",
      matchIfMissing = true)
  @ConditionalOnBean(ClusterEventPublisher.class)
  static class UnpagedCacheConfiguration {
    @Bean
    CacheUnpagedAspect cacheUnpagedAspect(UnpagedCachingService unpagedCachingService) {
      return new CacheUnpagedAspect(unpagedCachingService);
    }

    @Bean
    UnpagedCacheEventHandler unpagedCacheEventHandler(ClusterEventPublisher clusterEventPublisher) {
      return new UnpagedCacheEventHandler(clusterEventPublisher);
    }

    @Bean
    UnpagedCachingService unpagedCachingService(UnpagedCacheEventHandler unpagedCacheEventHandler) {
      return new UnpagedCachingService(unpagedCacheEventHandler);
    }
  }

  @Configuration
  @ConditionalOnProperty(
      prefix = "app.virtual-threads-monitor",
      name = "enabled",
      havingValue = "true")
  @EnableConfigurationProperties({VirtualThreadsMonitorProperties.class})
  static class VirtualThreadsMonitorConfiguration {
    @Resource VirtualThreadsMonitorProperties properties;

    @Bean
    VirtualThreadsMonitor virtualThreadsMonitor(
        List<VirtualThreadPinnedEventHandler> eventHandlers) {
      return new VirtualThreadsMonitor(properties, eventHandlers);
    }

    @ConditionalOnProperty(
        prefix = "app.virtual-threads-monitor.metrics",
        name = "enabled",
        havingValue = "true")
    @ConditionalOnBean(MeterRegistry.class)
    static class VirtualThreadsMonitorMetricsConfiguration {
      @Bean
      VirtualThreadPinnedEventHandler virtualThreadPinnedMetricsHandler(
          VirtualThreadsMonitorProperties properties, MeterRegistry meterRegistry) {
        return new VirtualThreadPinnedEventHandler.Metrics(properties.metrics(), meterRegistry);
      }
    }

    @ConditionalOnProperty(
        prefix = "app.virtual-threads-monitor.logging",
        name = "enabled",
        havingValue = "true")
    @Bean
    VirtualThreadPinnedEventHandler virtualThreadPinnedLoggingHandler() {
      return new VirtualThreadPinnedEventHandler.Logging(properties.logging());
    }
  }
}
