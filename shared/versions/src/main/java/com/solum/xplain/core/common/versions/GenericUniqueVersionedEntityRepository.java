package com.solum.xplain.core.common.versions;

import static com.solum.xplain.core.common.AggregateOptions.ALLOW_DISK_USE_BATCH_1000;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.common.versions.State.DELETED;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.listVersionedAll;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.listVersionedInFuture;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.listVersionedNonDeleted;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.versionedByIdExact;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.versionedLatestNonDeletedById;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.time.LocalDate.ofEpochDay;
import static java.util.Optional.ofNullable;
import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Sets;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.FutureVersionsAction;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import org.jspecify.annotations.NullMarked;
import org.springframework.core.GenericTypeResolver;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;

// TODO(SXSD-10634): Rename to VersionedEntityRepository
@NullMarked
public abstract class GenericUniqueVersionedEntityRepository<T extends VersionedEntity>
    implements VersionedEntityQueries<T> {

  private final MongoOperations mongoOperations;
  private final VersionedEntityMapper<T> mapper;
  private final Class<T> genericType;

  protected GenericUniqueVersionedEntityRepository(
      MongoOperations mongoOperations, VersionedEntityMapper<T> mapper) {
    this.mongoOperations = mongoOperations;
    this.mapper = mapper;
    this.genericType =
        (Class<T>)
            GenericTypeResolver.resolveTypeArgument(
                getClass(), GenericUniqueVersionedEntityRepository.class);
  }

  @Override
  public List<T> findAllActiveItems(BitemporalDate stateDate) {
    return entities(stateDate, VersionedEntityFilter.active(), new Criteria());
  }

  @Override
  public Either<ErrorItem, T> findOneActiveItem(String entityId, BitemporalDate stateDate) {
    return entity(entityId, stateDate, VersionedEntityFilter.active());
  }

  @Override
  public Either<ErrorItem, EntityId> archive(
      String entityId, BitemporalDate versionDate, ArchiveEntityForm f) {
    return entityExact(entityId, versionDate).map(e -> archive(e, f));
  }

  @Override
  public Either<ErrorItem, EntityId> delete(String entityId, BitemporalDate versionDate) {
    return entityExact(entityId, versionDate).flatMap(this::delete);
  }

  @Override
  public Either<ErrorItem, List<T>> findManyActiveItems(
      Collection<String> entityIds, BitemporalDate stateDate, boolean lenient) {
    var uniqueIds = new HashSet<>(entityIds);
    var entities =
        entities(
            stateDate,
            VersionedEntityFilter.active(),
            where(VersionedEntity.Fields.entityId).in(uniqueIds));
    if (!lenient && entities.size() != uniqueIds.size()) {
      var foundIds = entities.stream().map(T::getEntityId).collect(Collectors.toSet());
      return left(
          OBJECT_NOT_FOUND.entity(
              String.format("Entities id not found %s", Sets.difference(uniqueIds, foundIds))));
    }
    return right(entities);
  }

  /** For use where the entity has an externally derived entityId. */
  protected static Criteria hasEntityId(String entityId) {
    return where(VersionedEntity.Fields.entityId).is(entityId);
  }

  /**
   * Returns the unique entity criteria for the given entity. this will be
   * hasEntityId(entity.entityId) if the entity has an externally derived entityId (e.g.
   * CompanyIpvSettings is created with the entityId of the related Company). Otherwise a different
   * criteria for uniqueness should be created using one or more fields.
   */
  protected abstract Criteria uniqueEntityCriteria(
      T entity); // TODO: default to hasEntityId and update docs

  protected Either<ErrorItem, T> entity(
      String entityId, BitemporalDate vd, VersionedEntityFilter filter) {
    return findOneByEntityId(entityId, vd, filter)
        .map(Either::<ErrorItem, T>right)
        .orElse(left(notFound()));
  }

  protected Optional<T> findOneByEntityId(
      String entityId, BitemporalDate vd, VersionedEntityFilter filter) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .addAll(versionedLatestNonDeletedById(entityId, vd))
            .add(match(filter.criteria()))
            .build();
    return ofNullable(aggregation(operations).getUniqueMappedResult());
  }

  /**
   * @deprecated SXSD-9639 : use equivalent function with {@link BitemporalDate} instead.
   */
  @Deprecated
  protected Either<ErrorItem, T> entityExact(String entityId, LocalDate vd) {
    return entityExact(entityId, BitemporalDate.newOf(vd));
  }

  protected Either<ErrorItem, T> entityExact(String entityId, BitemporalDate vd) {
    return mongoOperations
        .query(genericType)
        .matching(versionedByIdExact(entityId, vd.getActualDate()))
        .first()
        .map(Either::<ErrorItem, T>right)
        .orElse(left(notFound()));
  }

  /**
   * @deprecated SXSD-9639 : use equivalent function with {@link BitemporalDate} instead.
   */
  @Deprecated
  protected List<T> entities(LocalDate stateDate, Criteria extraCriteria) {
    return entities(BitemporalDate.newOf(stateDate), extraCriteria);
  }

  protected List<T> entities(BitemporalDate stateDate, Criteria extraCriteria) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listVersionedAll(stateDate))
            .add(match(extraCriteria));

    if (defaultEntitiesSort() != Sort.unsorted()) {
      operations.add(sort(defaultEntitiesSort()));
    }
    return aggregation(operations.build()).getMappedResults();
  }

  protected List<T> entities(
      BitemporalDate stateDate, VersionedEntityFilter filter, Criteria extraCriteria) {
    return entities(stateDate, filter, extraCriteria, defaultEntitiesSort());
  }

  protected List<T> entities(
      BitemporalDate stateDate,
      VersionedEntityFilter filter,
      Criteria extraCriteria,
      Sort entitiesSort) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listVersionedNonDeleted(stateDate))
            .add(match(filter.criteria()))
            .add(match(extraCriteria));

    if (entitiesSort != Sort.unsorted()) {
      operations.add(sort(entitiesSort));
    }
    return aggregation(operations.build()).getMappedResults();
  }

  @Override
  public DateList futureVersionsByCriteria(Criteria uniquenessCriteria, BitemporalDate stateDate) {
    var futureVersions =
        futureVersions(uniquenessCriteria, stateDate.getActualDate()).stream()
            .map(VersionedEntity::getValidFrom)
            .toList();
    return DateList.uniqueSorted(futureVersions);
  }

  protected List<T> entityVersions(String entityId) {
    var operations =
        List.of(
            match(where(VersionedEntity.Fields.entityId).is(entityId)),
            sort(DESC, VersionedEntity.Fields.validFrom, VersionedEntity.Fields.recordDate));
    return aggregation(operations).getMappedResults();
  }

  protected Either<ErrorItem, EntityId> delete(T entity) {
    return Eithers.cond(
            DELETED != entity.getState(),
            OPERATION_NOT_ALLOWED.entity("Version already deleted"),
            entity)
        .map(
            e -> {
              deleteVersion(e);
              return entityId(e.getEntityId());
            });
  }

  protected EntityId archive(T entity, ArchiveEntityForm f) {
    var versionForm = f.getVersionForm();
    return update(entity, versionForm, e -> e.archive(versionForm, versionForm.getValidFrom()));
  }

  protected EntityId update(T entity, NewVersionFormV2 versionForm, UnaryOperator<T> modifyFn) {
    T updatedEntity = modifyFn.apply(mapper.copy(entity));
    if (!updatedEntity.valueEquals(entity)) {
      mongoOperations.save(beforeStoring(updatedEntity));
    }
    deleteFutureVersions(updatedEntity, versionForm);
    return entityId(updatedEntity.getEntityId());
  }

  private Optional<T> getEntityByUniqueField(Criteria uniquenessCriteria, BitemporalDate vd) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(uniquenessCriteria))
            .addAll(listVersionedAll(vd))
            .build();
    return Optional.ofNullable(aggregation(operations).getUniqueMappedResult());
  }

  protected Either<ErrorItem, EntityId> insert(T newEntity, NewVersionFormV2 versionForm) {
    // Always returns an Either.Right
    return right(saveNew(newEntity, versionForm));
  }

  /**
   * This is a logical insert but if there is an existing record (e.g. archived) then it may
   * actually end up being updated. It never returns an error.
   *
   * @param newEntity the new entity to save
   * @param versionForm versioning information
   * @return the logical ID of the entity inserted (or updated)
   */
  protected EntityId saveNew(T newEntity, NewVersionFormV2 versionForm) {
    return getEntityByUniqueField(
            uniqueEntityCriteria(newEntity), new BitemporalDate(versionForm.getStateDate()))
        .map(existing -> updateForInsert(versionForm, newEntity, existing))
        .orElseGet(() -> insertNew(newEntity, versionForm));
  }

  private EntityId insertNew(T entity, NewVersionFormV2 form) {
    var modifiedEntity = beforeStoring(entity);
    mongoOperations.insert(modifiedEntity);
    if (form.getValidFrom().isAfter(ofEpochDay(0))) {
      T archived = mapper.copy(modifiedEntity);
      archived.archive(form, ofEpochDay(0));
      mongoOperations.insert(archived);
    }
    return EntityId.entityId(entity.getEntityId());
  }

  private EntityId updateForInsert(NewVersionFormV2 versionForm, T newEntity, T existingEntity) {
    return update(
        existingEntity, versionForm, e -> fillExistingVersionFields(versionForm, newEntity, e));
  }

  private T fillExistingVersionFields(NewVersionFormV2 versionForm, T newEntity, T existingEntity) {
    newEntity.setEntityId(existingEntity.getEntityId());

    // Adjust validFrom when a new entity is actually existing ARCHIVED and it needs to be
    // made ACTIVE
    if (!NewVersionFormV2.ROOT_DATE.equals(versionForm.getValidFrom())) {
      newEntity.setValidFrom(versionForm.getValidFrom());
    } else {
      newEntity.setValidFrom(existingEntity.getValidFrom());
    }
    return newEntity;
  }

  protected T beforeStoring(T entity) {
    return entity;
  }

  protected Sort defaultEntitiesSort() {
    return Sort.unsorted();
  }

  private AggregationResults<T> aggregation(List<AggregationOperation> aggregationOperations) {
    return mongoOperations.aggregate(
        newAggregation(genericType, aggregationOperations).withOptions(ALLOW_DISK_USE_BATCH_1000),
        genericType);
  }

  private void deleteFutureVersions(T entity, NewVersionFormV2 versionForm) {
    if (versionForm.getFutureVersionsAction() == FutureVersionsAction.DELETE) {
      futureVersions(entity.getEntityId(), versionForm.getValidFrom()).forEach(this::deleteVersion);
    }
  }

  private void deleteVersion(T entity) {
    if (DELETED != entity.getState()) {
      var deletedEntity = mapper.copy(entity);
      deletedEntity.setState(DELETED);
      mongoOperations.save(deletedEntity);
    }
  }

  private List<T> futureVersions(String entityId, LocalDate validFrom) {
    return futureVersions(where(VersionedEntity.Fields.entityId).is(entityId), validFrom);
  }

  private List<T> futureVersions(Criteria criteria, LocalDate validFrom) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listVersionedInFuture(validFrom, criteria))
            .add(match(where(VersionedEntity.Fields.state).ne(DELETED.name())))
            .build();

    return mongoOperations
        .aggregate(
            newAggregation(genericType, operations).withOptions(ALLOW_DISK_USE_BATCH_1000),
            genericType)
        .getMappedResults();
  }

  protected final ErrorItem notFound() {
    return OBJECT_NOT_FOUND.entity("Object not found [" + genericType.getSimpleName() + "]");
  }

  protected final ErrorItem notFound(String entityId) {
    return OBJECT_NOT_FOUND.entity(
        "Object not found [" + genericType.getSimpleName() + "] with entity id [" + entityId + "]");
  }
}
