app:
  license-key:
  pnl-explain:
    enabled: true
  preview:
    allow-mid-only: true
  setup:
    reset:
      enabled: true
      aws-emulator-proxy-url: http://localhost:8083/2015-03-31/functions/function/invocations
  virtual-threads-monitor:
    enabled: true
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    oauth2-redirect-url: http://localhost:8080/xplain/api/swagger-ui/oauth2-redirect.html
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://oauth2:8081/default
  kafka:
    bootstrap-servers: localhost:29092
    properties:
      sasl:
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin-secret";
