spring:
  hazelcast:
    config:
  data:
    redis:
      cluster:
        nodes: localhost:6379,localhost:6380,localhost:6381,localhost:6382,localhost:6383,localhost:6384
        max-redirects: 3
      # should be unique for each instance for locking purposes
      client-name: xplain
      client-type: jedis
      repositories:
        enabled: false
      timeout: 2000ms
  cache:
    type: redis

app:
  data-grid:
    hazelcast:
      enabled: false
    redis:
      enabled: true
