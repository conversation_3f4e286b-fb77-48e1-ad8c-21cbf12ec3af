app:
  version: ${version}
  default:
    team: TEAM_DEFAULT
    role: ROLE_ADMIN
    trusted-client-ids:
  oauth2:
    inactivity-timeout-minutes: 30
    token-cache-duration-minutes: 480
    claims:
      username-claim: xplain/username
      roles-claim: xplain/roles-teams
      teams-claim: xplain/roles-teams
      principal-type-claim: gty
      principal-type-client-value: client-credentials
      client-id-claim: azp
  calculation:
    temp-dir: /tmp/xplain
    topics:
      valuations-topic: valuations
      metrics-topic: valuations-metrics
  company-docs:
    advanced-content-type-verification-enabled: true
  xva-remote:
    s3:
      bucket: xva-msg.qa.xplainfinancial.com
      region: eu-west-2
    topics:
      results-topic: xva-results
      valuations-topic: xva-requests
  xva-settings:
    sigma-step: 1
    time-gap: 0.083333
    hull-white-mean-reversion: 0.03
    time-end: 50
    simulations-for-xva: 1023
    simulations-for-pfe: 1023
    pfe-percentile: 0.95
  cluster:
    event-topic: CLUSTER_EVENTS
  unpaged-cache:
    enabled: true
  socket:
    timeout: 45000
    clean-up-rate: 15000
  mongock:
    enabled: true
  mongo-transactions:
    enabled: false
  mongo-kerberos:
    enabled: false
  retention:
    major-version-retention-months: 24
    minor-version-retention-months: 12
    workflow-retention-duration: P14D
  simulation:
    max-days: 14
  exception-management:
    valuation-data:
      updated-values-tolerance: 1e-8
  evidence:
    cleanup-cron: "0 0 0 * * MON-FRI"
  setup:
    environment-name: local
    reset:
      enabled: false
      aws-region: eu-west-2
      aws-lambda-function-name: arn:aws:lambda:\${app.setup.reset.aws-region}:575153581909:function:solum-xplain-setup-\${app.setup.environment-name}
      datasets:
        - XPLAIN_DEFAULT
        - LONDON
        - ALLCURVES
        - E2ETesting
      protected-collections:
        - accessLogEntry
        - auditEntry
        - auditEntryItem
        - role
        - team
  virtual-threads-monitor:
    enabled: false
    max-age: PT10S
    # threshold:
    logging:
      enabled: true
      level: INFO
      stack-trace-max-depth: 25
    metrics:
      enabled: true
      metric-name: jfr.thread.pinning
  workflow:
    flush-frequency: PT1S
    cleanup-cron: "0 0 3 * * MON-FRI"
    cache:
      wfProcessExecution:
        shared-cache-ttl: PT20M
        near-cache-ttl: PT20M
        max-entries: 50000
      wfStepInstance:
        shared-cache-ttl: PT20M
        near-cache-ttl: PT20M
        max-entries: 200000
      instrumentResultPreliminary:
        shared-cache-ttl: PT10M
        near-cache-ttl: PT10M
        max-entries: 50000
spring:
  cache:
    # We use JCache for Hazelcast, since we want ICache behind the scene and not IMap.
    # ICache has the near-cache support feature, which is required for the workflow cache.
    type: jcache
    jcache:
      provider: com.hazelcast.cache.HazelcastCachingProvider
  data:
    mongodb:
      database: solum-xplain
  jackson:
    serialization:
      write_dates_as_timestamps: false
    default-property-inclusion: non_null
  servlet:
    multipart:
      max-file-size: -1
      max-request-size: -1
  threads:
    virtual:
      enabled: true
  task:
    execution:
      shutdown:
        await-termination: true
        await-termination-period: 30s
  kafka:
    consumer:
      group-id: xplain-api
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      fetch-min-size: 2000000
      properties:
        spring.json.trusted.packages: "com.solum.xplain.*"
      enable-auto-commit: false
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      retries: **********
      acks: all
      properties:
        enable.idempotence: true
        max.in.flight.requests.per.connection: 5
    jaas:
      enabled: true
    listener:
      poll-timeout: 1800000
      ack-mode: BATCH
      type: batch
      concurrency: 2
    properties:
      security:
        protocol: SASL_PLAINTEXT
      sasl:
        mechanism: PLAIN
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username="\${KAFKA_USERNAME}" password="\${KAFKA_PASSWORD}";
springdoc:
  packages-to-scan: "com.solum.xplain"
  swagger-ui:
    enabled: false
  api-docs:
    enabled: false
  default-consumes-media-type: application/json
  default-produces-media-type: application/json
server:
  compression:
    enabled: true
    min-response-size: 1000
    mime-types:
      # Extra MIME types for Xplain
      - text/csv
      # Default Spring list
      - text/html
      - text/xml
      - text/plain
      - text/css
      - text/javascript
      - application/javascript
      - application/json
      - application/xml
  servlet:
    context-path: /xplain/api
  tomcat:
    connection-timeout: -1
    mbeanregistry:
      enabled: true
mongock:
  transaction-enabled: false
  migration-scan-package:
    - com.solum.xplain.support.migration.changeunits
  transaction-strategy: change_unit
management:
  server:
    port: 9000
  metrics:
    enable:
      kafka: false
  endpoints:
    web:
      exposure:
        include: health,prometheus
  #Remove once, redis instance on AWS has been created in kubernetes
  health:
    redis:
      enabled: false
