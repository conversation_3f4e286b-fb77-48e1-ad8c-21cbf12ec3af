# Xplain Calibration module.

## Overview

This module contains the calibration and calculation logic for all the instrument definitions based on portfolio items.

TODO(SXSD-10325): Enhance the documentation, this is pretty much an under construction README.md.

## Curve Calibration

## Presentation & Theory

[Presentation deck](https://solumfinancial.sharepoint.com/:p:/s/SXSD/EX0HLta9ikdNs4IxihYnYtsBdexg_As9undVqZYuk_WRIw?e=OiBQLx)

[Data set](https://solumfinancial.sharepoint.com/:f:/s/SXSD/EnltceNforlPpAK6YTFUMnEBgHkjT2nSMsEuBS9kwsUg3Q?e=HdTuFh)

## Code pointers.

### Enforcing "FX vs Non-FX" mode and "Single"

CalculationCurveGroupDataService.resolveBuilders

### Discount currency chosen by user.

The discount currency can be passed in multiple ways (through a specific implementation of CalculationForm) 
but is also known as "Triangulation Ccy".

### Discounting groups

CalculationCurveGroupDataService.discountingGroupsBuilderFn
- may apply CSA discounting on top of default discounting groups

CalculationCurveGroupDataService.defaultDiscountingGroupsBuilderFn
- default discounting groups builder by priority order
    - Stripping type LIBOR > Local currency > Single currency.

When looking into CalculationDiscountingType, there are only two types: Local Currency and per Discount Currency (but each supported currency is assigned a dedicated enum).
When looking into CalculationStrippingType, there's LIBOR and OIS, but Libor is also named Single, while OIS is (Dual)

There are many discounting groups builder available: 
- DiscountingGroupsBuilder
  - OisLocalCcySingleFallbackDiscountingGroupsBuilder
  - AbstractDiscountingGroupsBuilder 
    - SingleDiscountingGroupsBuilder
    - SingleCcyDiscountingGroupsBuilder group
    - LocalCcyDiscountingGroupsBuilder
    - CsaDiscountingGroupsBuilder

### Discounting with Single Stripping

### Bundles

`ResolvedDiscountingCalibrationBundles` contains all the calibration bundles.

They are initialized in CalculationCurveGroupDataBuilder.resolveCalibrationBundles.
These builders are initialized with `GenericPortfolioItemDiscountingCriteria` by calling `.withItem` through `withTrade`

Note that both FX and Non-FX (depending on the CurveConfigurationType) will have the opportunity to handle each `GenericPortfolioItemDiscountingCriteria`.
Each of them is associated to a `PortfolioItemDiscountingGroupSpecification`, using `fromGenericPortfolioItemCriteria` 
to transform it into a set of information required to build a Bundle on the fly.

Bundles are created as the trades are being processed through `withTrade`.

## Step by step run through.

Entry point of calibration for portfolio items is CalculationCurveGroupDataService.resolveCalculationData

As configuration, we consider that the state date, the market data type, price requirements, 
supported product types are globally available.
Global Settings include things like:
- inflation seasonality (inflationSeasonalitySettings)
- curve stripping (curveStrippingProductSettings)
- convexity setting (convexityAdjustmentsSettings)
- global valuation settings (globalValuationSettings)

### Step 1: How many calibration groups are needed?

Either we get a single one, or we get a split of FX vs Non-FX.
Please keep in mind that the following step applies to _one_ group.

### Step 2: For each calibration group: setting up the discounting curves.

Discounting curves depend on the usage of CSA (which will superseed the default discounting curves when applicable).
Since only one discounting curve is needed, we need to prioritize. 
- Discounting stripping type Libor -> Single discounting group builder
- Discounting type Local currency -> Local currency with fallback discounting group builder
- Fallback default -> Single discounting group builder
 
Single currency discounting group are built using `OisConfigurations` and a `PortfolioItemDiscountIndexResolver`.
They may use any of the indexes available from the curves.
ProductSettingsResolver is actually a close data copy of CurveStrippingProductSettings.

We need to emphasize that these are group *builders*, not the actual groups.

### Followup steps are located in Calibration project readme.md
