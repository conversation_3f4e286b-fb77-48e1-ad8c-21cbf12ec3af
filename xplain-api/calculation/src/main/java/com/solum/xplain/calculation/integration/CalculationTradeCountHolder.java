package com.solum.xplain.calculation.integration;

import com.solum.xplain.shared.datagrid.AtomicCounter;
import com.solum.xplain.shared.datagrid.DataGrid;
import io.atlassian.fugue.Checked;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class CalculationTradeCountHolder {

  public static final String CALC_TRADES_COUNT = "calc:trades-count:";
  private final DataGrid dataGrid;

  public void registerCalculation(String calculationId, Long tradesCount) {
    calculationTradesCounter(calculationId).ifPresent(c -> c.set(tradesCount));
    calculationFailureCounter(calculationId).ifPresent(c -> c.set(0));
  }

  public boolean allTradesProcessed(String calculationId, int newResponses) {
    return calculationTradesCounter(calculationId)
        .map(counter -> allTradesProcessed(counter, newResponses))
        .orElse(true);
  }

  public long updateFailureCount(String calculationId, int newFailures) {
    return calculationFailureCounter(calculationId)
        .map(
            counter -> {
              try {
                return counter.incrementAndGet(newFailures);
              } catch (Exception e) {
                return 0L;
              }
            })
        .orElse(0L);
  }

  private boolean allTradesProcessed(AtomicCounter counter, int newResponses) {
    if (newResponses == 0) {
      return false;
    }
    try {
      var unprocessedCount = counter.decrementAndGet(newResponses);
      if (unprocessedCount <= 0) {
        // TODO: consider removing this side effect as we've got explicit calls to
        // removeCalculation() anyway
        counter.closeQuietly();
        return true;
      }
      return false;
    } catch (Exception e) {
      return true;
    }
  }

  public long remainingUnprocessedTrades(String calculationId) {
    return calculationTradesCounter(calculationId)
        .flatMap(counter -> Checked.now(counter::get).toOptional())
        .map(count -> Math.max(0, count))
        .orElse(0L);
  }

  public long failedProcessedTrades(String calculationId) {
    return calculationFailureCounter(calculationId)
        .flatMap(counter -> Checked.now(counter::get).toOptional())
        .orElse(0L);
  }

  public void removeCalculation(String calculationId) {
    calculationTradesCounter(calculationId).ifPresent(AtomicCounter::closeQuietly);
    calculationFailureCounter(calculationId).ifPresent(AtomicCounter::closeQuietly);
  }

  private Optional<AtomicCounter> calculationTradesCounter(String calculationId) {
    return Checked.now(() -> dataGrid.getAtomicCounter(CALC_TRADES_COUNT + calculationId))
        .toEither()
        .leftMap(this::logError)
        .toOptional();
  }

  private Optional<AtomicCounter> calculationFailureCounter(String calculationId) {
    return Checked.now(() -> dataGrid.getAtomicCounter(CALC_TRADES_COUNT + calculationId + "-err"))
        .toEither()
        .leftMap(this::logError)
        .toOptional();
  }

  private Exception logError(Exception e) {
    log.warn("Error fetching counter", e);
    return e;
  }
}
