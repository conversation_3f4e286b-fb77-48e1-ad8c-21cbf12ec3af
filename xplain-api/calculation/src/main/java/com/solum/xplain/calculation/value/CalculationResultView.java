package com.solum.xplain.calculation.value;

import com.solum.xplain.calculation.CalculationResultScope;
import com.solum.xplain.calculation.CalculationResultStatus;
import com.solum.xplain.calculation.curves.value.CalculationResultsChartsView;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CalculationResultView {

  private String id;
  private LocalDateTime calculatedAt;
  private LocalDate stateDate;
  private LocalDate curveDate;
  private LocalDate valuationDate;
  private LocalDateTime recordDate;
  private PortfolioCalculationType calculationType;
  private MarketDataSourceType marketDataSourceType;
  private CalculationResultStatus calculationResultStatus;
  private CalculationResultScope calculationResultScope;
  private String calculatedBy;
  private String currency;
  private CalculationDiscountingType discountingType;
  private CalculationStrippingType strippingType;
  private String triangulationCcy;
  private String marketDataGroupId;
  private String marketDataGroupName;
  private String curveConfigurationId;
  private String curveConfigurationName;
  private String curveConfigurationCurveGroupName;
  private String fxCurveConfigurationId;
  private String fxCurveConfigurationName;
  private String fxCurveConfigurationCurveGroupName;
  private InstrumentPriceRequirements priceRequirements;
  private String portfolioId;
  private String externalPortfolioId;
  private String tradeId;
  private String externalTradeId;
  private String companyId;
  private String externalCompanyId;
  private String externalEntityId;
  private String entityId;
  private String savedCalculationId;
  private String name;
  private String comments;
  private CalculationResultsChartsView chartData;

  private Integer failedTradesCount;
  private Integer totalTradesCount;
  private String termOvernight;

  public CalculationResultView withChartData(CalculationResultsChartsView chartData) {
    setChartData(chartData);
    return this;
  }
}
