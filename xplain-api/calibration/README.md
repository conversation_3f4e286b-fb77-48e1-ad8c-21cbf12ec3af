# Xplain Calibration module.

## Overview

This module contains the calibration logic for all the instrument definitions based on curve configuration.

TODO(SXSD-10325): Enhance the documentation, this is pretty much an under construction README.md 

## Curve Calibration

## Presentation & Theory

[Presentation deck](https://solumfinancial.sharepoint.com/:p:/s/SXSD/EX0HLta9ikdNs4IxihYnYtsBdexg_As9undVqZYuk_WRIw?e=OiBQLx)

[Data set](https://solumfinancial.sharepoint.com/:f:/s/SXSD/EnltceNforlPpAK6YTFUMnEBgHkjT2nSMsEuBS9kwsUg3Q?e=HdTuFh)

## Code pointers.

### Offshore curves

Offshore / Onshore curves are resolved by AbstractDiscountableItemCurrencyResolver through a final marked method.
You'll find the logic in the method matches the description in the presentation deck.
- Offshore currency is always USD.
- Otherwise, delegate to implementor. (CurveDiscountableCurrencyResolver or DiscountablePortfolioItemCurrencyResolver)

### Calibration at Curve Configuration Level

Trades are generated by the class CalibrationTradesGenerator (when clicking on the "Generate portfolios")

Otherwise, trades are generated by extending OG. 
They are used in RatesCurveCalibrator, have to dig in how OG extension works, but these trades are returned by the curve node class.
`CurveNode.resolvedTrade()`

### Calibration code path

### Step 1: For each calibration group: Build discounting groups.

There are no concept of portfolio or sub-portfolio, all portfolio items are processed as they come.
It might come into play for the application configuration needed, but once fetch, they all go through `withItem`.

Each portfolio item is reduced to its definition of a DiscountableItem.

`withItem` will determine the key of the discounting group and add or merge to an existing DiscountingGroup.
An `DiscountingGroup` is built on a key and `DiscountableItemRequirements`.

All DiscountingGroupsBuilder are maintaining an internal map of discounting groups.

Keys are build based on the DiscountableItem
There are 2 types of keys:
- CurrencyDiscountingGroupKey (used by `CsaDiscountingGroupsBuilder`, `LocalCcyDiscountingGroupsBuilder`, `SingleCcyDiscountingGroupsBuilder`)
- IndexDiscountingGroupKey (used by `SingleDiscountingGroupsBuilder`)

#### IndexDiscountingGroupKey

`IndexDiscountingGroupKey` has the index resolved in `PortfolioItemDiscountIndexResolver.resolveIndex` (or `CurveDiscountableIndexResolver.resolveIndex` but the later one is likely when calibration is done at curve configuration level, not portfolio level).

The index part of the Key building can be found in each implementation of `PortfolioItemDiscountingGroupSpecification` (11 implementations)
The key being a `FloatingRateIndex`. It has a currency attached to it.

IndexDiscountingGroupKey = FloatingRateIndex + ClearingHouse (from DiscountableItemRequirements)

#### CurrencyDiscountingGroupKey

`SingleCcyDiscountingGroupsBuilder` will use its predefined currency + Offshore + ClearingHouse as a Key

`LocalCcyDiscountingGroupsBuilder` will use the currency attached to the item (through `AbstractDiscountableItemCurrencyResolver.resolveCurrency`, this is also where USD currency is enforced if the requirements are offshore).
For onshore, it will be based on TradeDiscountCurrencyResolver, which essentially delegates to `PortfolioItemDiscountingGroupSpecification.resolveCurrency`

`CsaDiscountingGroupsBuilder` will use the currency attached to the item, as all of them must implement `PortfolioItemDiscountingGroupSpecification.getCsaDiscountingGroup`.
Remember that the CSA discounting group is superseeding the default discounting group. A trade having a CSA currency, will not be keyed the same way as the exact same trade without CSA.

#### The case of OisLocalCcySingleFallbackDiscountingGroupsBuilder

This group builder is composite delegator to either a `LocalCcyDiscountingGroupsBuilder` (if it supports the currency) or to a `SingleDiscountingGroupsBuilder` as a fallback.

#### The process of merging discounting groups

When multiple items match the same key, their groups are merged (`AbstractDiscountingGroupsBuilder.merge`), however, which data is merged is left to implementor detail.

Merges are defined in each group, OisDiscountingGroup / CsaDiscountingGroup / SingleDiscountingGroup.

All the requirements are merged as a set union in `DiscountingGroupRequirements.merge`.
It includes :
- all foreign currencies.
- all credit names
- all caplet indices
- all swaption indices
- all required indices

/!\ This means that inherently, given a unique key, the resulting DiscountingGroupRequirements changes depending on the items.
It's not necessarily a problem, but it's worth noting that the output being used by OpenGamma. It might change the behavior of the engine.

All of them are from DiscountablePortfolioItem.requirements().
Merging is also individually validating each component of the key matches. (`validateMerge`)

They just fundamentally do the same thing, but the validation of the associated key is different and the signature is different.

### Step 2: For each calibration group: Build calibration bundles.

`CalibrationBundleResolver` will regroup all current information about curves, shift, valuation date and triangulation currency, which *might not be the discount currency*.

/!\ The calibration bundle resolver *will generate all possible entries possible from the curve configuration*.

It will then aggregate all the `DiscountingGroup` in a `DiscountingGroupsResults` (discount groups and fallbacks).
Note that in some cases, the resulting map may yield different types of discounting groups. Some results may eventually be dropped. (see `CsaResults.calibrationSubGroupsResolvers`)

The `CalibrationBundleResolver` will build CalibrationEntry and CalibrationDiscountSubGroup based on the input.
There are 2 types of CalibrationSubGroupsResolver
- OisCalibrationSubGroupsResolver
- SingleCalibrationSubGroupsResolver

They are essentially calling `CalibrationDiscountSubGroupsResolver` with different parameters.
The filtering of all the possible calibration entries is done in `CalibrationDiscountSubGroupsResolver.resolveSubGroups`.

In `CalibrationBundleResolver`, `CalibrationSubGroupsResolver.calibrationSubGroups` returns a list of discount sub groups which will be the bundle.

The bundle is composed of 
- the associated discounted currency used.
- list of sub groups.

Each subgroup is based on the discount curve as a `CalibrationEntry` for which we will need to find the requirements. (which are also `CalibrationEntry`).

### Step 3 : CalibrationEntryRequirementsResolver.requiredEntries

This method will trigger a recursive call to `additionalIndices` on each required indice.
This is because once an index curve is found it might need another index curve. This depends on the configuration of the curve.

It will trigger the recursive calls for both additional curve indices and the required calibration curve indices.
- additionalCurveIndices : additional indices required by the CalibrationEntry itself (the curve)
- requiredCalibrationIndices : the discount currency.

### Step 4 : CurveGroupCalibrationService.calibrateCurveGroup, OG calibration.

OG Calibration is triggered by in `CurveGroupCalibrationService.calibrateCurveGroup`, after all bundles are built.
The point of interest is the private function `calibrate`, where multiple data calibrations of curves are performed. 
Pay attention to `static` functions and `import`. OpenGamma calibrations are triggered by `static` functions.

Note that depending on the code path you are following, the OG Calibration might be triggered by calculation package rather than from the calibration package. 
If you search for call site of `CreditCurvesCalibration.calibrate`, you'll notice a call to it from `CurveGroupDataCalculator`.

/!\ There's no post OG Operations that are performed post OpenGamma calibration.