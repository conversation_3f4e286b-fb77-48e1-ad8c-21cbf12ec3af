package com.solum.xplain.calibration.discounting;

import static com.solum.xplain.calibration.discounting.DiscountingUtils.ensureOnlyOneCurve;
import static com.solum.xplain.core.curvegroup.curve.classifier.CurveType.INDEX_BASIS;
import static com.solum.xplain.core.curvegroup.curve.classifier.CurveType.IR_INDEX;
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static io.atlassian.fugue.Either.left;
import static org.slf4j.LoggerFactory.getLogger;

import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.solum.xplain.calibration.rates.CalibrationEntry;
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrency;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.springframework.lang.Nullable;

@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BaseDiscountSubGroupResolver {
  private static final Logger LOG = getLogger(BaseDiscountSubGroupResolver.class);

  private final OisConfigurations oisConfigurations;
  private final IndexBasedDiscountCurrency discountCcy;
  private final Boolean isOffshore;
  private final ClearingHouse clearingHouse;
  private final FloatingRateIndex discountingIndex;
  private final List<CalibrationEntry> allEntries;

  public static BaseDiscountSubGroupResolver baseDiscountGroupResolver(
      @NonNull LocalDate valuationDate,
      @NonNull IndexBasedDiscountCurrency discountCurrency,
      @Nullable Boolean isOffshore,
      @NonNull ClearingHouse clearingHouse,
      @Nullable FloatingRateIndex discountingIndex,
      @NonNull List<CalibrationEntry> allEntries) {
    return new BaseDiscountSubGroupResolver(
        OisConfigurations.of(valuationDate),
        discountCurrency,
        isOffshore,
        clearingHouse,
        discountingIndex,
        allEntries);
  }

  public Either<ErrorItem, CalibrationDiscountSubGroup> resolveBaseSubGroup() {
    return findBaseDiscountCurve().map(this::toSubGroup);
  }

  private CalibrationDiscountSubGroup toSubGroup(CalibrationEntry discountEntry) {
    LOG.debug("Resolved main discount curve {}", discountEntry);
    var requirementsResolver =
        CalibrationEntryRequirementsResolver.resolver(
            discountEntry, clearingHouse, Set.of(), allEntries);
    var discountCcyIndexCurves = requirementsResolver.requiredEntries();

    Set<CalibrationEntry> discountCalibrationEntries =
        ImmutableSet.<CalibrationEntry>builder()
            .add(discountEntry)
            .addAll(discountCcyIndexCurves)
            .build();

    return CalibrationDiscountSubGroup.newOfBase(
        discountCcy.getCurrency(), discountCalibrationEntries);
  }

  private Either<ErrorItem, CalibrationEntry> findBaseDiscountCurve() {
    Either<ErrorItem, CalibrationEntry> baseDiscountCurve;
    if (discountingIndex != null) {
      baseDiscountCurve = findIndexDiscountingCurve();
    } else {
      baseDiscountCurve = findOvernightIndexDiscountingCurve();
    }
    return baseDiscountCurve.map(e -> e.withResolvedDiscountCcy(discountCcy.getCurrency()));
  }

  private Either<ErrorItem, CalibrationEntry> findIndexDiscountingCurve() {
    return allEntries.stream()
        .filter(e -> e.index().filter(i -> i.equals(discountingIndex)).isPresent())
        .findAny()
        .map(Either::<ErrorItem, CalibrationEntry>right)
        .orElse(left(CALIBRATION_ERROR.entity("Curve not found for: " + discountingIndex)));
  }

  private Either<ErrorItem, CalibrationEntry> findOvernightIndexDiscountingCurve() {
    final Currency currency = discountCcy.getCurrency();
    List<CalibrationEntry> resolvedOisCurves =
        allEntries.stream()
            .filter(e -> e.isCurveType(IR_INDEX, INDEX_BASIS))
            .filter(e -> e.isOIS(currency))
            .filter(e -> e.matchesOffshore(BooleanUtils.isTrue(isOffshore)))
            .filter(e -> !e.getCurve().isOvernightTermCurve())
            .toList();

    return ensureOnlyOneCurve(
        oisConfigurations.imposeCurves(discountCcy, resolvedOisCurves), currency);
  }
}
