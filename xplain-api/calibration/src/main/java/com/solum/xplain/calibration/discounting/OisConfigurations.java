package com.solum.xplain.calibration.discounting;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.OvernightIndices;
import com.solum.xplain.calibration.rates.CalibrationEntry;
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies;
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrency;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.OISCurveImpositionConfiguration;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import com.solum.xplain.extensions.index.ExtendedOvernightIndices;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode
public class OisConfigurations {

  private static final IndexCurveConvention USD_SOFR_OIS_CURVE =
      ConventionalCurveConfigurations.lookupByOvernightIndex(OvernightIndices.USD_SOFR);

  private static final IndexCurveConvention USD_SOFR_TERM_OIS_3M_CURVE =
      ConventionalCurveConfigurations.lookupByOvernightTermIndex(
          ExtendedOvernightIndices.USD_SOFR_3M);

  private static final IndexCurveConvention USD_SOFR_TERM_OIS_6M_CURVE =
      ConventionalCurveConfigurations.lookupByOvernightTermIndex(
          ExtendedOvernightIndices.USD_SOFR_6M);

  private static final IndexCurveConvention USD_FED_FUND_OIS_CURVE =
      ConventionalCurveConfigurations.lookupByOvernightIndex(OvernightIndices.USD_FED_FUND);

  private static final IndexCurveConvention EUR_ESTR_OIS_CURVE =
      ConventionalCurveConfigurations.lookupByOvernightIndex(OvernightIndices.EUR_ESTR);

  private static final IndexCurveConvention EUR_EONIA_OIS_CURVE =
      ConventionalCurveConfigurations.lookupByOvernightIndex(OvernightIndices.EUR_EONIA);

  private static final Map<IndexBasedDiscountCurrency, IndexCurveConvention>
      OIS_CURVES_FOR_CURRENCY =
          Map.of(
              IndexBasedDiscountCurrencies.USD_FEDFUNDS,
              USD_FED_FUND_OIS_CURVE,
              IndexBasedDiscountCurrencies.USD_SOFR,
              USD_SOFR_OIS_CURVE,
              IndexBasedDiscountCurrencies.USD_SOFR_3M,
              USD_SOFR_TERM_OIS_3M_CURVE,
              IndexBasedDiscountCurrencies.USD_SOFR_6M,
              USD_SOFR_TERM_OIS_6M_CURVE,
              IndexBasedDiscountCurrencies.EUR_ESTR,
              EUR_ESTR_OIS_CURVE,
              IndexBasedDiscountCurrencies.EUR_EONIA,
              EUR_EONIA_OIS_CURVE);

  private static final List<OISCurveImpositionConfiguration> OIS_IMPOSITIONS_BY_TIME =
      List.of(
          OISCurveImpositionConfiguration.of(
              LocalDate.of(2020, 9, 16), USD_SOFR_OIS_CURVE, USD_FED_FUND_OIS_CURVE),
          OISCurveImpositionConfiguration.of(
              LocalDate.of(2020, 7, 27), EUR_ESTR_OIS_CURVE, EUR_EONIA_OIS_CURVE));

  private final LocalDate stateDate;

  public static OisConfigurations of(LocalDate valuationDate) {
    return new OisConfigurations(valuationDate);
  }

  public List<CalibrationEntry> imposeCurves(
      final IndexBasedDiscountCurrency discountCurrency,
      final List<CalibrationEntry> currencyOisCalibrationEntries) {
    if (isSpecificCurveEnforced(discountCurrency)) {
      return imposeForCurrency(
          currencyOisCalibrationEntries, discountCurrency, this::isRequiredCalibrationEntry);
    } else {
      return imposeCalibrationEntries(discountCurrency, currencyOisCalibrationEntries);
    }
  }

  private List<CalibrationEntry> imposeCalibrationEntries(
      IndexBasedDiscountCurrency discountCurrency,
      List<CalibrationEntry> currencyOisCurvesCalibrationEntries) {
    final var curveNames = curveNames(currencyOisCurvesCalibrationEntries);
    final var removableCurveNames =
        impositions(discountCurrency.getCurrency())
            .filter(i -> i.isApplicableForCurves(curveNames))
            .map(i -> i.getRemovableIndexCurve(stateDate).getName())
            .toList();
    return imposeForDate(
        currencyOisCurvesCalibrationEntries, removableCurveNames, CalibrationEntry::getCurveName);
  }

  public List<OvernightIndex> imposeIndices(
      final IndexBasedDiscountCurrency discountCurrency,
      final List<OvernightIndex> currencyOisIndices) {
    if (isSpecificCurveEnforced(discountCurrency)) {
      return imposeForCurrency(
          currencyOisIndices, discountCurrency, this::isRequiredOvernightIndex);
    } else {
      return imposeOvernightIndices(discountCurrency, currencyOisIndices);
    }
  }

  private List<OvernightIndex> imposeOvernightIndices(
      IndexBasedDiscountCurrency discountCurrency, List<OvernightIndex> currencyOisIndices) {
    var removableCurveNames =
        impositions(discountCurrency.getCurrency())
            .filter(i -> i.isApplicableForIndices(currencyOisIndices))
            .map(i -> i.getRemovableIndexCurve(stateDate).getIndex().getName())
            .toList();
    return imposeForDate(currencyOisIndices, removableCurveNames, OvernightIndex::getName);
  }

  private boolean isSpecificCurveEnforced(IndexBasedDiscountCurrency discountingCurrency) {
    return OIS_CURVES_FOR_CURRENCY.containsKey(discountingCurrency);
  }

  private <T> List<T> imposeForCurrency(
      List<T> entries,
      IndexBasedDiscountCurrency discountingCurrency,
      BiPredicate<T, IndexCurveConvention> isRequired) {
    var enforcedCurve = OIS_CURVES_FOR_CURRENCY.get(discountingCurrency);
    if (entries.isEmpty() || Objects.isNull(enforcedCurve)) {
      return entries;
    }
    return entries.stream().filter(e -> isRequired.test(e, enforcedCurve)).toList();
  }

  private boolean isRequiredOvernightIndex(
      OvernightIndex index, IndexCurveConvention requiredCurve) {
    return Objects.equals(index.getName(), requiredCurve.getIndex().getName());
  }

  private boolean isRequiredCalibrationEntry(
      CalibrationEntry entry, IndexCurveConvention requiredCurve) {
    return Objects.equals(entry.getCurveName(), requiredCurve.getName());
  }

  private <T> List<T> imposeForDate(
      List<T> entries, List<String> removableNames, Function<T, String> entryNameFn) {
    if (entries.size() <= 1) {
      return entries;
    }
    return entries.stream()
        .filter(e -> !CollectionUtils.containsAny(removableNames, entryNameFn.apply(e)))
        .toList();
  }

  private List<String> curveNames(List<CalibrationEntry> calibrationEntries) {
    return calibrationEntries.stream().map(CalibrationEntry::getCurveName).toList();
  }

  private Stream<OISCurveImpositionConfiguration> impositions(Currency currency) {
    return OIS_IMPOSITIONS_BY_TIME.stream().filter(c -> c.isApplicable(currency));
  }
}
