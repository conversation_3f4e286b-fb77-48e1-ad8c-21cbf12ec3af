package com.solum.xplain.calibration.settings;

import static java.util.stream.Collectors.toUnmodifiableMap;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.market.sensitivity.PointSensitivities;
import com.opengamma.strata.pricer.curve.CalibrationMeasures;
import com.opengamma.strata.pricer.curve.TradeCalibrationMeasure;
import com.opengamma.strata.pricer.index.DiscountingIborFutureTradePricer;
import com.opengamma.strata.pricer.index.HullWhiteIborFutureTradePricer;
import com.opengamma.strata.pricer.model.HullWhiteOneFactorPiecewiseConstantParametersProvider;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.fx.ResolvedFxSwapTrade;
import com.opengamma.strata.product.index.ResolvedIborFutureTrade;
import com.solum.xplain.core.settings.entity.ConvexityAdjustmentsSettings;
import com.solum.xplain.extensions.pricers.XplainDiscountingFxSwapProductPricer;
import com.solum.xplain.extensions.termdeposit.ResolvedTermOisFixingDepositTrade;
import com.solum.xplain.extensions.termdeposit.TermOisFixingDepositPricer;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.NonNull;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class MeasuresWithConvexityAdjustment {

  private static final DayCount DEFAULT_CONVEXITY_DC = DayCounts.ACT_365F;
  private static final ZoneId DEFAULT_CONVEXITY_ZONE = ZoneId.of("Europe/London");
  private static final String IBOR_FUTURE_PAR_SPREAD_MEASURE = "IborFutureParSpreadHullWhite";
  private static final String CALIBRATION_MEASURE_NAME = "ParSpreadWithAdjustedIborFuture";
  private static final String FX_SWAP_PAR_SPREAD_MEASURE = "FxSwapParSpreadDiscounting";
  private static final String TERM_OIS_FIXING_DEPOSIT_PAR_SPREAD_MEASURE =
      "TermOisFixingDepositParSpread";

  private final Map<Index, HullWhiteOneFactorPiecewiseConstantParametersProvider> convexityAdj;
  private final XplainDiscountingFxSwapProductPricer xplainDiscountingFxSwapProductPricer;
  private final TermOisFixingDepositPricer termOisFixingDepositPricer;

  public static MeasuresWithConvexityAdjustment ofConvexitySettings(
      @NonNull ConvexityAdjustmentsSettings settings,
      @NonNull LocalDate valuationDate,
      ReferenceData referenceData) {
    var vdTime = valuationDate.atStartOfDay(DEFAULT_CONVEXITY_ZONE);
    return new MeasuresWithConvexityAdjustment(
        settings.definitions().entrySet().stream()
            .collect(
                toUnmodifiableMap(
                    Map.Entry::getKey,
                    e ->
                        HullWhiteOneFactorPiecewiseConstantParametersProvider.of(
                            e.getValue(), DEFAULT_CONVEXITY_DC, vdTime))),
        new XplainDiscountingFxSwapProductPricer(referenceData),
        new TermOisFixingDepositPricer());
  }

  public CalibrationMeasures calibrationMeasures() {
    var iborFutureParSpreadHullWhite =
        TradeCalibrationMeasure.of(
            IBOR_FUTURE_PAR_SPREAD_MEASURE,
            ResolvedIborFutureTrade.class,
            this::resolveIborFutureParSpread,
            this::resolveIborFutureParSpreadSens);

    var fxSwapParSpread =
        TradeCalibrationMeasure.of(
            FX_SWAP_PAR_SPREAD_MEASURE,
            ResolvedFxSwapTrade.class,
            this::resolveFXSwapParSpread,
            this::resolveFxSwapParSpreadSens);

    var termOisFixingDepositParSpread =
        TradeCalibrationMeasure.of(
            TERM_OIS_FIXING_DEPOSIT_PAR_SPREAD_MEASURE,
            ResolvedTermOisFixingDepositTrade.class,
            this::resolveTermOisFixingDepositParSpread,
            this::resolveTermOisFixingDepositParSpreadSens);

    return CalibrationMeasures.of(
        CALIBRATION_MEASURE_NAME,
        TradeCalibrationMeasure.FRA_PAR_SPREAD,
        fxSwapParSpread,
        termOisFixingDepositParSpread,
        TradeCalibrationMeasure.IBOR_FIXING_DEPOSIT_PAR_SPREAD,
        iborFutureParSpreadHullWhite,
        TradeCalibrationMeasure.SWAP_PAR_SPREAD,
        TradeCalibrationMeasure.TERM_DEPOSIT_PAR_SPREAD);
  }

  private double resolveIborFutureParSpread(ResolvedIborFutureTrade t, RatesProvider p) {
    var adj = convexityAdj.get(t.getProduct().getIndex());
    if (adj != null) {
      return HullWhiteIborFutureTradePricer.DEFAULT.parSpread(t, p, adj, 0d);
    } else {
      return DiscountingIborFutureTradePricer.DEFAULT.parSpread(t, p, 0d);
    }
  }

  private double resolveFXSwapParSpread(ResolvedFxSwapTrade t, RatesProvider p) {
    return xplainDiscountingFxSwapProductPricer.parSpread(t.getProduct(), p);
  }

  private double resolveTermOisFixingDepositParSpread(
      ResolvedTermOisFixingDepositTrade t, RatesProvider p) {
    return termOisFixingDepositPricer.parSpread(t.getProduct(), p);
  }

  private PointSensitivities resolveIborFutureParSpreadSens(
      ResolvedIborFutureTrade t, RatesProvider p) {
    var adj = convexityAdj.get(t.getProduct().getIndex());
    if (adj != null) {
      return HullWhiteIborFutureTradePricer.DEFAULT.parSpreadSensitivityRates(t, p, adj);
    } else {
      return DiscountingIborFutureTradePricer.DEFAULT.parSpreadSensitivity(t, p);
    }
  }

  private PointSensitivities resolveTermOisFixingDepositParSpreadSens(
      ResolvedTermOisFixingDepositTrade t, RatesProvider p) {
    return termOisFixingDepositPricer.parSpreadSensitivity(t.getProduct(), p);
  }

  private PointSensitivities resolveFxSwapParSpreadSens(ResolvedFxSwapTrade t, RatesProvider p) {
    return xplainDiscountingFxSwapProductPricer.parSpreadSensitivity(t.getProduct(), p);
  }
}
