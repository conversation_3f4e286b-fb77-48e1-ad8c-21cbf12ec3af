package com.solum.xplain.core.company.repository

import static com.solum.xplain.core.teams.TeamBuilder.getTEAM_ID_2
import static com.solum.xplain.core.teams.TeamBuilder.teamWithExternalId
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.core.users.UserBuilder.userWithTeams

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.team.EntityTeamFilter
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.company.CompanyBuilder
import com.solum.xplain.core.company.CompanyLegalEntityBuilder
import com.solum.xplain.core.company.CompanyLegalEntityFilter
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.entity.CompanyLegalEntity
import com.solum.xplain.core.company.entity.CompanyLegalEntityIpvSettings
import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings
import com.solum.xplain.core.company.entity.IpvValuationProviders
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup
import com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvForm
import com.solum.xplain.core.company.events.CompanyArchived
import com.solum.xplain.core.company.form.CompanyLegalEntityCreateForm
import com.solum.xplain.core.company.form.CompanyLegalEntityUpdateForm
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.teams.Team
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.util.Pair
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class CompanyLegalEntityRepositoryTest extends IntegrationSpecification {

  static def TEAM_EXT_ID = "Team 1"

  @Resource
  MongoOperations operations

  @Resource
  CompanyLegalEntityRepository repository

  XplainPrincipal creator

  def setup() {
    def team = teamWithExternalId(TEAM_EXT_ID)
    creator = userWithTeams("creatorId", [team.id])
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
    operations.insert(team)
  }

  def cleanup() {
    operations.remove(new Query(), CompanyLegalEntity)
    operations.remove(new Query(), Team)
    operations.remove(new Query(), Company)
  }

  def "should create company entity"() {
    setup:
    def teamId = ObjectId.get()
    def companyId = "companyId"
    def company = new Company()
    company.id = companyId
    company.externalCompanyId = "externalCompanyId"
    operations.insert(company)

    when:
    def result = repository.createEntity(companyId,
      new CompanyLegalEntityCreateForm(
      name: "fund1",
      externalId: "eid",
      description: "aa",
      allowedTeamsForm: new AllowedTeamsForm(true, [teamId.toHexString()])))
    then:
    result.isRight()
    def loaded = operations.query(CompanyLegalEntity).firstValue()
    loaded.externalId == "eid"
    loaded.companyId == "companyId"
    loaded.vdkPrefix == "externalCompanyId_eid"
    loaded.externalCompanyId == "externalCompanyId"
    loaded.id == result.getOrNull().id
    loaded.name == "fund1"
    loaded.description == "aa"
    loaded.allowAllTeams
    loaded.teamIds == [teamId]
  }

  def "should archive company entity"() {
    setup:
    CompanyLegalEntity entity = CompanyLegalEntity.newOf("companyId")
    operations.insert(entity)
    when:
    repository.archiveEntity("companyId", entity.id)
    then:
    operations.findById(entity.id, CompanyLegalEntity).archived
  }

  def "should update company entity"() {
    setup:
    def entity = CompanyLegalEntity.newOf("companyId")
    entity.externalId = "EXT"
    def teamId = ObjectId.get()
    operations.insert(entity)
    when:
    def result = repository.updateEntity(
      "companyId",
      entity.id,
      new CompanyLegalEntityUpdateForm(name: "NEW",
      description: "NEW",
      allowedTeamsForm: new AllowedTeamsForm(true, [teamId.toHexString()]))

      )
    then:
    result.isRight()
    def loaded = operations.findById(entity.id, CompanyLegalEntity.class)
    loaded.id == entity.id
    loaded.externalId == "EXT"
    loaded.name == "NEW"
    loaded.description == "NEW"
    loaded.teamIds == [teamId]
    loaded.allowAllTeams
    loaded.auditLogs.size() == 1
  }

  def "should list company entities"() {
    setup:
    def team = new Team(name: "t1")
    operations.insert(team)
    CompanyLegalEntity entity = CompanyLegalEntity.newOf("companyId")
    entity.description = "description"
    entity.name = "name"
    entity.externalId = "eid"
    entity.teamIds = [team.id]
    operations.insert(entity)

    when:
    def result = repository.companyLegalEntitiesViews("companyId", CompanyLegalEntityFilter.notArchived())
    then:
    result.size() == 1
    result[0].name == "name"
    result[0].id == entity.id
    result[0].description == "description"
    result[0].externalId == "eid"
    result[0].teamIds == [team.id.toHexString()]
    result[0].teamNames == ["t1"]
  }

  def "should sort company entities"() {
    setup:
    def team = new Team(name: "t1")
    operations.insert(team)
    CompanyLegalEntity entityOne = CompanyLegalEntity.newOf("companyId")
    entityOne.description = "description"
    entityOne.name = "A"
    entityOne.externalId = "eid1"
    entityOne.teamIds = [team.id]
    CompanyLegalEntity entityTwo = CompanyLegalEntity.newOf("companyId")
    entityTwo.description = "description"
    entityTwo.name = "B"
    entityTwo.externalId = "eid2"
    entityTwo.teamIds = [team.id]
    operations.insertAll([entityOne, entityTwo])

    when:
    def result = repository.companyLegalEntitiesViews("companyId", CompanyLegalEntityFilter.notArchived(), Sort.by("name"))
    then:
    result.name == ["A", "B"]
  }

  def "should return company legal entity names view"() {
    setup:

    def company = new Company()
    company.externalCompanyId = "EXT"
    operations.insert(company)

    CompanyLegalEntity entityOne = CompanyLegalEntity.newOf(company.id)
    entityOne.externalId = "eid1"
    CompanyLegalEntity entityTwo = CompanyLegalEntity.newOf(company.id)
    entityTwo.externalId = "eid2"
    operations.insertAll([entityOne, entityTwo])

    when:
    def result = repository.companyLegalEntityNameViews()

    then:
    result.size() == 2
    result[0].entityId == entityOne.id
    result[0].externalId == entityOne.externalId
    result[0].companyId == entityOne.companyId
    result[0].companyExternalId == company.externalCompanyId

    result[1].entityId == entityTwo.id
    result[1].externalId == entityTwo.externalId
    result[1].companyId == entityTwo.companyId
    result[1].companyExternalId == company.externalCompanyId
  }

  def "should return company legal entity names view (with entity id filter)"() {
    setup:
    def company = new Company()
    company.externalCompanyId = "EXT"
    operations.insert(company)

    CompanyLegalEntity entityOne = CompanyLegalEntity.newOf(company.id)
    entityOne.externalId = "eid1"
    CompanyLegalEntity entityTwo = CompanyLegalEntity.newOf(company.id)
    entityTwo.externalId = "eid2"
    operations.insert(entityOne)
    var storedEntityTwo = operations.insert(entityTwo)

    when:
    def result = repository.companyLegalEntityNameViews(Set.of(storedEntityTwo.id))

    then:
    result.size() == 1
    result[0].entityId == entityTwo.id
    result[0].externalId == entityTwo.externalId
    result[0].companyId == entityTwo.companyId
    result[0].companyExternalId == company.externalCompanyId
  }

  def "should list companies entities"() {
    setup:
    def team = new Team(name: "t1")
    operations.insert(team)
    CompanyLegalEntity entity1 = CompanyLegalEntity.newOf("companyId")
    entity1.description = "description"
    entity1.name = "name"
    entity1.externalId = "eid"
    entity1.teamIds = [team.id]
    operations.insert(entity1)
    CompanyLegalEntity entity2 = CompanyLegalEntity.newOf("companyId2")
    entity2.description = "description"
    entity2.name = "name"
    entity2.externalId = "eid"
    entity2.teamIds = [team.id]
    operations.insert(entity2)
    CompanyLegalEntity entity3 = CompanyLegalEntity.newOf("companyId2")
    entity3.description = "description"
    entity3.name = "name"
    entity3.externalId = "eid"
    entity3.teamIds = [ObjectId.get()]
    operations.insert(entity3)

    def user = userWithTeams("id", [team.id])
    operations.insert(user)

    when:
    def result = repository.companiesLegalEntitiesViews(EntityTeamFilter.filter(user), ["companyId", "companyId2"])
    then:
    result.size() == 2
    result.id.contains(entity1.id)
    result.id.contains(entity2.id)
  }

  def "should list all entities"() {
    setup:
    CompanyLegalEntity entity1 = CompanyLegalEntity.newOf("companyId1")
    operations.insert(entity1)
    CompanyLegalEntity entity2 = CompanyLegalEntity.newOf("companyId2")
    operations.insert(entity2)
    CompanyLegalEntity entity3 = CompanyLegalEntity.newOf("companyId2")
    entity3.archived = true
    operations.insert(entity3)

    when:
    def result = repository.allLegalEntities()
    then:
    result.size() == 2
    result[0].id == entity1.id
    result[1].id == entity2.id
  }

  def "should return entity import views"() {
    setup:
    def compId = ObjectId.get().toHexString()
    def entId = ObjectId.get().toHexString()

    def vf = LocalDate.of(2020, 1, 1)
    def stateDate = BitemporalDate.newOf(vf.plusYears(1))

    def cConfigExtId = "cConfigId"
    def mdgName= "mdgName"
    def mdgName2= "mdgName2"
    def companyExtId = "companyExtId"
    def entityExtId = "entityExtId"
    def entityName = "entityName"
    def entityDescription = "entityDescription"
    def ipvGroupId = "ipvGroupId"
    def ipvGroupName = "ipvGroupName"
    def products = ipvSettingsProducts(ipvGroupId, ipvGroupName)

    def company = new Company()
    company.id = compId
    company.externalCompanyId = companyExtId

    def entity = new CompanyLegalEntity()
    entity.id = entId
    entity.externalId = entityExtId
    entity.companyId = compId
    entity.name = entityName
    entity.description = entityDescription
    entity.allowAllTeams = compAllowAllTeams
    entity.teamIds = compAllowAllTeams ? [] : creator.teams

    def valSettingsV1 = entityValSettingsVersion(vf.minusDays(1), vf, entId, cConfigExtId, mdgName)
    def valSettingsV2 = entityValSettingsVersion(vf, vf, entId, cConfigExtId, mdgName2)

    def ipvSettingsV1 = entityIpvSettingsVersion(vf.minusDays(1), vf.minusDays(1), entId, SlaDeadline.LDN_1600, null)
    def ipvSettingsV2 = entityIpvSettingsVersion(vf, vf, entId, SlaDeadline.LDN_1500, products)

    operations.insert(company)
    operations.insert(entity)
    operations.insert(valSettingsV1)
    operations.insert(valSettingsV2)
    operations.insert(ipvSettingsV1)
    operations.insert(ipvSettingsV2)

    when:
    def settings = repository.entityImportAggregatedViews(creator, stateDate).toList()

    then:
    settings.size() == 1
    settings[0].companyId == companyExtId
    settings[0].entityId == entityExtId
    settings[0].entityName == entityName
    settings[0].description == entityDescription
    settings[0].valuationDataGroup == ipvGroupName
    settings[0].slaDeadline == SlaDeadline.LDN_1500.toString()
    settings[0].curveConfiguration == cConfigExtId
    settings[0].marketDataGroup == mdgName2
    settings[0].allowAllTeams == compAllowAllTeams.toString()
    settings[0].teams == compTeamNames

    where:
    compAllowAllTeams | compTeamNames
    true              | []
    false             | [TEAM_EXT_ID]
  }

  def "should get company entity"() {
    setup:
    CompanyLegalEntity entity = CompanyLegalEntity.newOf("companyId")
    entity.description = "description"
    entity.name = "name"
    entity.externalId = "eid"
    operations.insert(entity)

    when:
    def result = repository.companyLegalEntityView("companyId", entity.id)
    then:
    result.isRight()
    def loaded = result.getOrNull()
    loaded.name == "name"
    loaded.id == entity.id
    loaded.description == "description"
    loaded.externalId == "eid"
  }

  def "should get excluded entity references for user"() {
    given: "some companies with different access rights"
    def user = creator
    def otherTeam = new ObjectId(TEAM_ID_2)
    def companyId = new ObjectId().toHexString()
    def excludedCompanyId = new ObjectId().toHexString()
    CompanyLegalEntity everyoneAllowed = new CompanyLegalEntityBuilder(companyId).build()
    CompanyLegalEntity userTeamAllowed1 = new CompanyLegalEntityBuilder(companyId).allowAllTeams(false).teamIds([user.teams[0], otherTeam]).build()
    CompanyLegalEntity userTeamAllowed2 = new CompanyLegalEntityBuilder(companyId).allowAllTeams(false).teamIds([otherTeam, user.teams[0]]).build()
    CompanyLegalEntity userTeamNotAllowed = new CompanyLegalEntityBuilder(companyId).allowAllTeams(false).teamIds([otherTeam, new ObjectId()]).build()
    CompanyLegalEntity companyAlreadyExcluded = new CompanyLegalEntityBuilder(excludedCompanyId).allowAllTeams(false).teamIds([otherTeam]).build()
    operations.insertAll([everyoneAllowed, userTeamAllowed1, userTeamAllowed2, userTeamNotAllowed, companyAlreadyExcluded])

    when:
    def result = repository.streamExcludedLegalEntityReferencesForUser(user, [excludedCompanyId])

    then:
    result*.entityId == [userTeamNotAllowed.id]
  }

  @Unroll
  def "should get user company entity view allowAll #allowAll isRight #isRight"() {
    setup:
    def company = new CompanyBuilder()
      .allowAllTeams(true)
      .build()
    operations.insert(company)

    def legalEntity = CompanyLegalEntity.newOf(company.id)
    legalEntity.teamIds = [legalEntityTeamId]
    legalEntity.allowAllTeams = allowAll

    operations.insert(legalEntity)

    def result = repository.userCompanyLegalEntityView(
      user("id"),
      company.id,
      legalEntity.id)

    expect:
    result.isRight() == isRight

    and: "team names should be loaded"
    if (isRight) {
      result.getOrNull().getView().teamNames == expectedTeamNames
    }

    where:
    legalEntityTeamId                        | allowAll | isRight | expectedTeamNames
    new ObjectId("000000000000000000000000") | false    | true    | ["Default Team"]
    new ObjectId("000000000000000000000001") | false    | false   | []
    new ObjectId("000000000000000000000001") | true     | true    | [] // omit missing teams (covers case where team is subsequently deleted)
  }

  def "should validate unique company entity name"() {
    setup:
    CompanyLegalEntity entity = CompanyLegalEntity.newOf("companyId")
    entity.description = "description"
    entity.name = "name"
    entity.externalId = "eid"

    operations.insert(entity)
    expect:
    !repository.existsByNameExcludingSelf("companyId", entity.id, "name")
    repository.existsByNameExcludingSelf("companyId", null, "name")
    !repository.existsByNameExcludingSelf("companyId", null, "rand")
  }

  def "should validate unique company external id"() {
    setup:
    CompanyLegalEntity entity = CompanyLegalEntity.newOf("companyId")
    entity.externalId = "EXTERNAL1"
    CompanyLegalEntity entity2 = CompanyLegalEntity.newOf("companyId")
    entity2.externalId = "ARCHIVED"
    entity2.archived = true
    operations.insertAll([entity, entity2])
    expect:
    repository.existsByExternalId("companyId", "EXTERNAL1")
    !repository.existsByExternalId("companyId2", "EXTERNAL1")
    !repository.existsByExternalId("companyId", "ARCHIVED")
    !repository.existsByExternalId("companyId", "RANDOM")
  }

  def "should archive company legal entity on company archive"() {
    setup:
    CompanyLegalEntity entity = CompanyLegalEntity.newOf("companyId")
    entity.archived = false

    CompanyLegalEntity entity2 = CompanyLegalEntity.newOf("companyId")
    entity2.archived = false

    CompanyLegalEntity entity3 = CompanyLegalEntity.newOf("companyId")
    entity3.archived = true

    operations.insertAll([entity, entity2, entity3])

    when:
    repository.onCompanyArchived(CompanyArchived.newOf(EntityId.entityId("companyId")))

    then:
    operations.findById(entity.id, CompanyLegalEntity.class).archived
    operations.findById(entity2.id, CompanyLegalEntity.class).archived
    operations.findById(entity3.id, CompanyLegalEntity.class).archived
  }

  def "should create vdkPrefix when importing entity"() {
    setup:
    def teamId = ObjectId.get()
    def companyId = ObjectId.get()

    when:
    def result = repository.createEntity(companyId.toHexString(),
      new CompanyLegalEntityCsvForm(
      companyId :companyId,
      companyExternalId: "externalCompanyId",
      externalId: "eid",
      description: "aa",
      allowedTeamsForm: new AllowedTeamsForm(true, [teamId.toHexString()])), BitemporalDate.newOfNow())

    then:
    result.isRight()
    def loaded = operations.query(CompanyLegalEntity).firstValue()
    loaded.vdkPrefix == "externalCompanyId_eid"
    loaded.externalCompanyId == "externalCompanyId"
  }

  def "should resolve company entity keys"() {
    setup:
    def entity1 = CompanyLegalEntity.newOf("companyId1")
    entity1.vdkPrefix = "prefix1"
    entity1.archived = false
    def entity2 = CompanyLegalEntity.newOf("companyId2")
    entity2.vdkPrefix = "prefix2"
    entity2.archived = false
    def entity3 = CompanyLegalEntity.newOf("companyId3")
    entity3.vdkPrefix = "prefix3"
    entity3.archived = true // Archived entity should be excluded
    operations.insertAll([entity1, entity2, entity3])

    def groupDataKeys = Set.of("prefix1", "prefix2", "prefix3")

    when:
    def result = repository.resolvedCompanyEntityKeys(groupDataKeys)

    then:
    result.size() == 2
    result.any { it.key() == "prefix1" && it.companyId() == "companyId1" && it.legalEntityId() == entity1.id }
    result.any { it.key() == "prefix2" && it.companyId() == "companyId2" && it.legalEntityId() == entity2.id }
    !result.any { it.key() == "prefix3" }
  }

  def "should return archived legal entities"() {
    setup:
    def companyId = "companyId"
    def entityId = ObjectId.get().toHexString()

    CompanyLegalEntity archivedEntity = CompanyLegalEntity.newOf(companyId)
    archivedEntity.id = entityId
    archivedEntity.archived = true

    CompanyLegalEntity activeEntity = CompanyLegalEntity.newOf(companyId)
    activeEntity.archived = false

    operations.insertAll([archivedEntity, activeEntity])

    when:
    def result = repository.archivedLegalEntities(companyId, entityId)

    then:
    result.size() == 1
    result[0].id == entityId
    result[0].archived
  }

  def "should update company entities count"() {
    setup:
    def company1 = new CompanyBuilder().build()
    def company2 = new CompanyBuilder().build()
    operations.insertAll([company1, company2])

    CompanyLegalEntity entity1 = CompanyLegalEntity.newOf(company1.id)
    CompanyLegalEntity entity2 = CompanyLegalEntity.newOf(company1.id)
    CompanyLegalEntity entity3 = CompanyLegalEntity.newOf(company2.id)
    CompanyLegalEntity archivedEntity = CompanyLegalEntity.newOf(company1.id)
    archivedEntity.archived = true

    operations.insertAll([entity1, entity2, entity3, archivedEntity])

    when:
    repository.updateCompanyEntitiesCountBulk([company1.id, company2.id])

    then:
    def updatedCompany1 = operations.findById(company1.id, Company)
    def updatedCompany2 = operations.findById(company2.id, Company)
    updatedCompany1.numberOfEntities == 2 // counts just non-archived
    updatedCompany2.numberOfEntities == 1
  }

  def "should find by companyId and externalIds"() {
    setup:
    def companyId1 = "company1"
    def companyId2 = "company2"
    def externalId1 = "ext1"
    def externalId2 = "ext2"

    def entity1 = CompanyLegalEntity.newOf(companyId1)
    entity1.externalId = externalId1
    def entity2 = CompanyLegalEntity.newOf(companyId2)
    entity2.externalId = externalId2
    def entity3 = CompanyLegalEntity.newOf(companyId1)
    entity3.externalId = "other"

    operations.insertAll([entity1, entity2, entity3])

    when:
    def pairs = [Pair.of(companyId1, externalId1), Pair.of(companyId2, externalId2)] as Set
    def result = repository.findByCompanyIdAndExternalIds(pairs).toList()

    then:
    result.size() == 2
    result.any { it.companyId == companyId1 && it.externalId == externalId1 }
    result.any { it.companyId == companyId2 && it.externalId == externalId2 }
  }

  static def entityValSettingsVersion(LocalDate recordDate, LocalDate validFrom, String entityId, String curveConfigExtId, String mdgExtId) {
    def valSettings = new CompanyLegalEntityValuationSettings()
    valSettings.entityId = entityId
    valSettings.state = State.ACTIVE
    valSettings.setMarketDataGroup(ValuationSettingsMarketDataGroup.marketDataGroup(ObjectId.get().toString(), mdgExtId))
    valSettings.setCurveConfiguration(EntityReference.newOf(ObjectId.get().toString(), curveConfigExtId))
    valSettings.recordDate = LocalDateTime.of(recordDate, LocalTime.NOON)
    valSettings.validFrom = validFrom
    return valSettings
  }

  static def entityIpvSettingsVersion(LocalDate recordDate, LocalDate validFrom, String entityId, SlaDeadline slaDeadline, List<IpvValuationProviders> products) {
    def ipvSettings = new CompanyLegalEntityIpvSettings()
    ipvSettings.setProducts(products)
    ipvSettings.entityId = entityId
    ipvSettings.state = State.ACTIVE
    ipvSettings.recordDate = LocalDateTime.of(recordDate, LocalTime.NOON)
    ipvSettings.validFrom = validFrom
    ipvSettings.slaDeadline = slaDeadline
    return ipvSettings
  }

  static def ipvSettingsProducts(String vdgId, String vdgName) {
    def entityRef = EntityReference.newOf(vdgId, vdgName)

    def provider = ipvProviderProduct(entityRef, CoreProductType.IRS)
    def providerXccy = ipvProviderProduct(null, CoreProductType.XCCY)
    def providerFx = ipvProviderProduct(entityRef, CoreProductType.FXOPT)

    return [provider, providerXccy, providerFx]
  }

  static def ipvProviderProduct(EntityReference ipvGroup, ProductType productType) {
    def provider = new IpvValuationProviders()
    provider.setProductType(productType)
    provider.setIpvDataGroup(ipvGroup)
    return provider
  }
}
