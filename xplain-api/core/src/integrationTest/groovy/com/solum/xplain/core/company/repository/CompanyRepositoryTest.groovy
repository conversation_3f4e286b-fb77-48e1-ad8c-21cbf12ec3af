package com.solum.xplain.core.company.repository

import static com.solum.xplain.core.teams.TeamBuilder.TEAM_ID_2
import static com.solum.xplain.core.teams.TeamBuilder.teamWithExternalId
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.core.users.UserBuilder.userWithTeams

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.team.EntityTeamFilter
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.company.CompanyBuilder
import com.solum.xplain.core.company.CompanyFilter
import com.solum.xplain.core.company.csv.CompanyUniqueKey
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.entity.CompanyIpvSettings
import com.solum.xplain.core.company.entity.CompanyValuationSettings
import com.solum.xplain.core.company.entity.IpvValuationProviders
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup
import com.solum.xplain.core.company.form.CompanyCreateForm
import com.solum.xplain.core.company.form.CompanyUpdateForm
import com.solum.xplain.core.company.value.CompanyView
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.teams.Team
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class CompanyRepositoryTest extends IntegrationSpecification {

  static def TEAM_EXT_ID = "Team 1"

  @Resource
  MongoOperations operations

  @Resource
  CompanyRepository repository

  XplainPrincipal creator

  def setup() {
    def team = teamWithExternalId(TEAM_EXT_ID)
    creator = userWithTeams("creatorId", [team.id])
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
    operations.insert(team)
  }

  def cleanup() {
    operations.remove(new Query(), Company)
    operations.remove(new Query(), Team)
  }

  def "should save new company"() {
    setup:
    def result = repository.createCompany(new CompanyCreateForm(
      name: "name",
      externalCompanyId: "externalCompanyId",
      description: "description",
      allowedTeamsForm: new AllowedTeamsForm(true, null)))
    expect:
    result.isRight()
    def company = operations.findById(result.getOrNull().id, Company)
    company.id == result.getOrNull().id
    company.externalCompanyId == "externalCompanyId"
    company.name == "name"
    company.description == "description"
    company.allowAllTeams
  }

  def "should update company"() {
    setup:
    def company = insertCompany()
    def result = repository.updateCompany(
      company.id,
      new CompanyUpdateForm(
      name: "UPDATED",
      description: "UPDATED",
      allowedTeamsForm: new AllowedTeamsForm(true, null)))
    expect:
    result.isRight()
    def updated = operations.findById(company.id, Company)
    updated.externalCompanyId != "UPDATED"
    updated.name == "UPDATED"
    updated.description == "UPDATED"
    updated.allowAllTeams
    updated.auditLogs.size() == 1
  }

  def "should get sorted companies by name and external company id"() {
    setup:
    def sort = Sort.by(CompanyView.Fields.name, CompanyView.Fields.externalCompanyId)
    def companyOne = new CompanyBuilder()
      .name("A1")
      .externalCompanyId("B1")
      .numberOfPortfolios(1)
      .build()
    def companyTwo = new CompanyBuilder()
      .name("A1")
      .externalCompanyId("B2")
      .numberOfPortfolios(1)
      .build()
    operations.insertAll([companyTwo, companyOne])

    when:
    def result = repository.companyViewList(EntityTeamFilter.filter(user("id"),), CompanyFilter.nonArchived(), sort)

    then:
    result.name == ["A1", "A1"]
    result.externalCompanyId == ["B1", "B2"]
  }

  def "should return count for matching companies"() {
    setup:
    def xplUser= user()
    def otherTeamId = new ObjectId("000000000000000000000001")

    def company1 = insertCompanyWithExtId("ext1")
    def company2 = insertCompanyWithExtId("ext2")
    def company3RestrictedAccess = new CompanyBuilder()
      .externalCompanyId("ext3")
      .allowAllTeams(false)
      .teamIds([xplUser.getTeams().get(0)])
      .build()
    def company4Inaccessible = new CompanyBuilder()
      .externalCompanyId("ext4")
      .allowAllTeams(false)
      .teamIds([otherTeamId])
      .build()

    operations.insert(company3RestrictedAccess)
    operations.insert(company4Inaccessible)

    def keys = Set.of(
      new CompanyUniqueKey("ext1"),
      new CompanyUniqueKey("ext3"),
      new CompanyUniqueKey("ext4"),
      new CompanyUniqueKey("extRandom"),
      )

    when:
    def result = repository.numberAccessibleCompaniesMatchingKeys(keys, teamFilter)

    then:
    result == resultsCnt

    where:
    teamFilter                      | resultsCnt
    EntityTeamFilter.emptyFilter()  | 3
    EntityTeamFilter.filter(user()) | 2
  }

  def "should get company list"() {
    setup:
    def company = insertCompany()
    def result = repository.companyViewList(EntityTeamFilter.filter(user("id")), CompanyFilter.nonArchived())
    expect:
    result.size() == 1
    result.get(0).id == company.id
    result.get(0).externalCompanyId == "externalCompanyId"
    result.get(0).name == "name"
    result.get(0).numberOfPortfolios == 1
  }

  def "should get company list with empty filter"() {
    setup:
    def company = insertCompany()
    def result = repository.companyViewList(EntityTeamFilter.emptyFilter(), CompanyFilter.nonArchived())
    expect:
    result.size() == 1
    result.get(0).id == company.id
    result.get(0).externalCompanyId == "externalCompanyId"
    result.get(0).name == "name"
    result.get(0).numberOfPortfolios == 1
  }

  def "should get company"() {
    setup:
    def company = insertCompany()
    def result = repository.companyView(company.id)
    expect:
    result.isRight()
    result.getOrNull().name == "name"
    result.getOrNull().externalCompanyId == "externalCompanyId"
    result.getOrNull().numberOfPortfolios == 1
    result.getOrNull().description == "description"
  }

  @Unroll
  def "should get user company view allowAll #allowAll isRight #isRight"() {
    setup:
    def company = new CompanyBuilder()
      .allowAllTeams(allowAll)
      .teamIds([companyTeamId])
      .build()
    operations.insert(company)
    def result = repository.userCompanyView(user("id"), company.id)

    expect:
    result.isRight() == isRight

    where:
    companyTeamId                            | allowAll | isRight
    new ObjectId("000000000000000000000000") | false    | true
    new ObjectId("000000000000000000000001") | false    | false
    new ObjectId("000000000000000000000001") | true     | true
  }

  def "should validate unique external company id"() {
    setup:
    def company = insertCompany()

    expect:
    repository.existsByNameIdExcludingSelf("name", null)
    !repository.existsByNameIdExcludingSelf("name2", null)
    !repository.existsByNameIdExcludingSelf("name", company.id)
    !repository.existsByNameIdExcludingSelf("name2", company.id)
  }

  def "should validate unique company external id"() {
    setup:
    Company company = new Company()
    company.externalCompanyId = "EXTERNAL1"
    Company company2 = new Company()
    company2.externalCompanyId = "ARCHIVED"
    company2.archived = true
    operations.insertAll([company, company2])
    expect:
    repository.existByExternalId("EXTERNAL1")
    !repository.existByExternalId("ARCHIVED")
    !repository.existByExternalId("RANDOM")
  }

  def "should return company import views"() {
    setup:
    def compId = ObjectId.get().toHexString()
    def vf = LocalDate.of(2020, 1, 1)
    def stateDate = BitemporalDate.newOf(vf.plusYears(1))

    def cConfigExtId = "cConfigId"
    def mdgName= "mdgName"
    def mdgName2= "mdgName2"
    def companyExtId = "companyExtId"
    def companyName = "companyName"
    def companyDescription = "companyDescription"
    def ipvGroupId = "ipvGroupId"
    def ipvGroupName = "ipvGroupName"
    def products = ipvSettingsProducts(ipvGroupId, ipvGroupName)

    def company = new Company()
    company.id = compId
    company.externalCompanyId = companyExtId
    company.name = companyName
    company.description = companyDescription
    company.allowAllTeams = compAllowAllTeams
    company.teamIds = compAllowAllTeams ? [] : creator.teams

    def valSettingsV1 = companySettingsVersion(vf.minusDays(1), vf, compId, cConfigExtId, mdgName)
    def valSettingsV2 = companySettingsVersion(vf, vf, compId, cConfigExtId, mdgName2)

    def ipvSettingsV1 = companyIpvSettingsVersion(vf.minusDays(1), vf.minusDays(1), compId, SlaDeadline.LDN_1600, null)
    def ipvSettingsV2 = companyIpvSettingsVersion(vf, vf, compId, SlaDeadline.LDN_1500, products)

    operations.insert(company)
    operations.insert(valSettingsV1)
    operations.insert(valSettingsV2)
    operations.insert(ipvSettingsV1)
    operations.insert(ipvSettingsV2)

    when:
    def settings = repository.companyImportAggregatedViews(creator, stateDate).toList()

    then:
    settings.size() == 1
    settings[0].companyId == companyExtId
    settings[0].companyName == companyName
    settings[0].description == companyDescription
    settings[0].valuationDataGroup == ipvGroupName
    settings[0].slaDeadline == SlaDeadline.LDN_1500.toString()
    settings[0].curveConfiguration == cConfigExtId
    settings[0].marketDataGroup == mdgName2
    settings[0].allowAllTeams == compAllowAllTeams.toString()
    settings[0].teams == compTeamNames

    where:
    compAllowAllTeams | compTeamNames
    true              | []
    false             | [TEAM_EXT_ID]
  }

  def insertCompany() {
    def company = new CompanyBuilder()
      .numberOfPortfolios(1)
      .build()
    operations.save(company)
  }

  def insertCompanyWithExtId(String extId) {
    def company = new CompanyBuilder()
      .numberOfPortfolios(1)
      .externalCompanyId(extId)
      .build()
    operations.save(company)
  }

  def "should update company portfolios counts"() {
    setup:
    def company = insertCompany()
    def company2 = operations.save(new CompanyBuilder().id(ObjectId.get().toHexString()).numberOfPortfolios(5).build())
    def expectedCount = 10
    def expectedCount2 = 20

    when:
    repository.updatePortfolioCounts([(company.id) : expectedCount, (company2.id) : expectedCount2])
    def result = operations.findById(company.id, Company)
    def result2 = operations.findById(company2.id, Company)

    then:
    result.numberOfPortfolios == expectedCount
    result2.numberOfPortfolios == expectedCount2
  }

  def "should get active company references"() {
    setup:
    Company company = new Company()
    company.externalCompanyId = "EXTERNAL1"
    Company company2 = new Company()
    company2.externalCompanyId = "ARCHIVED"
    company2.archived = true
    operations.insertAll([company, company2])
    def companyIds = [company.id, company2.id]

    when:
    def result = repository.companyReferences(companyIds)

    then:
    result.entityId == [company.id]
  }

  def "should get excluded company references for user"() {
    given: "some companies with different access rights"
    def user = creator
    def otherTeam = new ObjectId(TEAM_ID_2)
    Company everyoneAllowed = CompanyBuilder.company()
    Company userTeamAllowed1 = new CompanyBuilder().allowAllTeams(false).teamIds([user.teams[0], otherTeam]).build()
    Company userTeamAllowed2 = new CompanyBuilder().allowAllTeams(false).teamIds([otherTeam, user.teams[0]]).build()
    Company userTeamNotAllowed = new CompanyBuilder().allowAllTeams(false).teamIds([otherTeam, new ObjectId()]).build()
    operations.insertAll([everyoneAllowed, userTeamAllowed1, userTeamAllowed2, userTeamNotAllowed])

    when:
    def result = repository.streamExcludedCompanyReferencesForUser(user).toList()

    then:
    result*.entityId == [userTeamNotAllowed.id]
  }

  def "should find companies by external ids"() {
    given:
    def company1 = new CompanyBuilder().externalCompanyId("ext1").build()
    def company2 = new CompanyBuilder().externalCompanyId("ext2").build()
    def company3 = new CompanyBuilder().externalCompanyId("ext3").archived(true).build()
    operations.insertAll([company1, company2, company3])

    when:
    def result = repository.findByExternalIds(["ext1", "ext2", "ext3", "notfound"] as Set).toList()

    then:
    result.size() == 2
    result*.externalCompanyId.containsAll(["ext1", "ext2"])
    !result*.externalCompanyId.contains("ext3") // archived should not be returned
  }

  static def companySettingsVersion(LocalDate recordDate, LocalDate validFrom, String companyId, String curveConfigExtId, String mdgExtId) {
    def valSettings = new CompanyValuationSettings()
    valSettings.entityId = companyId
    valSettings.state = State.ACTIVE
    valSettings.setMarketDataGroup(ValuationSettingsMarketDataGroup.marketDataGroup(ObjectId.get().toString(), mdgExtId))
    valSettings.setCurveConfiguration(EntityReference.newOf(ObjectId.get().toString(), curveConfigExtId))
    valSettings.recordDate = LocalDateTime.of(recordDate, LocalTime.NOON)
    valSettings.validFrom = validFrom
    return valSettings
  }

  static def companyIpvSettingsVersion(LocalDate recordDate, LocalDate validFrom, String companyId, SlaDeadline slaDeadline, List<IpvValuationProviders> products) {
    def ipvSettings = new CompanyIpvSettings()
    ipvSettings.setProducts(products)
    ipvSettings.entityId = companyId
    ipvSettings.state = State.ACTIVE
    ipvSettings.recordDate = LocalDateTime.of(recordDate, LocalTime.NOON)
    ipvSettings.validFrom = validFrom
    ipvSettings.slaDeadline = slaDeadline
    return ipvSettings
  }

  static def ipvSettingsProducts(String vdgId, String vdgName) {
    def entityRef = EntityReference.newOf(vdgId, vdgName)

    def provider = ipvProviderProduct(entityRef, CoreProductType.IRS)
    def providerXccy = ipvProviderProduct(null, CoreProductType.XCCY)
    def providerFx = ipvProviderProduct(entityRef, CoreProductType.FXOPT)

    return [provider, providerXccy, providerFx]
  }

  static def ipvProviderProduct(EntityReference ipvGroup, ProductType productType) {
    def provider = new IpvValuationProviders()
    provider.setProductType(productType)
    provider.setIpvDataGroup(ipvGroup)
    return provider
  }
}
