package com.solum.xplain.core.curveconfiguration

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP
import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CDS
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_IBOR_SWAP
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofIrCurve
import static com.solum.xplain.core.market.MarketDataSample.STATE_DATE
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT
import static java.time.LocalDate.ofEpochDay
import static java.time.ZoneOffset.ofHours
import static org.springframework.data.mongodb.core.query.Criteria.where
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.solum.xplain.core.common.AuditContext
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationOverride
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationProviderOverride
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationSearchForm
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationUpdateForm
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView
import com.solum.xplain.core.curveconfiguration.value.MarketDataProviderForm
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CurveConfigurationRepositoryTest extends IntegrationSpecification {

  @Resource
  CurveConfigurationRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  private AuditContext audit() {
    return new AuditContext(AuditUser.of(user), LocalDate.of(2019, 1, 1).atStartOfDay())
  }

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), CurveGroup)
    operations.remove(new Query(), CurveConfiguration)
  }

  def "should insert configuration"() {
    setup:
    def group = curveGroup()
    operations.insert(group)
    def providerForm = new MarketDataProviderForm("P", "S")
    def form = new CurveConfigurationForm(
      name: "config",
      curveGroupId: group.id,
      instruments: [(CDS): providerForm],
      overrides: [],
      versionForm: new NewVersionFormV2("", VAL_DT, VAL_DT, KEEP)
      )

    when:
    def result = repository.insert(form)

    then:
    result.isRight()
    def loaded = operations.findOne(new Query(where("entityId").is(result.right().get().id)), CurveConfiguration.class)
    loaded.modifiedBy == audit().user()
    that loaded.modifiedAt.toEpochSecond(ofHours(0)),
      closeTo(LocalDateTime.now().toEpochSecond(ofHours(0)), 10)
    loaded.state == State.ACTIVE
    loaded.name == "config"
    loaded.comment == ""
    loaded.curveGroupId == group.id
    loaded.validFrom == VAL_DT
    loaded.instruments.size() == 1
    loaded.instruments[(CDS)].primary == "P"
    loaded.instruments[(CDS)].secondary == "S"
  }

  def "should update configuration"() {
    setup:
    def config = curveConf()
    operations.insert(config)

    def group = curveGroup()
    operations.insert(group)
    def providerForm = new MarketDataProviderForm("PP", "SS")
    def form = new CurveConfigurationUpdateForm(
      curveGroupId: group.id,
      instruments: [(CDS): providerForm],
      overrides: [],
      versionForm: new NewVersionFormV2("", VAL_DT, VAL_DT, KEEP)
      )

    when:
    def result = repository.update(config.entityId, config.getValidFrom(), form)

    then:
    result.isRight()
    def loaded = allCurveConfigsSorted(config.entityId)

    loaded.size() == 2
    loaded[0].name == "Configuration"
    loaded[1].modifiedBy == audit().user()
    that loaded[0].modifiedAt.toEpochSecond(ofHours(0)),
      closeTo(LocalDateTime.now().toEpochSecond(ofHours(0)), 10)
    loaded[1].curveGroupId == group.id
    loaded[1].validFrom == VAL_DT
    loaded[1].instruments.size() == 1
    loaded[1].instruments[(CDS)].primary == "PP"
    loaded[1].instruments[(CDS)].secondary == "SS"
  }

  def "should get configurations"() {
    setup:
    def sort = Sort.by(CurveConfigurationView.Fields.name)
    def group = curveGroup()
    operations.insert(group)

    def config = curveConf()
    config.setValidFrom(VAL_DT)
    config.setCurveGroupId(group.id)
    operations.insert(config)

    def configArchived = curveConf()
    configArchived.setValidFrom(VAL_DT)
    configArchived.setCurveGroupId(group.id)
    configArchived.setState(State.ARCHIVED)
    operations.insert(configArchived)

    when:
    def result = repository.curveConfigurationViews(STATE_DATE, active(), sort)

    then:
    result.size() == 1
    result[0].entityId == config.entityId
    result[0].recordDate != null
    result[0].modifiedBy == user.name
    result[0].modifiedAt != null
    result[0].validFrom == VAL_DT
    result[0].curveGroupId == group.id
    result[0].curveGroupName == group.name
    result[0].instruments.size() == 1
    result[0].instruments[(CDS)].primary == "P"
    result[0].instruments[(CDS)].secondary == "S"
    result[0].overrides.size() == 1
    result[0].overrides[0].priority == 1
    result[0].overrides[0].enabled
    result[0].overrides[0].instruments[(FIXED_IBOR_SWAP)].primary == "P"
    result[0].overrides[0].instruments[(FIXED_IBOR_SWAP)].secondary == "S"
    result[0].overrides[0].instruments[(FIXED_IBOR_SWAP)].assetNames == ["EUR"]
  }

  def "should get single instrument resolver"() {
    setup:
    def config = setupCurveConf()
    when:
    def result = repository.curveConfigInstrResolver(STATE_DATE, config.getEntityId())

    then:
    result.isRight()
    def loaded = result.getOrNull()
    loaded.curveGroupId == "curveGroupId"
    loaded.instruments.size() == 1
    loaded.instruments["CDS"].primary == "P"
    loaded.instruments["CDS"].secondary == "S"
    loaded.resolveProvider(ofIrCurve("EUR", "EUR", FIXED_IBOR_SWAP, "3M", "3M", "k", "name"))
      .orElse(null).primary == "P"
    loaded.resolveProvider(ofIrCurve("EUR", "EUR", FIXED_IBOR_SWAP, "3M", "3M", "k", "name"))
      .orElse(null).secondary == "S"
  }

  def "should get all configuration instrument active resolvers"() {
    setup:
    setupCurveConf()
    def configArchived = curveConf()
    configArchived.setValidFrom(VAL_DT)
    configArchived.setCurveGroupId("groupId")
    configArchived.setState(State.ARCHIVED)
    operations.insert(configArchived)

    when:
    def result = repository.curveConfigInstrResolvers(STATE_DATE)
    then:
    result.size() == 1
    result[0].getCurveGroupId() != "groupId"
  }

  def "should get future versions"() {
    setup:
    def conf = setupCurveConf()

    when:
    def result = repository.futureVersions(new CurveConfigurationSearchForm(conf.name, ofEpochDay(0)))
    then:
    result.dates.size() == 1
    result.dates[0] == VAL_DT
  }

  def "should get configurations names for curve group"() {
    setup:
    def group = curveGroup()
    operations.insertAll([group])

    def config = curveConf()
    config.setValidFrom(VAL_DT)
    config.setName("name")
    config.setCurveGroupId(group.id)
    operations.insert(config)

    when:
    def result = repository.curveConfigForCurveGroupViews(LocalDate.now(), group.getId())

    then:
    result.size() == 1
    result[0].id == config.entityId
    result[0].curveGroupId == group.id
    result[0].name == "name"
  }

  def "should get configurations versions"() {
    setup:
    def config = setupCurveConf()

    when:
    def result = repository.versions(config.entityId)
    then:
    result.size() == 3
    result[0].validFrom == VAL_DT
    result[1].validFrom == ofEpochDay(0)
    result[2].validFrom == ofEpochDay(0)
  }

  def "should get configuration single"() {
    setup:
    def config = setupCurveConf()

    when:
    def result = repository.getView(config.entityId, BitemporalDate.newOf(VAL_DT))

    then:
    result.isRight()
    result.right().get().validFrom == VAL_DT
  }

  def "should delete configuration"() {
    setup:
    def config = setupCurveConf()

    when:
    def result = repository.delete(config.entityId, config.getValidFrom())

    then:
    result.isRight()
    def loaded = allCurveConfigsSorted(config.getEntityId())
    loaded.size() == 4
    loaded[0].state == State.ACTIVE
    loaded[1].state == State.ACTIVE
    loaded[2].state == State.ACTIVE
    loaded[3].state == State.DELETED
  }

  def "should archive config"() {
    setup:
    def config = setupCurveConf()

    when:
    def result = repository.archive(
      config.entityId,
      VAL_DT,
      new ArchiveEntityForm(new NewVersionFormV2("ARCHIVE", VAL_DT, VAL_DT, KEEP)))

    then:
    result.isRight()
    def loaded = allCurveConfigsSorted(result.getOrNull().id)
    loaded.size() == 4
    loaded[0].state == State.ACTIVE
    loaded[1].state == State.ACTIVE
    loaded[2].state == State.ACTIVE
    loaded[3].state == State.ARCHIVED
    loaded[3].comment == "ARCHIVE"
  }

  def "should check if config exists by id"() {
    setup:
    def config = curveConf()
    operations.insert(config)

    expect:
    repository.validCurveConfigurationId(config.entityId)
  }

  def "should check if config does not exist by non existant id"() {
    expect:
    !repository.validCurveConfigurationId(new ObjectId().toHexString())
  }

  def "getViews returns map of entityId to Either with CurveConfigurationView for inserted entities"() {
    given:
    // Insert required CurveGroup
    def group = curveGroup()
    operations.insert(group)

    // Insert CurveConfigurations linked to the group
    def config1 = CurveConfiguration.newOf()
    config1.setEntityId("id1")
    config1.setName("Config1")
    config1.setCurveGroupId(group.id)
    config1.setValidFrom(VAL_DT)
    config1.setInstruments([(CDS): new MarketDataProviders(primary: "P1", secondary: "S1")])
    operations.insert(config1)

    def config2 = CurveConfiguration.newOf()
    config2.setEntityId("id2")
    config2.setName("Config2")
    config2.setCurveGroupId(group.id)
    config2.setValidFrom(VAL_DT)
    config2.setInstruments([(CDS): new MarketDataProviders(primary: "P2", secondary: "S2")])
    operations.insert(config2)

    def config3 = CurveConfiguration.newOf()
    config2.setEntityId("id3")
    config2.setName("Config3")
    config2.setCurveGroupId(group.id)
    config2.setValidFrom(VAL_DT)
    config2.setInstruments([(CDS): new MarketDataProviders(primary: "P2", secondary: "S2")])
    operations.insert(config3)

    def stateDate = BitemporalDate.newOf(VAL_DT)

    when:
    def result = repository.getViews(["id1", "id2"] as Set, stateDate)

    then:
    result.size() == 2
    result["id1"].isRight()
    result["id2"].isRight()
    CurveConfigurationView curveConfigurationView1 = result["id1"].right().get()
    CurveConfigurationView curveConfigurationView2 = result["id2"].right().get()
    curveConfigurationView1.name == "Config1"
    curveConfigurationView2.name == "Config2"
    curveConfigurationView1.curveGroupId == group.id
    curveConfigurationView2.curveGroupId == group.id
  }

  private List<CurveConfiguration> allCurveConfigsSorted(String entityId) {
    operations
      .find(new Query(where("entityId").is(entityId)), CurveConfiguration.class)
      .stream()
      .sorted(Comparator.comparing({ c -> c.getRecordDate() }))
      .toList()
  }

  private CurveConfiguration setupCurveConf() {
    def config = curveConf()
    operations.insert(config)
    def configMinor = curveConf()
    configMinor.setEntityId(config.getEntityId())
    configMinor.setComment("latest")
    operations.insert(configMinor)
    def configAnotherVersion = curveConf()
    configAnotherVersion.setEntityId(config.getEntityId())
    configAnotherVersion.setValidFrom(VAL_DT)
    operations.insert(configAnotherVersion)
    config
  }

  private static CurveConfiguration curveConf() {
    def curveConf = CurveConfiguration.newOf()
    curveConf.setValidFrom(ofEpochDay(0))
    curveConf.setName("Configuration")
    curveConf.setCurveGroupId("curveGroupId")
    curveConf.setInstruments([(CDS): new MarketDataProviders(primary: "P", secondary: "S")])
    curveConf.setOverrides([
      new CurveConfigurationOverride(
      priority: 1,
      instruments: [(FIXED_IBOR_SWAP): new CurveConfigurationProviderOverride(primary: "P", secondary: "S", assetNames: ["EUR"])],
      enabled: true)
    ])
    curveConf.setRecordDate(STATE_DATE.recordDate.minusSeconds(1))
    curveConf
  }
}
