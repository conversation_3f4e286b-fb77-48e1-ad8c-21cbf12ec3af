package com.solum.xplain.core.curvegroup.ratefx

import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE
import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static java.math.BigDecimal.ONE
import static java.time.LocalDate.now
import static java.time.LocalDate.parse
import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesBuilder
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNode
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesForm
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeForm
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesView
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.users.UserBuilder
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class CurveGroupFxRatesRepositoryTest extends IntegrationSpecification {
  static def STATE_DATE = BitemporalDate.newOf(now())
  static def VALID_FROM = parse("2020-01-01")
  static def FUTURE_VALID_FROM = parse("2100-01-01")
  static def EUR_USD_NODE_FORM = new CurveGroupFxRatesNodeForm(domesticCurrency: "EUR", foreignCurrency: "USD")

  @Resource
  CurveGroupFxRatesRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")


  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), MarketDataGroup)
    operations.remove(new Query(), CurveGroupFxRates)
    operations.remove(new Query(), CurveGroup)
  }

  def "should create rates"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new CurveGroupFxRatesForm(nodes: [EUR_USD_NODE_FORM],
    versionForm: NewVersionFormV2.newDefault())

    when:
    def result = repository.createRates(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    // New major ROOT version
    assertAutofilledFields(loaded[0])
    assertNodes(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id
  }

  def "should insert rates with archived from ROOT date"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new CurveGroupFxRatesForm(nodes: [EUR_USD_NODE_FORM],
    versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).build())

    when:
    def result = repository.createRates(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New major version
    assertAutofilledFields(loaded[0])
    assertNodes(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id

    // New major ROOT ARCHIVED version
    assertAutofilledFields(loaded[1])
    assertNodes(loaded[1], form)
    loaded[1].state == State.ARCHIVED
    loaded[1].validFrom == ROOT_DATE
    loaded[1].comment == form.versionForm.comment
    loaded[1].entityId == group.id
  }

  @Unroll
  def "should update on insert if rates for group already exists with state #initialState"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .state(initialState)
      .entityId(group.id)
      .build()
    operations.insert(rates)

    def form = new CurveGroupFxRatesForm(nodes: [EUR_USD_NODE_FORM], //Updated,
    versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).stateDate(VALID_FROM).build())

    when:
    def result = repository.createRates(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New major version
    assertAutofilledFields(loaded[0])
    assertNodes(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id

    // Initial major ROOT version
    rates.valueEquals(loaded[1])

    where:
    initialState << [State.ACTIVE, State.ARCHIVED, State.DELETED]
  }

  def "should update rates"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .build()
    operations.insert(rates)

    def form = new CurveGroupFxRatesForm(nodes: [EUR_USD_NODE_FORM], //Updated,
    versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).comment("Comment").build())

    when:
    def result = repository.updateRates(group.id, rates.validFrom, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New minor version
    assertAutofilledFields(loaded[0])
    assertNodes(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id

    // Initial major ROOT version
    rates.valueEquals(loaded[1])
  }

  def "should update rates and remove future versions"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .build()
    operations.insert(rates)

    def ratesInFuture = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insert(ratesInFuture)

    def form = new CurveGroupFxRatesForm(nodes: [EUR_USD_NODE_FORM], //Updated
    versionForm: NewVersionFormV2.builder()
    .validFrom(VALID_FROM)
    .futureVersionsAction(FutureVersionsAction.DELETE)
    .build())
    when:
    def result = repository.updateRates(group.id, rates.validFrom, form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 4

    // Future Major (DELETED)
    loaded[0].validFrom == FUTURE_VALID_FROM
    loaded[0].state == State.DELETED

    // Future major
    loaded[1].validFrom == FUTURE_VALID_FROM
    loaded[1].state == State.ACTIVE

    // New major
    loaded[2].validFrom == VALID_FROM
    loaded[2].state == State.ACTIVE

    // Initial major (ROOT)
    loaded[3].validFrom == ROOT_DATE
    loaded[3].state == State.ACTIVE
  }

  def "should not update rates when no changes"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .build()
    operations.insert(rates)

    def form = new CurveGroupFxRatesForm(nodes: [], // as in rates
    versionForm: NewVersionFormV2.builder().validFrom(rates.validFrom).build())

    when:
    def result = repository.updateRates(group.id, rates.validFrom, form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    loaded[0].valueEquals(rates)
  }

  def "should DELETE FX rates"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .build()
    operations.insert(rates)

    when:
    def result = repository.deleteRates(group.id, rates.validFrom)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    loaded[0].validFrom == rates.validFrom
    loaded[0].state == State.DELETED

    loaded[1].validFrom == rates.validFrom
    loaded[1].state == State.ACTIVE
  }

  def "should fail delete FX rates with status DELETED"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .state(State.DELETED)
      .build()
    operations.insert(rates)

    when:
    def result = repository.deleteRates(group.id, rates.validFrom)

    then:
    result.isLeft()
    def error = result.left().get() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED

    def loaded = allSortedValidFromDesc(rates.entityId)
    loaded.size() == 1

    loaded[0].validFrom == rates.validFrom
    loaded[0].state == State.DELETED
  }

  def "should load active rates"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .nodes([new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")] as Set)
      .build()
    operations.insert(rates)

    when:
    def result = repository.getActiveRates(group.id, STATE_DATE)

    then:
    result.isRight()

    def loaded = result.right().get() as CurveGroupFxRates
    loaded.valueEquals(rates)
  }

  def "should load active rates view"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .nodes([new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")] as Set)
      .build()
    operations.insert(rates)

    when:
    def result = repository.getActiveRatesView(group.id, STATE_DATE)

    then:
    result.isRight()

    def view = result.right().get() as CurveGroupFxRatesView
    view.validFrom == rates.validFrom
    view.state == State.ACTIVE
    view.entityId == group.id
    view.comment == rates.comment
    view.modifiedBy == user.name
    view.modifiedAt != null
    view.recordDate != null
    view.numberOfFxRates == rates.nodes.size()
  }

  def "should load rates versions"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .build()
    operations.insert(rates)

    def ratesInFuture = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .nodes([new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")] as Set)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insert(ratesInFuture)

    when:
    def result = repository.getRatesVersionViews(group.id)

    then:
    result.size() == 2
    // Future major
    result[0].validFrom == ratesInFuture.validFrom
    result[0].state == ratesInFuture.state
    result[0].entityId == ratesInFuture.entityId
    result[0].comment == ratesInFuture.comment
    result[0].modifiedBy == user.name
    result[0].modifiedAt != null
    result[0].recordDate != null
    result[0].numberOfFxRates == ratesInFuture.nodes.size()

    // Initial major (ROOT)
    result[1].validFrom == rates.validFrom
    result[1].state == rates.state
    result[1].entityId == rates.entityId
    result[1].comment == rates.comment
    result[1].modifiedBy == user.name
    result[1].modifiedAt != null
    result[1].recordDate != null
    result[1].numberOfFxRates == rates.nodes.size()
  }

  def "should load rates future versions dates list"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder().entityId(group.id).build()
    def ratesInFuture = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .state(State.ARCHIVED)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insertAll([rates, ratesInFuture])

    when:
    def result = repository.getFutureVersions(group.id, rates.validFrom)

    then:
    result.dates == [ratesInFuture.validFrom]
  }

  def "should load rates nodes count"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .nodes([new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")] as Set)
      .build()
    operations.insert(rates)

    when:
    def result = repository.getRatesNodesCount(CurveGroupEntryFilter.singleGroup(now(), group.id))

    then:
    result.size() == 1
    result[0].curveGroupId == group.id
    result[0].count == 1
  }

  def "should load rates nodes"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def node1 = new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")
    def node2 = new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "GBP")
    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .nodes([node1, node2] as Set)
      .build()
    operations.insert(rates)

    when:
    def result = repository.getRatesNodes(group.id, STATE_DATE)

    then:
    result.size() == 2
    result.contains(node1)
    result.contains(node2)
  }

  def "should load rates nodes values views"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def node = new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")
    def rates = new CurveGroupFxRatesBuilder()
      .entityId(group.id)
      .nodes([node] as Set)
      .build()
    operations.insert(rates)

    when:
    def result = repository.getRatesNodesValuesViews(group.id, now(), [(node.key): new CalculationMarketValueFullView(value: ONE)])

    then:
    result.versionDate == rates.validFrom
    result.list.size() == rates.nodes.size()

    result.list[0].domesticCurrency == "EUR"
    result.list[0].foreignCurrency == "USD"
    result.list[0].value == ONE
  }

  def "should return sorted rates"() {
    setup:

    def node1 = new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")
    def node2 = new CurveGroupFxRatesNode(domesticCurrency: "AED", foreignCurrency: "PLN")
    def node3 = new CurveGroupFxRatesNode(domesticCurrency: "USD", foreignCurrency: "GBP")
    def node4 = new CurveGroupFxRatesNode(domesticCurrency: "CAD", foreignCurrency: "CHF")
    def node5 = new CurveGroupFxRatesNode(domesticCurrency: "AUD", foreignCurrency: "NZD")
    def node6 = new CurveGroupFxRatesNode(domesticCurrency: "AUD", foreignCurrency: "BGN")

    operations.insert(new CurveGroupFxRatesBuilder()
      .nodes([node1, node2, node3, node4, node5, node6] as Set)
      .build())

    when:
    def result = repository.getRatesNodesValuesViews("groupId", now(),
      [("EUR/USD"): new CalculationMarketValueFullView(value: 1),
        ("AED/PLN"): new CalculationMarketValueFullView(value: 2),
        ("USD/GBP"): new CalculationMarketValueFullView(value: 3),
        ("CAD/CHF"): new CalculationMarketValueFullView(value: 4),
        ("AUD/NZD"): new CalculationMarketValueFullView(value: 5)])

    then:
    result.list.size() == 6
    result.list[0].key == "EUR/USD"
    result.list[0].value == 1
    result.list[1].key == "AUD/BGN"
    result.list[1].value == null
    result.list[2].key == "AUD/NZD"
    result.list[2].value == 5
    result.list[3].key == "USD/GBP"
    result.list[3].value == 3
    result.list[4].key == "CAD/CHF"
    result.list[4].value == 4
    result.list[5].key == "AED/PLN"
    result.list[5].value == 2
  }

  def "should find active rates for given group IDs and state date"() {
    given:
    def groupId1 = "group1"
    def groupId2 = "group2"
    def missingGroupId = "missing"
    def stateDate = BitemporalDate.newOfNow()

    def rates1 = new CurveGroupFxRatesBuilder().entityId(groupId1).build()
    def rates2 = new CurveGroupFxRatesBuilder().entityId(groupId2).build()
    operations.insertAll([rates1, rates2])

    when:
    def result = repository.findActiveRates([groupId1, groupId2, missingGroupId] as Set, stateDate)

    then:
    result.size() == 3
    result[groupId1].isRight()
    result[groupId2].isRight()
    result[missingGroupId].isLeft()
    result[groupId1].right().get().entityId == groupId1
    result[groupId2].right().get().entityId == groupId2
    result[missingGroupId].left().get().reason == Error.OBJECT_NOT_FOUND
  }

  def static assertNodes(CurveGroupFxRates rates, CurveGroupFxRatesForm form) {
    rates.nodes.size() == (form.nodes == null ? 0 : form.nodes.size())
    rates.nodes.each { node ->
      null != form.getNodes().find { nodeForm ->
        nodeForm.with {
          domesticCurrency == node.domesticCurrency
          foreignCurrency == node.foreignCurrency
        }
      }
    }
  }

  def assertAutofilledFields(CurveGroupFxRates rates) {
    rates.id != null
    rates.recordDate != null
    rates.modifiedAt != null
    rates.modifiedBy != null
    rates.modifiedBy.name == user.name
  }

  private List<CurveGroupFxRates> allSortedValidFromDesc(String entityId) {
    operations
      .query(CurveGroupFxRates).matching(query(where("entityId").is(entityId)).with(Sort.by(DESC, "validFrom", "recordDate")))
      .all()
  }
}
