package com.solum.xplain.core.classifiers;

import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.deposit.type.IborFixingDepositConvention;
import com.opengamma.strata.product.deposit.type.TermDepositConvention;
import com.opengamma.strata.product.fra.type.FraConvention;
import com.opengamma.strata.product.fx.type.FxSwapConvention;
import com.opengamma.strata.product.index.type.IborFutureContractSpec;
import com.opengamma.strata.product.swap.type.FixedIborSwapConvention;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention;
import com.opengamma.strata.product.swap.type.IborIborSwapConvention;
import com.opengamma.strata.product.swap.type.OvernightIborSwapConvention;
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConvention;
import com.opengamma.strata.product.swap.type.XCcyOvernightOvernightSwapConvention;
import com.solum.xplain.extensions.immfra.ImmFraConvention;
import com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConvention;
import com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConvention;
import com.solum.xplain.extensions.xccyfixedois.XCcyFixedOvernightSwapConvention;
import com.solum.xplain.extensions.xccyiborois.XCcyIborOvernightSwapConvention;
import java.util.Optional;
import java.util.Set;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CurveNodeTypes {

  public static final String IBOR_FIXING_DEPOSIT_NODE = "IborFixingDeposit";
  public static final String FRA_NODE = "ForwardRateAgreement";
  public static final String IMM_FRA_NODE = "IMMForwardRateAgreement";
  public static final String IBOR_FUTURE_NODE = "IborFuture";
  public static final String FIXED_OVERNIGHT_SWAP_NODE = "FixedOvernightSwap";
  public static final String FIXED_IBOR_SWAP_NODE = "FixedIborSwap";
  public static final String IBOR_IBOR_SWAP_NODE = "IborIborSwap";
  public static final String OVERNIGHT_IBOR_BASIS_SWAP_NODE = "OvernightIborBasisSwap";
  public static final String XCCY_IBOR_IBOR_SWAP_NODE = "XCCYIborIborSwap";
  public static final String XCCY_OIS_OIS_SWAP_NODE = "XCCYOvernightOvernightSwap";
  public static final String XCCY_IBOR_OIS_SWAP_NODE = "XCCYIborOvernightSwap";
  public static final String XCCY_FIXED_OVERNIGHT_SWAP_NODE = "XCCYFixedOvernightSwap";
  public static final String FX_SWAP_NODE = "FxSwap";
  public static final String FIXED_INFLATION_SWAP_NODE = "FixedInflationSwap";
  public static final String TERM_DEPOSIT_NODE = "TermDeposit";
  public static final String TERM_OIS_FIXING_DEPOSIT_NODE = "TermOISFixingDeposit";
  public static final String OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE = "OvernightTermOvernightSwap";

  public static final Set<String> VALUES =
      Set.of(
          IBOR_FIXING_DEPOSIT_NODE,
          FRA_NODE,
          IMM_FRA_NODE,
          IBOR_FUTURE_NODE,
          FIXED_OVERNIGHT_SWAP_NODE,
          FIXED_IBOR_SWAP_NODE,
          IBOR_IBOR_SWAP_NODE,
          OVERNIGHT_IBOR_BASIS_SWAP_NODE,
          XCCY_IBOR_IBOR_SWAP_NODE,
          XCCY_OIS_OIS_SWAP_NODE,
          XCCY_IBOR_OIS_SWAP_NODE,
          XCCY_FIXED_OVERNIGHT_SWAP_NODE,
          FX_SWAP_NODE,
          FIXED_INFLATION_SWAP_NODE,
          TERM_DEPOSIT_NODE,
          TERM_OIS_FIXING_DEPOSIT_NODE,
          OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE);

  public static Optional<String> nodeTypeFromConvention(Named convention) {
    if (convention instanceof IborFixingDepositConvention) {
      return Optional.of(IBOR_FIXING_DEPOSIT_NODE);
    } else if (convention instanceof FraConvention) {
      return Optional.of(FRA_NODE);
    } else if (convention instanceof IborFutureContractSpec) {
      return Optional.of(IBOR_FUTURE_NODE);
    } else if (convention instanceof XCcyFixedOvernightSwapConvention) {
      return Optional.of(XCCY_FIXED_OVERNIGHT_SWAP_NODE);
    } else if (convention instanceof FixedOvernightSwapConvention) {
      return Optional.of(FIXED_OVERNIGHT_SWAP_NODE);
    } else if (convention instanceof FixedIborSwapConvention) {
      return Optional.of(FIXED_IBOR_SWAP_NODE);
    } else if (convention instanceof IborIborSwapConvention) {
      return Optional.of(IBOR_IBOR_SWAP_NODE);
    } else if (convention instanceof OvernightIborSwapConvention) {
      return Optional.of(OVERNIGHT_IBOR_BASIS_SWAP_NODE);
    } else if (convention instanceof XCcyIborIborSwapConvention) {
      return Optional.of(XCCY_IBOR_IBOR_SWAP_NODE);
    } else if (convention instanceof XCcyOvernightOvernightSwapConvention) {
      return Optional.of(XCCY_OIS_OIS_SWAP_NODE);
    } else if (convention instanceof XCcyIborOvernightSwapConvention) {
      return Optional.of(XCCY_IBOR_OIS_SWAP_NODE);
    } else if (convention instanceof FxSwapConvention) {
      return Optional.of(FX_SWAP_NODE);
    } else if (convention instanceof FixedInflationSwapConvention) {
      return Optional.of(FIXED_INFLATION_SWAP_NODE);
    } else if (convention instanceof TermDepositConvention) {
      return Optional.of(TERM_DEPOSIT_NODE);
    } else if (convention instanceof ImmFraConvention) {
      return Optional.of(IMM_FRA_NODE);
    } else if (convention instanceof TermOisFixingDepositConvention) {
      return Optional.of(TERM_OIS_FIXING_DEPOSIT_NODE);
    } else if (convention instanceof OvernightTermOvernightSwapConvention) {
      return Optional.of(OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE);
    }
    return Optional.empty();
  }

  public static boolean isImmNode(String convention) {
    return IMM_FRA_NODE.equals(convention) || IBOR_FUTURE_NODE.equals(convention);
  }
}
