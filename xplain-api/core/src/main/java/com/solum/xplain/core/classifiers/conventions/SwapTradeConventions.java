package com.solum.xplain.core.classifiers.conventions;

import static com.solum.xplain.core.utils.proxy.ProxyUtils.lazyProxy;

import com.google.common.collect.Streams;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.swap.type.FixedIborSwapConvention;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention;
import com.opengamma.strata.product.swap.type.IborIborSwapConvention;
import com.opengamma.strata.product.swap.type.OvernightIborSwapConvention;
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConvention;
import com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConvention;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SwapTradeConventions {

  @SuppressWarnings("unchecked")
  public static final List<SwapConvention> CONVENTIONS =
      lazyProxy(
          List.class,
          () ->
              Streams.concat(
                      conventionsStream(
                          FixedIborSwapConvention.extendedEnum(), ConventionMapper.INSTANCE::to),
                      conventionsStream(
                          FixedInflationSwapConvention.extendedEnum(),
                          ConventionMapper.INSTANCE::to),
                      conventionsStream(
                          FixedOvernightSwapConvention.extendedEnum(),
                          ConventionMapper.INSTANCE::to),
                      conventionsStream(
                          IborIborSwapConvention.extendedEnum(), ConventionMapper.INSTANCE::to),
                      conventionsStream(
                          OvernightIborSwapConvention.extendedEnum(),
                          ConventionMapper.INSTANCE::to),
                      conventionsStream(
                          XCcyIborIborSwapConvention.extendedEnum(), ConventionMapper.INSTANCE::to),
                      conventionsStream(
                          OvernightTermOvernightSwapConvention.extendedEnum(),
                          ConventionMapper.INSTANCE::to))
                  .toList());

  public static Optional<SwapConvention> resolveStrataConvention(List<SwapLegKey> legKeys) {

    return CONVENTIONS.stream().filter(v -> containsLeg(v, legKeys)).findFirst();
  }

  private static <T extends Named> Stream<SwapConvention> conventionsStream(
      ExtendedEnum<T> conventionEnum, Function<T, SwapConvention> mapper) {
    return conventionEnum.lookupAll().values().stream().map(mapper);
  }

  private static boolean containsLeg(SwapConvention convention, List<SwapLegKey> legKeys) {
    return legKeys.contains(SwapLegKey.fromLeg(convention.getPayLeg()))
        && legKeys.contains(SwapLegKey.fromLeg(convention.getReceiveLeg()));
  }
}
