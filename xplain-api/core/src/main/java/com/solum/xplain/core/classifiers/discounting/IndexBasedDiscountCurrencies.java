package com.solum.xplain.core.classifiers.discounting;

import static com.opengamma.strata.basics.currency.Currency.AUD;
import static com.opengamma.strata.basics.currency.Currency.CAD;
import static com.opengamma.strata.basics.currency.Currency.CHF;
import static com.opengamma.strata.basics.currency.Currency.GBP;
import static com.opengamma.strata.basics.currency.Currency.JPY;
import static com.opengamma.strata.basics.currency.Currency.NZD;
import static com.solum.xplain.core.classifiers.Constants.EXPLICIT_CURRENCIES;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.common.CollectionUtils;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IndexBasedDiscountCurrencies {

  public static final IndexBasedDiscountCurrency USD_FEDFUNDS =
      IndexBasedDiscountCurrency.of("USD-FEDFUNDS", Currency.USD);
  public static final IndexBasedDiscountCurrency USD_SOFR =
      IndexBasedDiscountCurrency.of("USD-SOFR", Currency.USD);
  public static final IndexBasedDiscountCurrency USD_SOFR_3M =
      IndexBasedDiscountCurrency.of("USD-SOFR-3M", Currency.USD);
  public static final IndexBasedDiscountCurrency USD_SOFR_6M =
      IndexBasedDiscountCurrency.of("USD-SOFR-6M", Currency.USD);
  public static final IndexBasedDiscountCurrency EUR_EONIA =
      IndexBasedDiscountCurrency.of("EUR-EONIA", Currency.EUR);
  public static final IndexBasedDiscountCurrency EUR_ESTR =
      IndexBasedDiscountCurrency.of("EUR-ESTR", Currency.EUR);

  private static final List<IndexBasedDiscountCurrency> INDEX_BASED_DISCOUNT_CURRENCIES;
  private static final List<IndexBasedDiscountCurrency> AVAILABLE_DISCOUNT_CURRENCIES;
  private static final List<Currency> NON_INDEX_BASED_DISCOUNT_CURRENCIES;

  static {
    INDEX_BASED_DISCOUNT_CURRENCIES =
        List.of(USD_FEDFUNDS, USD_SOFR, USD_SOFR_3M, USD_SOFR_6M, EUR_EONIA, EUR_ESTR);
    NON_INDEX_BASED_DISCOUNT_CURRENCIES = List.of(GBP, AUD, CAD, CHF, JPY, NZD);
    AVAILABLE_DISCOUNT_CURRENCIES =
        CollectionUtils.join(
            INDEX_BASED_DISCOUNT_CURRENCIES,
            NON_INDEX_BASED_DISCOUNT_CURRENCIES.stream()
                .map(IndexBasedDiscountCurrencies::getOf)
                .toList());
  }

  public static List<IndexBasedDiscountCurrency> getAvailableDiscountCurrencies() {
    return AVAILABLE_DISCOUNT_CURRENCIES;
  }

  public static IndexBasedDiscountCurrency get(String name) {
    return AVAILABLE_DISCOUNT_CURRENCIES.stream()
        .filter(c -> Objects.equals(c.getName(), name))
        .findFirst()
        .orElse(
            EXPLICIT_CURRENCIES.stream()
                .filter(c -> Objects.equals(c.getCode(), name))
                .findFirst()
                .map(IndexBasedDiscountCurrencies::getOf)
                .orElse(null));
  }

  public static IndexBasedDiscountCurrency getOf(@NonNull Currency currency) {
    return IndexBasedDiscountCurrency.of(currency.getCode(), currency);
  }
}
