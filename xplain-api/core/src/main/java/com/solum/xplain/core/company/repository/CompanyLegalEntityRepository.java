package com.solum.xplain.core.company.repository;

import static com.hazelcast.map.impl.operation.steps.IMapOpStep.BATCH_SIZE;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.listVersionedActive;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.unionWithLatestActiveVersion;
import static com.solum.xplain.core.company.entity.CompanyLegalEntityIpvSettings.COMPANY_LEGAL_ENTITY_IPV_SETTINGS_COLLECTION;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.teams.Team.TEAM_COLLECTION;
import static com.solum.xplain.core.teams.TeamUtils.collectTeamNames;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static com.solum.xplain.core.utils.mongo.LookupOperationUtils.objectIdConversionLookup;
import static com.solum.xplain.core.utils.mongo.MongoVariables.POSITIONAL_OPERATOR;
import static io.atlassian.fugue.Either.right;
import static java.util.Collections.emptySet;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toSet;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.lookup;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.diff.AuditableDiffable;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.company.CommonCompanyEntityImportView;
import com.solum.xplain.core.company.CompanyLegalEntityFilter;
import com.solum.xplain.core.company.CompanyLegalEntityFilter.CompanyIdWithLegalEntityId;
import com.solum.xplain.core.company.entity.Company;
import com.solum.xplain.core.company.entity.CompanyLegalEntity;
import com.solum.xplain.core.company.entity.CompanyLegalEntityImportView;
import com.solum.xplain.core.company.entity.CompanyLegalEntityReference;
import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings;
import com.solum.xplain.core.company.entity.IpvValuationProviders;
import com.solum.xplain.core.company.entity.IpvValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup;
import com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvForm;
import com.solum.xplain.core.company.events.CompanyArchived;
import com.solum.xplain.core.company.events.CompanyLegalEntityArchived;
import com.solum.xplain.core.company.events.CompanyLegalEntityCreated;
import com.solum.xplain.core.company.events.CompanyLegalEntityImported;
import com.solum.xplain.core.company.events.CompanyLegalEntityUpdated;
import com.solum.xplain.core.company.events.CompanyListEvent;
import com.solum.xplain.core.company.form.CompanyLegalEntityCreateForm;
import com.solum.xplain.core.company.form.CompanyLegalEntityUpdateForm;
import com.solum.xplain.core.company.mapper.CompanyMapper;
import com.solum.xplain.core.company.value.CompanyImportView;
import com.solum.xplain.core.company.value.CompanyLegalEntityNamesView;
import com.solum.xplain.core.company.value.CompanyLegalEntityView;
import com.solum.xplain.core.company.value.ResolvedNavKeys;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.ValuationDataKeyPrefixUtils;
import com.solum.xplain.core.teams.Team;
import com.solum.xplain.core.teams.TeamRepository;
import com.solum.xplain.core.teams.value.TeamNameView;
import com.solum.xplain.core.utils.mongo.LookupOperationUtils;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators.First;
import org.springframework.data.mongodb.core.aggregation.LookupOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

@NullMarked
@Repository
public class CompanyLegalEntityRepository {
  private static final String COMPANY_COLLECTION = "company";
  private static final String COMPANY_LEGAL_ENTITY_COLLECTION = "companyLegalEntity";

  private final MongoOperations mongoOperations;
  private final TeamRepository teamRepository;
  private final CompanyMapper mapper;
  private final AuditingHandler auditingHandler;
  private final CompanyRepository companyRepository;
  private final ApplicationEventPublisher publisher;

  public CompanyLegalEntityRepository(
      MongoOperations mongoOperations,
      TeamRepository teamRepository,
      CompanyMapper mapper,
      AuditingHandler auditingHandler,
      CompanyRepository companyRepository,
      ApplicationEventPublisher publisher) {
    this.mongoOperations = mongoOperations;
    this.teamRepository = teamRepository;
    this.mapper = mapper;
    this.auditingHandler = auditingHandler;
    this.companyRepository = companyRepository;
    this.publisher = publisher;
  }

  public Either<ErrorItem, EntityId> createEntity(
      String companyId, CompanyLegalEntityCreateForm form) {
    var entity = mapper.fromForm(form, CompanyLegalEntity.newOf(companyId));
    var company = companyRepository.companyReference(companyId);

    setVdkPrefixAndCompanyExtIdFields(entity, company.getExternalCompanyId());

    mongoOperations.insert(entity);
    publisher.publishEvent(CompanyLegalEntityCreated.newOf(companyId, entity.getId()));
    return right(entityId(entity.getId()));
  }

  public Either<ErrorItem, EntityId> createEntity(
      String companyId, CompanyLegalEntityCsvForm form, BitemporalDate stateDate) {
    var entity = mapper.fromForm(form, CompanyLegalEntity.newOf(companyId));
    setVdkPrefixAndCompanyExtIdFields(entity, form.getCompanyExternalId());
    mongoOperations.insert(entity);

    var event =
        CompanyLegalEntityImported.newOf(
            EntityId.entityId(entity.getId()),
            companyId,
            form.getMarketDataGroup(),
            form.getValuationDataGroup(),
            form.getSlaDeadline(),
            form.getCurveConfiguration(),
            stateDate);

    publisher.publishEvent(event);
    return right(entityId(entity.getId()));
  }

  /** Sets VdkPrefix and CompanyExtId fields that are derived rather than user-provided */
  private void setVdkPrefixAndCompanyExtIdFields(
      CompanyLegalEntity entity, String companyExternalId) {
    entity.setVdkPrefix(
        ValuationDataKeyPrefixUtils.toValuationDataKeyPrefix(
            companyExternalId, entity.getExternalId()));
    entity.setExternalCompanyId(companyExternalId);
  }

  public Either<ErrorItem, EntityId> updateEntity(
      String companyId, String entityId, CompanyLegalEntityUpdateForm form) {
    return update(companyId, entityId, e -> mapper.fromForm(form, e))
        .map(t -> publishUpdatedEvent(companyId, entityId));
  }

  public Either<ErrorItem, EntityId> archiveEntity(String companyId, String entityId) {
    return update(companyId, entityId, mapper::copyArchived)
        .map(id -> publishArchivedEvent(companyId, entityId));
  }

  private EntityId publishArchivedEvent(String companyId, String entityId) {
    publisher.publishEvent(CompanyLegalEntityArchived.newOf(companyId, entityId));
    return EntityId.entityId(entityId);
  }

  private EntityId publishUpdatedEvent(String companyId, String entityId) {
    publisher.publishEvent(CompanyLegalEntityUpdated.newOf(companyId, entityId));
    return EntityId.entityId(entityId);
  }

  private Either<ErrorItem, EntityId> update(
      String companyId, String entityId, UnaryOperator<CompanyLegalEntity> f) {
    return entity(companyId, entityId)
        .map(
            entity -> {
              var newValue = f.apply(mapper.copy(entity));
              var diff = entity.diff(newValue);
              if (diff.numberOfDiffs() > 0) {
                newValue.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
              }
              mongoOperations.save(newValue);
              return EntityId.entityId(newValue.getId());
            });
  }

  public List<CompanyLegalEntityView> companyLegalEntitiesViews(
      String companyId, CompanyLegalEntityFilter filter) {
    return companyLegalEntitiesViews(companyId, filter, Sort.unsorted());
  }

  public List<CompanyLegalEntityView> companyLegalEntitiesViews(
      String companyId, CompanyLegalEntityFilter filter, Sort sort) {
    return legalEntityViews(
        where(CompanyLegalEntity.Fields.companyId).is(companyId), filter.criteria(), sort);
  }

  public List<CompanyLegalEntityNamesView> companyLegalEntityNameViews() {
    return companyLegalEntityNameViews(emptySet());
  }

  public List<CompanyLegalEntityNamesView> companyLegalEntityNameViews(Set<String> legalEntityIds) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(CompanyLegalEntityFilter.notArchived().criteria()));
    if (!legalEntityIds.isEmpty()) {
      operations.add(match(where(AuditableDiffable.Fields.id).in(legalEntityIds)));
    }
    operations.add(
        objectIdConversionLookup(COMPANY_COLLECTION, CompanyLegalEntity.Fields.companyId),
        unwind(COMPANY_COLLECTION),
        project(
                CompanyLegalEntityNamesView.Fields.externalId,
                CompanyLegalEntityNamesView.Fields.companyId)
            .and(AuditableDiffable.Fields.id)
            .as(CompanyLegalEntityNamesView.Fields.entityId)
            .and(joinPaths(COMPANY_COLLECTION, Company.Fields.externalCompanyId))
            .as(CompanyLegalEntityNamesView.Fields.companyExternalId));

    return mongoOperations
        .aggregateAndReturn(CompanyLegalEntityNamesView.class)
        .by(Aggregation.newAggregation(CompanyLegalEntity.class, operations.build()))
        .all()
        .getMappedResults();
  }

  public List<CompanyLegalEntityView> companiesLegalEntitiesViews(
      EntityTeamFilter teamFilter, List<String> companyIds) {
    return legalEntityViews(
        where(CompanyLegalEntity.Fields.companyId)
            .in(companyIds)
            .andOperator(teamFilter.criteria()),
        notArchived(),
        Sort.unsorted());
  }

  private List<CompanyLegalEntityView> legalEntityViews(
      Criteria filterCriteria, Criteria archiveCriteria, Sort sort) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(filterCriteria))
            .add(match(archiveCriteria));
    if (sort.isSorted()) {
      operations.add(Aggregation.sort(sort));
    }

    var result =
        mongoOperations
            .aggregate(
                Aggregation.newAggregation(CompanyLegalEntity.class, operations.build()),
                CompanyLegalEntity.class)
            .getMappedResults();

    var teamNamesById = teamNamesById(result);
    return result.stream().map(v -> mapper.toEntityView(v, teamNamesById)).toList();
  }

  public Stream<Map.Entry<CompanyIdWithLegalEntityId, CompanyLegalEntityView>>
      companyLegalEntityView(CompanyLegalEntityFilter filter) {
    // Legal Entities
    var legalEntities =
        mongoOperations.stream(query(filter.criteria()), CompanyLegalEntity.class)
            .collect(
                Collectors.toMap(
                    item -> new CompanyIdWithLegalEntityId(item.getCompanyId(), item.getId()),
                    identity()));
    Map<String, String> teamNameMap = teamNamesById(legalEntities.values());
    return legalEntities.entrySet().stream()
        .map(item -> Map.entry(item.getKey(), mapper.toEntityView(item.getValue(), teamNameMap)));
  }

  private Map<String, String> teamNamesById(Collection<CompanyLegalEntity> companyLegalEntities) {
    var teamIds =
        companyLegalEntities.stream()
            .map(CompanyLegalEntity::getTeamIds)
            .filter(Objects::nonNull)
            .flatMap(Collection::stream)
            .map(ObjectId::toHexString)
            .collect(Collectors.toSet());
    return teamRepository.teamNamesList(teamIds).stream()
        .collect(Collectors.toMap(TeamNameView::getId, TeamNameView::getName));
  }

  public List<CompanyLegalEntity> allLegalEntities() {
    return mongoOperations.query(CompanyLegalEntity.class).matching(query(notArchived())).all();
  }

  public List<CompanyLegalEntity> activeLegalEntities(EntityTeamFilter entityTeamFilter) {
    return mongoOperations
        .query(CompanyLegalEntity.class)
        .matching(query(notArchived().andOperator(entityTeamFilter.criteria())))
        .all();
  }

  public List<CompanyLegalEntity> archivedLegalEntities(String companyId, String entityId) {
    return mongoOperations
        .query(CompanyLegalEntity.class)
        .matching(
            query(
                where(CompanyLegalEntity.Fields.archived)
                    .is(true)
                    .andOperator(
                        where(CompanyLegalEntity.Fields.companyId).is(companyId),
                        where(AuditableDiffable.Fields.id).is(entityId))))
        .all();
  }

  public Stream<CompanyLegalEntityImportView> entityImportAggregatedViews(
      XplainPrincipal user, BitemporalDate stateDate) {

    var latestActiveEntityValuationSettingsOps = listVersionedActive(stateDate);

    var latestVersionIpvSettingsMapping =
        unionWithLatestActiveVersion(
            COMPANY_LEGAL_ENTITY_IPV_SETTINGS_COLLECTION,
            VersionedEntity.Fields.entityId,
            stateDate);

    var accessibleEntitiesFilter =
        match(EntityTeamFilter.filter(user).criteria(COMPANY_LEGAL_ENTITY_COLLECTION));

    var companyLookupOps =
        List.of(
            objectIdConversionLookup(
                COMPANY_COLLECTION,
                joinPaths(COMPANY_LEGAL_ENTITY_COLLECTION, CompanyLegalEntity.Fields.companyId)),
            unwind(COMPANY_COLLECTION));

    var accessibleEntityLookupOps =
        List.of(
            LookupOperationUtils.objectIdConversionLookup(
                COMPANY_LEGAL_ENTITY_COLLECTION, VersionedEntity.Fields.entityId),
            unwind(COMPANY_LEGAL_ENTITY_COLLECTION),
            teamLookUp(joinPaths(COMPANY_LEGAL_ENTITY_COLLECTION, Company.Fields.teamIds)),
            accessibleEntitiesFilter);

    // get latest (as of state date) valuation settings, map with latest (as of state date)
    // entity ipv settings using entityId (= companyLegalEntity._id), lookup entity by entityId
    // and filter out those that aren't accessible, then sort by companyId and then entityId
    var ops =
        ImmutableList.<AggregationOperation>builder()
            .addAll(latestActiveEntityValuationSettingsOps)
            .addAll(latestVersionIpvSettingsMapping)
            .addAll(accessibleEntityLookupOps)
            .addAll(companyLookupOps)
            .add(importViewProjections())
            .add(
                sort(
                    Sort.by(Direction.ASC, CommonCompanyEntityImportView.Fields.companyId)
                        .and(Sort.by(Direction.ASC, CompanyLegalEntityImportView.Fields.entityId))))
            .build();

    return mongoOperations.aggregateStream(
        Aggregation.newAggregation(CompanyLegalEntityValuationSettings.class, ops),
        CompanyLegalEntityImportView.class);
  }

  private ProjectionOperation importViewProjections() {
    var entityFieldName = POSITIONAL_OPERATOR + COMPANY_LEGAL_ENTITY_COLLECTION;
    var companyFieldName = POSITIONAL_OPERATOR + COMPANY_COLLECTION;

    return project(CommonCompanyEntityImportView.Fields.slaDeadline)
        .and(
            joinPaths(
                POSITIONAL_OPERATOR + ValuationSettings.Fields.marketDataGroup,
                ValuationSettingsMarketDataGroup.Fields.marketDataGroupName))
        .as(CommonCompanyEntityImportView.Fields.marketDataGroup)
        .and(
            joinPaths(
                POSITIONAL_OPERATOR + ValuationSettings.Fields.curveConfiguration,
                EntityReference.Fields.name))
        .as(CommonCompanyEntityImportView.Fields.curveConfiguration)
        .and(joinPaths(companyFieldName, Company.Fields.externalCompanyId))
        .as(CommonCompanyEntityImportView.Fields.companyId)
        .and(joinPaths(entityFieldName, CompanyLegalEntity.Fields.externalId))
        .as(CompanyLegalEntityImportView.Fields.entityId)
        .and(joinPaths(entityFieldName, CompanyLegalEntity.Fields.name))
        .as(CompanyLegalEntityImportView.Fields.entityName)
        .and(joinPaths(entityFieldName, CompanyLegalEntity.Fields.description))
        .as(CompanyImportView.Fields.description)
        // TODO: at the moment just taking the first non-null VDG
        .and(
            First.first(
                propertyName(
                    POSITIONAL_OPERATOR + IpvValuationSettings.Fields.products,
                    IpvValuationProviders.Fields.ipvDataGroup,
                    EntityReference.Fields.name)))
        .as(CommonCompanyEntityImportView.Fields.valuationDataGroup)
        .and(propertyName(TEAM_COLLECTION, Team.Fields.externalId))
        .as(CommonCompanyEntityImportView.Fields.teams)
        .and(joinPaths(entityFieldName, CompanyLegalEntity.Fields.allowAllTeams))
        .as(CommonCompanyEntityImportView.Fields.allowAllTeams);
  }

  private LookupOperation teamLookUp(String localField) {
    return lookup(TEAM_COLLECTION, localField, UNDERSCORE_ID, TEAM_COLLECTION);
  }

  public Either<ErrorItem, CompanyLegalEntityView> companyLegalEntityView(
      String companyId, String entityId) {
    return entity(companyId, entityId).map(v -> mapper.toEntityView(v, teamNames(v)));
  }

  public Stream<CompanyLegalEntityReference> streamExcludedLegalEntityReferencesForUser(
      XplainPrincipal user, Collection<String> excludedCompanyIds) {
    Criteria criteria =
        where(CompanyLegalEntity.Fields.allowAllTeams)
            .is(false)
            .and(CompanyLegalEntity.Fields.companyId)
            .nin(excludedCompanyIds);
    List<ObjectId> teamIds = user.getTeams();
    if (!teamIds.isEmpty()) {
      criteria = criteria.and(CompanyLegalEntity.Fields.teamIds).nin(teamIds);
    }
    var aggregationOperations =
        List.of(match(criteria), match(notArchived()), projectCompanyLegalEntityReferenceView());
    return mongoOperations.aggregateStream(
        newAggregation(CompanyLegalEntity.class, aggregationOperations),
        CompanyLegalEntityReference.class);
  }

  private ProjectionOperation projectCompanyLegalEntityReferenceView() {
    return project()
        .and(AuditableDiffable.Fields.id)
        .as(EntityReference.Fields.entityId)
        .and(CompanyLegalEntity.Fields.name)
        .as(EntityReference.Fields.name)
        .and(CompanyLegalEntity.Fields.externalId)
        .as(CompanyLegalEntityReference.Fields.externalEntityId);
  }

  public Either<ErrorItem, UserTeamEntity<CompanyLegalEntityView>> userCompanyLegalEntityView(
      XplainPrincipal user, String companyId, String entityId) {
    return companyRepository
        .userCompanyView(user, companyId)
        .flatMap(c -> companyLegalEntityView(companyId, entityId))
        .map(view -> UserTeamEntity.userEntity(user, view))
        .flatMap(UserTeamEntity::allowTeamsOnly);
  }

  public boolean existsByNameExcludingSelf(String companyId, String entityId, String name) {
    return mongoOperations
        .query(CompanyLegalEntity.class)
        .matching(
            query(
                    where(CompanyLegalEntity.Fields.companyId)
                        .is(companyId)
                        .and(Company.Fields.name)
                        .is(name)
                        .and(AuditableDiffable.Fields.id)
                        .ne(entityId))
                .addCriteria(notArchived()))
        .exists();
  }

  public boolean existsByExternalId(String companyId, String externalId) {
    return mongoOperations
        .query(CompanyLegalEntity.class)
        .matching(
            query(
                    where(CompanyLegalEntity.Fields.externalId)
                        .is(externalId)
                        .and(CompanyLegalEntity.Fields.companyId)
                        .is(companyId))
                .addCriteria(notArchived()))
        .exists();
  }

  public @Nullable CompanyLegalEntity findByExternalId(String companyId, String externalId) {
    return mongoOperations
        .query(CompanyLegalEntity.class)
        .matching(
            query(
                    where(CompanyLegalEntity.Fields.externalId)
                        .is(externalId)
                        .and(CompanyLegalEntity.Fields.companyId)
                        .is(companyId))
                .addCriteria(notArchived()))
        .firstValue();
  }

  public Stream<CompanyLegalEntity> findByCompanyIdAndExternalIds(
      Set<Pair<String, String>> companyIdAndExternalId) {
    Criteria orCriteria =
        new Criteria()
            .orOperator(
                companyIdAndExternalId.stream()
                    .map(
                        pair ->
                            where(CompanyLegalEntity.Fields.externalId)
                                .is(pair.getSecond())
                                .and(CompanyLegalEntity.Fields.companyId)
                                .is(pair.getFirst()))
                    .toList());
    return mongoOperations.stream(
        query(orCriteria.andOperator(notArchived())), CompanyLegalEntity.class);
  }

  public Either<ErrorItem, CompanyLegalEntity> entity(String companyId, String entityId) {
    return mongoOperations
        .query(CompanyLegalEntity.class)
        .matching(query(legalEntityCriteria(companyId, entityId)))
        .first()
        .map(Either::<ErrorItem, CompanyLegalEntity>right)
        .orElse(Either.left(OBJECT_NOT_FOUND.entity("Company Entity not found")));
  }

  private Criteria legalEntityCriteria(String companyId, String entityId) {
    return where(AuditableDiffable.Fields.id)
        .is(entityId)
        .and(CompanyLegalEntity.Fields.companyId)
        .is(companyId)
        .andOperator(notArchived());
  }

  private Criteria notArchived() {
    return where(CompanyLegalEntity.Fields.archived).is(false);
  }

  private List<String> teamNames(CompanyLegalEntity entity) {
    return collectTeamNames(
        teamRepository.teamNamesList(
            ofNullable(entity.getTeamIds()).stream()
                .flatMap(Collection::stream)
                .map(ObjectId::toHexString)
                .toList()));
  }

  public Set<ResolvedNavKeys> resolvedCompanyEntityKeys(Set<String> groupDataKeys) {
    var resolvedKeys = new HashSet<ResolvedNavKeys>();
    Iterables.partition(groupDataKeys, BATCH_SIZE)
        .forEach(
            keys -> {
              var criteria =
                  where(CompanyLegalEntity.Fields.vdkPrefix).in(keys).andOperator(notArchived());
              var result = findCompanyEntityKeys(criteria);
              resolvedKeys.addAll(result);
            });

    return resolvedKeys;
  }

  private Set<ResolvedNavKeys> findCompanyEntityKeys(Criteria criteria) {

    var operations =
        List.of(
            match(criteria),
            project()
                .and(CompanyLegalEntity.Fields.vdkPrefix)
                .as("key")
                .and(CompanyLegalEntity.Fields.companyId)
                .as("companyId")
                .and(AuditableDiffable.Fields.id)
                .as("legalEntityId"));

    return mongoOperations
        .aggregateAndReturn(ResolvedNavKeys.class)
        .by(newAggregation(CompanyLegalEntity.class, operations))
        .stream()
        .collect(toSet());
  }

  @EventListener
  public void onCompanyArchived(CompanyArchived companyArchived) {
    companyLegalEntitiesViews(companyArchived.getEntityId(), CompanyLegalEntityFilter.notArchived())
        .forEach(e -> archiveEntity(companyArchived.getEntityId(), e.getId()));
  }

  @EventListener
  public void onCompanyLegalEntityCreated(CompanyLegalEntityCreated event) {
    updateCompanyEntitiesCountBulk(List.of(event.getCompanyId()));
  }

  @EventListener
  public void onCompanyLegalEntityArchived(CompanyLegalEntityArchived event) {
    updateCompanyEntitiesCountBulk(List.of(event.getCompanyId()));
  }

  @EventListener
  public void onCompanyLegalEntityImported(CompanyLegalEntityImported event) {
    updateCompanyEntitiesCountBulk(List.of(event.getCompanyId()));
  }

  @EventListener
  public void onCompanyList(CompanyListEvent event) {
    updateCompanyEntitiesCountBulk(event.companyIds());
  }

  public void updateCompanyEntitiesCountBulk(List<String> companyIds) {
    var countsAggregation =
        Aggregation.newAggregation(
            match(
                where(CompanyLegalEntity.Fields.companyId)
                    .in(companyIds)
                    .and(CompanyLegalEntity.Fields.archived)
                    .is(false)),
            group(CompanyLegalEntity.Fields.companyId).count().as("count"));

    var companyCounts =
        mongoOperations
            .aggregate(countsAggregation, COMPANY_LEGAL_ENTITY_COLLECTION, Document.class)
            .getMappedResults();

    CollectionUtils.chunked(companyCounts.stream())
        .forEach(
            batch -> {
              var bulkOps =
                  mongoOperations.bulkOps(BulkOperations.BulkMode.UNORDERED, Company.class);
              batch.forEach(
                  doc -> {
                    var companyId = doc.getString("_id");
                    var count = doc.getInteger("count");
                    var update = Update.update("numberOfEntities", count);
                    bulkOps.updateOne(query(where("_id").is(companyId)), update);
                  });
              bulkOps.execute();
            });
  }
}
