package com.solum.xplain.core.company.repository;

import static com.solum.xplain.core.common.AggregateFields.COUNT_FIELD;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.listVersionedActive;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.unionWithLatestActiveVersion;
import static com.solum.xplain.core.company.entity.Company.COMPANY_COLLECTION;
import static com.solum.xplain.core.company.entity.CompanyIpvSettings.COMPANY_IPV_SETTINGS_COLLECTION;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.teams.Team.TEAM_COLLECTION;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static com.solum.xplain.core.utils.mongo.MongoVariables.POSITIONAL_OPERATOR;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.lookup;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.diff.AuditableDiffable;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.company.CommonCompanyEntityImportView;
import com.solum.xplain.core.company.CompanyFilter;
import com.solum.xplain.core.company.csv.CompanyCsvForm;
import com.solum.xplain.core.company.csv.CompanyUniqueKey;
import com.solum.xplain.core.company.entity.Company;
import com.solum.xplain.core.company.entity.CompanyReference;
import com.solum.xplain.core.company.entity.CompanyValuationSettings;
import com.solum.xplain.core.company.entity.IpvValuationProviders;
import com.solum.xplain.core.company.entity.IpvValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup;
import com.solum.xplain.core.company.events.CompanyArchived;
import com.solum.xplain.core.company.events.CompanyCreated;
import com.solum.xplain.core.company.events.CompanyImported;
import com.solum.xplain.core.company.events.CompanyUpdated;
import com.solum.xplain.core.company.form.CompanyCreateForm;
import com.solum.xplain.core.company.form.CompanyUpdateForm;
import com.solum.xplain.core.company.mapper.CompanyMapper;
import com.solum.xplain.core.company.value.CompanyImportView;
import com.solum.xplain.core.company.value.CompanyView;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.teams.Team;
import com.solum.xplain.core.utils.mongo.LookupOperationUtils;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators.First;
import org.springframework.data.mongodb.core.aggregation.LookupOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@NullMarked
@Repository
public class CompanyRepository {
  private final MongoOperations mongoOperations;
  private final CompanyMapper mapper;
  private final ApplicationEventPublisher eventPublisher;
  private final AuditingHandler auditingHandler;

  public CompanyRepository(
      MongoOperations mongoOperations,
      CompanyMapper mapper,
      ApplicationEventPublisher eventPublisher,
      AuditingHandler auditingHandler) {
    this.mongoOperations = mongoOperations;
    this.mapper = mapper;
    this.eventPublisher = eventPublisher;
    this.auditingHandler = auditingHandler;
  }

  public Either<ErrorItem, CompanyView> companyView(String companyId) {
    List<AggregationOperation> aggregationOperations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(notArchivedCompany(companyId)))
            .add(teamLookUp(Company.Fields.teamIds))
            .add(projectView())
            .build();
    var result =
        mongoOperations
            .aggregate(newAggregation(Company.class, aggregationOperations), CompanyView.class)
            .getUniqueMappedResult();
    return result == null
        ? Either.left(OBJECT_NOT_FOUND.entity("Company not found"))
        : Either.right(result);
  }

  public List<CompanyReference> companyReferences(List<String> companyIds) {
    var aggregationOperations =
        List.of(match(notArchivedCompanies(companyIds)), projectCompanyReferenceView());
    return mongoOperations
        .aggregate(newAggregation(Company.class, aggregationOperations), CompanyReference.class)
        .getMappedResults();
  }

  public Stream<CompanyReference> companyReferencesStream(Collection<String> companyIds) {
    var aggregationOperations =
        List.of(match(notArchivedCompanies(companyIds)), projectCompanyReferenceView());
    return mongoOperations.aggregateStream(
        newAggregation(Company.class, aggregationOperations), CompanyReference.class);
  }

  public @Nullable CompanyReference companyReference(String companyId) {
    var aggregationOperations =
        List.of(match(notArchivedCompany(companyId)), projectCompanyReferenceView());
    return mongoOperations
        .aggregate(newAggregation(Company.class, aggregationOperations), CompanyReference.class)
        .getUniqueMappedResult();
  }

  public List<CompanyReference> companyReferences(EntityTeamFilter teamFilter) {
    var aggregationOperations =
        List.of(match(teamFilter.criteria()), match(notArchived()), projectCompanyReferenceView());
    return mongoOperations
        .aggregate(newAggregation(Company.class, aggregationOperations), CompanyReference.class)
        .getMappedResults();
  }

  public Stream<CompanyReference> streamExcludedCompanyReferencesForUser(XplainPrincipal user) {
    Criteria criteria = where(Company.Fields.allowAllTeams).is(false);
    List<ObjectId> teamIds = user.getTeams();
    if (!teamIds.isEmpty()) {
      criteria = criteria.and(Company.Fields.teamIds).nin(teamIds);
    }
    var aggregationOperations =
        List.of(match(criteria), match(notArchived()), projectCompanyReferenceView());
    return mongoOperations.aggregateStream(
        newAggregation(Company.class, aggregationOperations), CompanyReference.class);
  }

  public Either<ErrorItem, UserTeamEntity<CompanyView>> userCompanyView(
      XplainPrincipal user, String companyId) {
    return companyView(companyId)
        .map(p -> UserTeamEntity.userEntity(user, p))
        .flatMap(UserTeamEntity::allowTeamsOnly);
  }

  public Either<ErrorItem, EntityId> createCompany(CompanyCreateForm form) {
    Company company = mapper.fromForm(form);
    mongoOperations.insert(company);
    eventPublisher.publishEvent(new CompanyCreated(company.getId()));
    return Either.right(EntityId.entityId(company.getId()));
  }

  public Either<ErrorItem, EntityId> createCompany(CompanyCsvForm form, BitemporalDate stateDate) {
    Company company = mapper.fromForm(form);
    mongoOperations.insert(company);

    var createdEvent =
        CompanyImported.newOf(
            EntityId.entityId(company.getId()),
            form.getMarketDataGroup(),
            form.getValuationDataGroup(),
            form.getSlaDeadline(),
            form.getCurveConfiguration(),
            stateDate);

    eventPublisher.publishEvent(createdEvent);
    return Either.right(EntityId.entityId(company.getId()));
  }

  public Either<ErrorItem, EntityId> updateCompany(String companyId, CompanyUpdateForm form) {
    return update(companyId, e -> mapper.fromForm(form, e)).map(this::publishUpdatedEvent);
  }

  public Either<ErrorItem, EntityId> archiveCompany(String companyId) {
    return update(companyId, mapper::copyArchived).map(this::publishArchivedEvent);
  }

  private Either<ErrorItem, EntityId> update(String id, UnaryOperator<Company> f) {
    return companyEntity(id)
        .map(
            entity -> {
              var newValue = f.apply(mapper.copy(entity));
              var diff = entity.diff(newValue);
              if (diff.numberOfDiffs() > 0) {
                newValue.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
              }
              mongoOperations.save(newValue);
              return entityId(newValue.getId());
            });
  }

  public List<Company> companyList(EntityTeamFilter filter) {
    return mongoOperations
        .query(Company.class)
        .matching(query(filter.criteria()).addCriteria(notArchived()))
        .all();
  }

  /**
   * Returns number of companies that are accessible with respect to the teamFilter and match the
   * given unique keys.
   *
   * @param uniqueKeys the unique keys to match
   * @param teamFilter the team filter to apply
   * @return the number of matches
   */
  public int numberAccessibleCompaniesMatchingKeys(
      Set<CompanyUniqueKey> uniqueKeys, EntityTeamFilter teamFilter) {

    var uniqueKeysStrings = uniqueKeys.stream().map(CompanyUniqueKey::externalCompanyId).toList();

    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(notArchived()))
            .add(match(teamFilter.criteria()))
            .add(match(where(Company.Fields.externalCompanyId).in(uniqueKeysStrings)))
            .add(count().as(COUNT_FIELD))
            .build();

    var result =
        mongoOperations
            .aggregate(newAggregation(Company.class, operations), Document.class)
            .getUniqueMappedResult();
    return Optional.ofNullable(result).map(r -> r.getInteger(COUNT_FIELD)).orElse(0);
  }

  public List<CompanyView> companyViewList(
      EntityTeamFilter teamFilter, CompanyFilter filter, Sort sort) {
    var aggregationOperations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(teamFilter.criteria()))
            .add(match(filter.criteria()))
            .add(teamLookUp(Company.Fields.teamIds))
            .add(projectView());
    if (sort.isSorted()) {
      aggregationOperations.add(Aggregation.sort(sort));
    }

    return mongoOperations
        .aggregate(newAggregation(Company.class, aggregationOperations.build()), CompanyView.class)
        .getMappedResults();
  }

  public List<CompanyView> companyViewList(EntityTeamFilter teamFilter, CompanyFilter filter) {
    return companyViewList(teamFilter, filter, Sort.unsorted());
  }

  public boolean existsByNameIdExcludingSelf(String name, String id) {
    return mongoOperations
        .query(Company.class)
        .matching(
            query(where(AuditableDiffable.Fields.id).ne(id).and(Company.Fields.name).is(name))
                .addCriteria(notArchived()))
        .exists();
  }

  public boolean existByExternalId(String externalId) {
    return mongoOperations
        .query(Company.class)
        .matching(
            query(where(Company.Fields.externalCompanyId).is(externalId))
                .addCriteria(notArchived()))
        .exists();
  }

  public Stream<Company> findByExternalIds(Set<String> externalIds) {
    return mongoOperations.stream(
        query(where(Company.Fields.externalCompanyId).in(externalIds)).addCriteria(notArchived()),
        Company.class);
  }

  public @Nullable Company findByExternalId(String externalId) {
    return mongoOperations
        .query(Company.class)
        .matching(
            query(where(Company.Fields.externalCompanyId).is(externalId))
                .addCriteria(notArchived()))
        .firstValue();
  }

  public Either<ErrorItem, Company> companyEntity(String id) {
    return mongoOperations
        .query(Company.class)
        .matching(query(notArchivedCompany(id)))
        .one()
        .map(Either::<ErrorItem, Company>right)
        .orElse(Either.left(OBJECT_NOT_FOUND.entity("Company not found")));
  }

  private ProjectionOperation projectView() {

    return project()
        .and(Company.Fields.name)
        .as(CompanyView.Fields.name)
        .and(Company.Fields.externalCompanyId)
        .as(CompanyView.Fields.externalCompanyId)
        .and(Company.Fields.numberOfPortfolios)
        .as(CompanyView.Fields.numberOfPortfolios)
        .and(Company.Fields.numberOfEntities)
        .as(CompanyView.Fields.numberOfEntities)
        .and(Company.Fields.description)
        .as(CompanyView.Fields.description)
        .and(propertyName(TEAM_COLLECTION, Team.Fields.name))
        .as(CompanyView.Fields.teamNames)
        .and(Company.Fields.teamIds)
        .as(CompanyView.Fields.teamIds)
        .and(Company.Fields.allowAllTeams)
        .as(CompanyView.Fields.allowAllTeams);
  }

  private ProjectionOperation projectCompanyReferenceView() {
    return project()
        .and(AuditableDiffable.Fields.id)
        .as(EntityReference.Fields.entityId)
        .and(Company.Fields.name)
        .as(EntityReference.Fields.name)
        .and(Company.Fields.externalCompanyId)
        .as(CompanyReference.Fields.externalCompanyId);
  }

  public Stream<CompanyImportView> companyImportAggregatedViews(
      XplainPrincipal user, BitemporalDate stateDate) {

    var latestActiveValuationSettingsOps = listVersionedActive(stateDate);

    var latestVersionIpvSettingsMapping =
        unionWithLatestActiveVersion(
            COMPANY_IPV_SETTINGS_COLLECTION, VersionedEntity.Fields.entityId, stateDate);

    var accessibleCompaniesFilter =
        match(EntityTeamFilter.filter(user).criteria(COMPANY_COLLECTION));
    var companyLookupAndMergeOps =
        List.of(
            LookupOperationUtils.objectIdConversionLookup(
                COMPANY_COLLECTION, VersionedEntity.Fields.entityId),
            unwind(COMPANY_COLLECTION),
            teamLookUp(joinPaths(COMPANY_COLLECTION, Company.Fields.teamIds)),
            accessibleCompaniesFilter,
            companyMergeProjections());

    // get latest (as of state date) valuation settings, map with latest (as of state date)
    // company ipv settings using entityId (= company._id), lookup company by entityId
    // and filter out those that aren't accessible, then sort by companyID
    var ops =
        ImmutableList.<AggregationOperation>builder()
            .addAll(latestActiveValuationSettingsOps)
            .addAll(latestVersionIpvSettingsMapping)
            .addAll(companyLookupAndMergeOps)
            .add(sort(Sort.by(Direction.ASC, CommonCompanyEntityImportView.Fields.companyId)))
            .build();

    return mongoOperations.aggregateStream(
        Aggregation.newAggregation(CompanyValuationSettings.class, ops), CompanyImportView.class);
  }

  private ProjectionOperation companyMergeProjections() {
    var companyFieldName = POSITIONAL_OPERATOR + COMPANY_COLLECTION;

    return project(CommonCompanyEntityImportView.Fields.slaDeadline)
        .and(
            joinPaths(
                POSITIONAL_OPERATOR + ValuationSettings.Fields.marketDataGroup,
                ValuationSettingsMarketDataGroup.Fields.marketDataGroupName))
        .as(CommonCompanyEntityImportView.Fields.marketDataGroup)
        .and(
            joinPaths(
                POSITIONAL_OPERATOR + ValuationSettings.Fields.curveConfiguration,
                EntityReference.Fields.name))
        .as(CommonCompanyEntityImportView.Fields.curveConfiguration)
        .and(joinPaths(companyFieldName, Company.Fields.externalCompanyId))
        .as(CommonCompanyEntityImportView.Fields.companyId)
        .and(joinPaths(companyFieldName, Company.Fields.name))
        .as(CompanyImportView.Fields.companyName)
        .and(joinPaths(companyFieldName, Company.Fields.description))
        .as(CompanyImportView.Fields.description)
        // TODO: at the moment just taking the first non-null VDG
        .and(
            First.first(
                propertyName(
                    POSITIONAL_OPERATOR + IpvValuationSettings.Fields.products,
                    IpvValuationProviders.Fields.ipvDataGroup,
                    EntityReference.Fields.name)))
        .as(CommonCompanyEntityImportView.Fields.valuationDataGroup)
        .and(propertyName(TEAM_COLLECTION, Team.Fields.externalId))
        .as(CommonCompanyEntityImportView.Fields.teams)
        .and(joinPaths(companyFieldName, Company.Fields.allowAllTeams))
        .as(CommonCompanyEntityImportView.Fields.allowAllTeams);
  }

  private LookupOperation teamLookUp(String localField) {
    return lookup(TEAM_COLLECTION, localField, UNDERSCORE_ID, TEAM_COLLECTION);
  }

  private Criteria notArchivedCompany(String companyId) {
    return where(AuditableDiffable.Fields.id).is(companyId).andOperator(notArchived());
  }

  private Criteria notArchivedCompanies(Collection<String> companyIds) {
    return where(AuditableDiffable.Fields.id).in(companyIds).andOperator(notArchived());
  }

  private Criteria notArchived() {
    return where(Company.Fields.archived).is(false);
  }

  private EntityId publishArchivedEvent(EntityId id) {
    eventPublisher.publishEvent(CompanyArchived.newOf(id));
    return id;
  }

  private EntityId publishUpdatedEvent(EntityId id) {
    eventPublisher.publishEvent(CompanyUpdated.newOf(id));
    return id;
  }

  public void updatePortfolioCounts(Map<String, Integer> counts) {
    BulkOperations bulkOperations =
        mongoOperations.bulkOps(BulkOperations.BulkMode.UNORDERED, Company.class);
    counts.forEach(
        (companyId, count) ->
            bulkOperations.updateOne(
                query(where(AuditableDiffable.Fields.id).is(companyId)),
                Update.update(Company.Fields.numberOfPortfolios, count)));
    bulkOperations.execute();
  }
}
