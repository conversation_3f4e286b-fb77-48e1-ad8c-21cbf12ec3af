package com.solum.xplain.core.config;

import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.solum.xplain.core.config.converters.BeanJacksonSerializer;
import com.solum.xplain.core.config.converters.DiffSerializer;
import com.solum.xplain.core.config.converters.InputStreamSerializer;
import com.solum.xplain.core.config.converters.ObjectIdJacksonSerializer;
import com.solum.xplain.core.config.converters.instrument.AssetClassJacksonDeserializer;
import com.solum.xplain.core.config.converters.instrument.AssetClassJacksonSerializer;
import com.solum.xplain.core.config.converters.instrument.InstrumentTypeJacksonDeserializer;
import com.solum.xplain.core.config.converters.instrument.InstrumentTypeJacksonSerializer;
import com.solum.xplain.core.config.converters.instrument.InstrumentTypeKeyDeserializer;
import com.solum.xplain.core.config.converters.product.ProductTypeJacksonDeserializer;
import com.solum.xplain.core.config.converters.product.ProductTypeJacksonSerializer;
import com.solum.xplain.core.config.converters.product.ProductTypeKeyDeserializer;
import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.AssetClassResolver;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.core.instrument.InstrumentTypeResolver;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.ProductTypeResolver;
import com.solum.xplain.core.viewconfig.config.String2ViewConverter;
import com.solum.xplain.core.viewconfig.config.ViewJacksonDeserializer;
import com.solum.xplain.core.viewconfig.config.ViewJacksonSerializer;
import jakarta.inject.Provider;
import lombok.AllArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@AllArgsConstructor
@NullMarked
public class JacksonConfig {

  private final Provider<InstrumentTypeResolver> instrumentProvider;
  private final Provider<AssetClassResolver> assetClassProvider;
  private final Provider<ProductTypeResolver> productProvider;
  private final Provider<String2ViewConverter> viewConverter;

  @Bean
  public Jackson2ObjectMapperBuilderCustomizer jacksonCustomizer() {
    return builder -> {
      builder.serializers(
          new BeanJacksonSerializer(),
          new ObjectIdJacksonSerializer(),
          new DiffSerializer(),
          new InputStreamSerializer(),
          new ViewJacksonSerializer());
      builder.deserializers(
          new InstrumentTypeJacksonDeserializer(instrumentProvider),
          new AssetClassJacksonDeserializer(assetClassProvider),
          new ProductTypeJacksonDeserializer(productProvider),
          new ViewJacksonDeserializer(viewConverter));
    };
  }

  @Bean
  public Module instrumentsModule() {
    SimpleModule module = new SimpleModule();
    module.addSerializer(new InstrumentTypeJacksonSerializer());
    module.addSerializer(new AssetClassJacksonSerializer());
    module.addDeserializer(AssetClass.class, new AssetClassJacksonDeserializer(assetClassProvider));
    module.addDeserializer(
        InstrumentType.class, new InstrumentTypeJacksonDeserializer(instrumentProvider));
    module.addKeyDeserializer(
        InstrumentType.class, new InstrumentTypeKeyDeserializer(instrumentProvider));
    return module;
  }

  @Bean
  public Module productModule() {
    SimpleModule module = new SimpleModule();
    module.addSerializer(new ProductTypeJacksonSerializer());
    module.addDeserializer(ProductType.class, new ProductTypeJacksonDeserializer(productProvider));
    module.addKeyDeserializer(ProductType.class, new ProductTypeKeyDeserializer(productProvider));
    return module;
  }
}
