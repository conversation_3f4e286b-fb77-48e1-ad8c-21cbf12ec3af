package com.solum.xplain.core.curveconfiguration;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration.newOf;
import static java.util.function.Function.identity;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationNameView;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationSearchForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationUpdateForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import one.util.streamex.StreamEx;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@NullMarked
public class CurveConfigurationRepository
    extends GenericUniqueVersionedEntityRepository<CurveConfiguration> {

  private final MongoOperations mongoOperations;
  private final CurveConfigurationMapper mapper;
  private final CurveGroupRepository curveGroupRepository;

  public CurveConfigurationRepository(
      MongoOperations mongoOperations,
      CurveConfigurationMapper mapper,
      CurveGroupRepository curveGroupRepository) {
    super(mongoOperations, mapper);
    this.mongoOperations = mongoOperations;
    this.mapper = mapper;
    this.curveGroupRepository = curveGroupRepository;
  }

  public static Criteria uniqueEntityCriteria(String name) {
    return where(VersionedNamedEntity.Fields.name).is(name);
  }

  @Override
  protected Criteria uniqueEntityCriteria(CurveConfiguration entity) {
    return uniqueEntityCriteria(entity.getName());
  }

  public Either<ErrorItem, EntityId> insert(CurveConfigurationForm f) {
    var curveConfiguration = mapper.fromForm(f, newOf());
    return insert(curveConfiguration, f.getVersionForm());
  }

  public Either<ErrorItem, CurveConfigurationView> getView(
      String entityId, BitemporalDate stateDate) {
    return entity(entityId, stateDate, active()).map(toViewFn());
  }

  public Map<String, Either<ErrorItem, CurveConfigurationView>> getViews(
      Set<String> entityIds, BitemporalDate stateDate) {
    var allCurves =
        StreamEx.of(
                entities(stateDate, active(), where(VersionedEntity.Fields.entityId).in(entityIds)))
            .toMap(VersionedEntity::getEntityId, identity());
    var requiredCurveGroupIds =
        StreamEx.of(allCurves.values()).map(CurveConfiguration::getCurveGroupId).toSet();

    var groupViews = curveGroupRepository.getCurveGroups(requiredCurveGroupIds);

    return StreamEx.of(entityIds)
        .toMap(
            identity(),
            entity -> {
              var curve = allCurves.get(entity);
              if (curve == null) {
                return Either.left(notFound(entity));
              }
              var group = groupViews.get(curve.getCurveGroupId());
              return group.map(groupView -> mapper.from(curve, groupView.getName()));
            });
  }

  public boolean validCurveConfigurationId(String entityId) {
    return mongoOperations.exists(
        query(where(VersionedEntity.Fields.entityId).is(entityId)), CurveConfiguration.class);
  }

  public List<CurveConfigurationView> curveConfigurationViews(
      BitemporalDate stateDate, VersionedEntityFilter filter, Sort sort) {
    return entities(stateDate, filter, new Criteria(), sort).stream().map(toViewFn()).toList();
  }

  public List<CurveConfigurationInstrumentResolver> curveConfigInstrResolvers(
      BitemporalDate stateDate) {
    return entities(stateDate, active(), new Criteria()).stream()
        .map(CurveConfigurationInstrumentResolver::fromConfiguration)
        .toList();
  }

  public Either<ErrorItem, CurveConfigurationInstrumentResolver> curveConfigInstrResolver(
      BitemporalDate stateDate, String entityId) {
    return entity(entityId, stateDate, active())
        .map(CurveConfigurationInstrumentResolver::fromConfiguration);
  }

  public List<CurveConfigurationNameView> curveConfigForCurveGroupViews(
      LocalDate stateDate, String curveGroupId) {
    return entities(
            new BitemporalDate(stateDate),
            active(),
            where(CurveConfiguration.Fields.curveGroupId).is(curveGroupId))
        .stream()
        .map(mapper::from)
        .toList();
  }

  public DateList futureVersions(CurveConfigurationSearchForm form) {
    return futureVersionsByCriteria(uniqueEntityCriteria(form.getName()), form.getStateDate());
  }

  public List<CurveConfigurationView> versions(String entityId) {
    return entityVersions(entityId).stream().map(toViewFn()).toList();
  }

  public Either<ErrorItem, EntityId> update(
      String entityId, LocalDate versionDate, CurveConfigurationUpdateForm f) {
    return entityExact(entityId, versionDate)
        .map(e -> update(e, f.getVersionForm(), copiedEntity -> mapper.fromForm(f, copiedEntity)));
  }

  private Function<CurveConfiguration, CurveConfigurationView> toViewFn() {
    var groups =
        curveGroupRepository.curveGroupList().stream()
            .collect(Collectors.toMap(CurveGroupView::getId, CurveGroupView::getName));
    return c -> mapper.from(c, groups.get(c.getCurveGroupId()));
  }
}
