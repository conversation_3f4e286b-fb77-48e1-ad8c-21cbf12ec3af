package com.solum.xplain.core.curvegroup.conventions.index;

import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_1M;
import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_3M;
import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_6M;
import static com.opengamma.strata.basics.index.IborIndices.CAD_CDOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.CAD_CDOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.CZK_PRIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.CZK_PRIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.DKK_CIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.DKK_CIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.HUF_BUBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.HUF_BUBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.MXN_TIIE_13W;
import static com.opengamma.strata.basics.index.IborIndices.MXN_TIIE_26W;
import static com.opengamma.strata.basics.index.IborIndices.MXN_TIIE_4W;
import static com.opengamma.strata.basics.index.IborIndices.NOK_NIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.NOK_NIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.NZD_BKBM_3M;
import static com.opengamma.strata.basics.index.IborIndices.PLN_WIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.PLN_WIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.SEK_STIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.SEK_STIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.ZAR_JIBAR_3M;
import static com.opengamma.strata.basics.index.OvernightIndices.AUD_AONIA;
import static com.opengamma.strata.basics.index.OvernightIndices.BRL_CDI;
import static com.opengamma.strata.basics.index.OvernightIndices.CAD_CORRA;
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_EONIA;
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_ESTR;
import static com.opengamma.strata.basics.index.OvernightIndices.GBP_SONIA;
import static com.opengamma.strata.basics.index.OvernightIndices.JPY_TONAR;
import static com.opengamma.strata.basics.index.OvernightIndices.NOK_NOWA;
import static com.opengamma.strata.basics.index.OvernightIndices.NZD_NZIONA;
import static com.opengamma.strata.basics.index.OvernightIndices.THB_THOR;
import static com.opengamma.strata.basics.index.OvernightIndices.USD_FED_FUND;
import static com.opengamma.strata.basics.index.OvernightIndices.USD_SOFR;
import static com.opengamma.strata.product.index.type.IborFutureContractSpecs.EUR_EURIBOR_3M_IMM_ICE;
import static com.opengamma.strata.product.index.type.IborFutureContractSpecs.GBP_LIBOR_3M_IMM_ICE;
import static com.opengamma.strata.product.index.type.IborFutureContractSpecs.USD_LIBOR_3M_IMM_CME;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.CHF_FIXED_1Y_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.CHF_FIXED_1Y_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_6M;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.GBP_FIXED_3M_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.GBP_FIXED_6M_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.JPY_FIXED_6M_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.USD_FIXED_1Y_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.USD_FIXED_6M_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.JPY_LIBOR_1M_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.JPY_LIBOR_3M_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.USD_LIBOR_1M_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.USD_LIBOR_3M_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.OvernightIborSwapConventions.GBP_SONIA_OIS_1Y_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.OvernightIborSwapConventions.USD_FED_FUND_AA_LIBOR_3M;
import static com.solum.xplain.core.curvegroup.conventions.index.ImmutableIrIndexCurveConvention.irCurve;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.CZK_CZEONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.DKK_DESTR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.HUF_BUBORON;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.IDR_INDONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.ILS_SHIR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.KRW_KOFR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.MXN_F_TIIE;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.MYR_MYOR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.PLN_POLONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.PLN_WIRON;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.RUB_RUONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.SEK_SWESTR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.ZAR_ZARONIA;
import static com.solum.xplain.extensions.index.OffshoreIndices.CNY_REPO_OFFSHORE_1W;
import static com.solum.xplain.extensions.index.OffshoreIndices.ILS_SHIR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.INR_OMIBOR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.KRW_CD_OFFSHORE_13W;
import static com.solum.xplain.extensions.index.OffshoreIndices.KRW_KOFR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.MXN_F_TIIE_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.MXN_TIIE_OFFSHORE_4W;
import static com.solum.xplain.extensions.index.OffshoreIndices.MYR_KLIBOR_OFFSHORE_3M;
import static com.solum.xplain.extensions.index.OffshoreIndices.MYR_MYOR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.THB_THBFIX_OFFSHORE_6M;
import static com.solum.xplain.extensions.index.OffshoreIndices.THB_THOR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.TWD_TAIBOR_OFFSHORE_3M;
import static com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConventions.USD_SOFR_OIS_SOFR_3M;
import static com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConventions.USD_SOFR_OIS_SOFR_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.AED_FIXED_1Y_EIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.AUD_FIXED_3M_BBSW_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.AUD_FIXED_6M_BBSW_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CAD_FIXED_6M_CDOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CHF_FIXED_1Y_LIBOR_1M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CNH_FIXED_3M_HIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CNY_FIXED_3M_REPO_1W;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CNY_FIXED_3M_REPO_1W_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CNY_FIXED_3M_SHIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CZK_FIXED_1Y_PRIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CZK_FIXED_1Y_PRIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.DKK_FIXED_1Y_CIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.DKK_FIXED_1Y_CIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_1M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.GBP_FIXED_6M_LIBOR_1M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.HKD_FIXED_3M_HIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.HUF_FIXED_1Y_BUBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.HUF_FIXED_3M_BUBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.ILS_FIXED_1Y_TLBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.KRW_FIXED_3M_CD_13W;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.KRW_FIXED_3M_CD_13W_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.MXN_FIXED_13W_TIIE_13W;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.MXN_FIXED_26W_TIIE_26W;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.MXN_FIXED_4W_TIIE_4W;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.MXN_FIXED_4W_TIIE_4W_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.MYR_FIXED_3M_KLIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.MYR_FIXED_3M_KLIBOR_3M_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.NOK_FIXED_1Y_NIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.NOK_FIXED_1Y_NIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.NZD_FIXED_6M_BKBM_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.PLN_FIXED_1Y_WIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.PLN_FIXED_1Y_WIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.RUB_FIXED_1Y_MOSPRIME_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.SAR_FIXED_1Y_SAIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.SEK_FIXED_1Y_STIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.SGD_FIXED_1M_SOR_1M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.SGD_FIXED_3M_SOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.SGD_FIXED_6M_SOR_1M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.SGD_FIXED_6M_SOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.SGD_FIXED_6M_SOR_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.THB_FIXED_6M_THBFIX_6M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.THB_FIXED_6M_THBFIX_6M_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.TRY_FIXED_1Y_TRLIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.TWD_FIXED_3M_TAIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.TWD_FIXED_3M_TAIBOR_3M_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.ZAR_FIXED_3M_JIBAR_3M;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.AUD_FIXED_1Y_AONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.AUD_FIXED_SHORTTERM_AONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.AUD_FIXED_TERM_AONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.BRL_FIXED_TERM_CDI_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.BRL_FIXED_TERM_CDI_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CAD_FIXED_6M_CORRA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CAD_FIXED_SHORTTERM_CORRA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CAD_FIXED_TERM_CORRA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CHF_FIXED_1Y_SARON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CHF_FIXED_SHORTTERM_SARON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CHF_FIXED_TERM_SARON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CLP_FIXED_6M_TNA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CLP_FIXED_6M_TNA_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CLP_FIXED_SHORTTERM_TNA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CLP_FIXED_SHORTTERM_TNA_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CLP_FIXED_TERM_TNA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CLP_FIXED_TERM_TNA_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.COP_FIXED_3M_OIBR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.COP_FIXED_3M_OIBR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.COP_FIXED_SHORTTERM_OIBR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.COP_FIXED_SHORTTERM_OIBR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.COP_FIXED_TERM_OIBR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.COP_FIXED_TERM_OIBR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CZK_FIXED_1Y_CZEONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CZK_FIXED_SHORTTERM_CZEONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.CZK_FIXED_TERM_CZEONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.DKK_FIXED_1Y_DESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.DKK_FIXED_SHORTTERM_DESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.DKK_FIXED_TERM_DESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.EUR_FIXED_1Y_EONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.EUR_FIXED_1Y_ESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.EUR_FIXED_SHORTTERM_EONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.EUR_FIXED_SHORTTERM_ESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.EUR_FIXED_TERM_EONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.EUR_FIXED_TERM_ESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.GBP_FIXED_1Y_SONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.GBP_FIXED_SHORTTERM_SONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.GBP_FIXED_TERM_SONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.HKD_FIXED_3M_HONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.HKD_FIXED_SHORTTERM_HONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.HKD_FIXED_TERM_HONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.HUF_FIXED_1Y_BUBORON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.HUF_FIXED_SHORTTERM_BUBORON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.HUF_FIXED_TERM_BUBORON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.IDR_FIXED_1Y_INDONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.IDR_FIXED_SHORTTERM_INDONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.IDR_FIXED_TERM_INDONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ILS_FIXED_1Y_SHIR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ILS_FIXED_1Y_SHIR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ILS_FIXED_SHORTTERM_SHIR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ILS_FIXED_SHORTTERM_SHIR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ILS_FIXED_TERM_SHIR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ILS_FIXED_TERM_SHIR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.INR_FIXED_6M_OMIBOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.INR_FIXED_6M_OMIBOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.INR_FIXED_SHORTTERM_OMIBOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.INR_FIXED_SHORTTERM_OMIBOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.INR_FIXED_TERM_OMIBOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.INR_FIXED_TERM_OMIBOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.JPY_FIXED_1Y_TONAR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.JPY_FIXED_SHORTTERM_TONAR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.JPY_FIXED_TERM_TONAR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.KRW_FIXED_3M_KOFR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.KRW_FIXED_3M_KOFR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.KRW_FIXED_SHORTTERM_KOFR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.KRW_FIXED_SHORTTERM_KOFR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.KRW_FIXED_TERM_KOFR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.KRW_FIXED_TERM_KOFR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.MYR_FIXED_3M_MYOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.MYR_FIXED_3M_MYOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.MYR_FIXED_SHORTTERM_MYOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.MYR_FIXED_SHORTTERM_MYOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.MYR_FIXED_TERM_MYOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.MYR_FIXED_TERM_MYOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.NOK_FIXED_1Y_NOWA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.NOK_FIXED_SHORTTERM_NOWA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.NOK_FIXED_TERM_NOWA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.NZD_FIXED_1Y_NZIONA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.NZD_FIXED_SHORTTERM_NZIONA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.NZD_FIXED_TERM_NZIONA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.PLN_FIXED_1Y_POLONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.PLN_FIXED_1Y_WIRON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.PLN_FIXED_SHORTTERM_POLONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.PLN_FIXED_SHORTTERM_WIRON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.PLN_FIXED_TERM_POLONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.PLN_FIXED_TERM_WIRON_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.RUB_FIXED_1Y_RUONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.RUB_FIXED_SHORTTERM_RUONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.RUB_FIXED_TERM_RUONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.SEK_FIXED_1Y_SWESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.SEK_FIXED_SHORTTERM_SWESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.SEK_FIXED_TERM_SWESTR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.SGD_FIXED_6M_SORA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.SGD_FIXED_SHORTTERM_SORA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.SGD_FIXED_TERM_SORA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.THB_FIXED_3M_THOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.THB_FIXED_3M_THOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.THB_FIXED_SHORTTERM_THOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.THB_FIXED_SHORTTERM_THOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.THB_FIXED_TERM_THOR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.THB_FIXED_TERM_THOR_OIS_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.TRY_FIXED_3M_TLREF_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.TRY_FIXED_SHORTTERM_TLREF_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.TRY_FIXED_TERM_TLREF_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.USD_FIXED_1Y_FED_FUND_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.USD_FIXED_1Y_SOFR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.USD_FIXED_SHORTTERM_FED_FUND_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.USD_FIXED_SHORTTERM_SOFR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.USD_FIXED_TERM_FED_FUND_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.USD_FIXED_TERM_SOFR_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ZAR_FIXED_1Y_ZARONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ZAR_FIXED_SHORTTERM_ZARONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions.ZAR_FIXED_TERM_ZARONIA_OIS;
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.AUD_BBSW_3M_QUARTERLY_FUTURE;
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.AUD_BBSW_3M_QUARTERLY_FUTURE_SD;
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.CAD_CDOR_3M_IMM_MSE;
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.NZD_BKBM_3M_QUARTERLY_FUTURE;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AED_EIBOR_1M_EIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AED_EIBOR_3M_EIBOR_12M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AED_EIBOR_3M_EIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AUD_BBSW_1M_BBSW_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AUD_BBSW_3M_BBSW_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.CAD_CDOR_1M_CDOR_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.CHF_LIBOR_1M_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.CHF_LIBOR_3M_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.CZK_PRIBOR_3M_PRIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.DKK_CIBOR_3M_CIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.EUR_EURIBOR_1M_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.EUR_EURIBOR_3M_EURIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.EUR_EURIBOR_6M_EURIBOR_12M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.GBP_LIBOR_1M_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.GBP_LIBOR_3M_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.GBP_LIBOR_6M_LIBOR_12M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.HUF_BUBOR_3M_BUBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.NOK_NIBOR_3M_NIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.SEK_STIBOR_3M_STIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.SGD_SOR_1M_SOR_6M;
import static com.solum.xplain.extensions.product.ExtendedOvernightIborSwapConventions.CAD_CORRA_OIS_6M_CDOR_3M;
import static com.solum.xplain.extensions.product.ExtendedOvernightIborSwapConventions.EUR_EONIA_OIS_1Y_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedOvernightIborSwapConventions.EUR_ESTR_OIS_1Y_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedOvernightIborSwapConventions.USD_SOFR_OIS_3M_LIBOR;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T0;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T1;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T2;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.INR_DEPOSIT_T0;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.NZD_DEPOSIT_T0;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.NZD_DEPOSIT_T1;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.NZD_DEPOSIT_T2;
import static com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConventions.USD_SOFR_3M_TERM_OIS;
import static com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConventions.USD_SOFR_6M_TERM_OIS;
import static java.util.List.of;

import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.OvernightIndices;
import com.opengamma.strata.product.deposit.type.IborFixingDepositConvention;
import com.opengamma.strata.product.fra.type.FraConvention;
import com.opengamma.strata.product.index.type.IborFutureContractSpec;
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.extensions.immfra.ExtendedImmutableImmFraConventions;
import com.solum.xplain.extensions.index.OffshoreIndices;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import com.solum.xplain.extensions.product.ExtendedIborContractSpecs;
import java.util.List;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

// TODO: make this (and similar classes) singleton beans nand get things out of static context.
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IrIndexCurveConventions {
  private static final IborIndex AED_EIBOR_1M = IborIndex.of("AED-EIBOR-1M");
  private static final IborIndex AED_EIBOR_12M = IborIndex.of("AED-EIBOR-12M");
  private static final IborIndex AED_EIBOR_6M = IborIndex.of("AED-EIBOR-6M");
  private static final IborIndex AED_EIBOR_3M = IborIndex.of("AED-EIBOR-3M");
  private static final IborIndex CNY_REPO_1W = IborIndex.of("CNY-REPO-1W");
  private static final IborIndex HKD_HIBOR_3M = IborIndex.of("HKD-HIBOR-3M");
  private static final IborIndex ILS_TLBOR_3M = IborIndex.of("ILS-TLBOR-3M");
  private static final IborIndex KRW_CD_13W = IborIndex.of("KRW-CD-13W");
  private static final IborIndex MYR_KLIBOR_3M = IborIndex.of("MYR-KLIBOR-3M");
  private static final IborIndex SAR_SAIBOR_3M = IborIndex.of("SAR-SAIBOR-3M");
  private static final IborIndex SGD_SOR_1M = IborIndex.of("SGD-SOR-1M");
  private static final IborIndex SGD_SOR_3M = IborIndex.of("SGD-SOR-3M");
  private static final IborIndex SGD_SOR_6M = IborIndex.of("SGD-SOR-6M");
  private static final IborIndex THB_THBFIX_6M = IborIndex.of("THB-THBFIX-6M");
  private static final IborIndex TRY_TRILBOR_3M = IborIndex.of("TRY-TRLIBOR-3M");
  private static final IborIndex TWD_TAIBOR_3M = IborIndex.of("TWD-TAIBOR-3M");
  private static final IborIndex RUB_MOSPRIME_3M = IborIndex.of("RUB-MOSPRIME-3M");

  private static final OvernightIndex HKD_HONIA = OvernightIndex.of("HKD-HONIA");
  private static final OvernightIndex INR_OMIBOR = OvernightIndex.of("INR-OMIBOR");
  private static final OvernightIndex SGD_SORA = OvernightIndex.of("SGD-SORA");
  private static final OvernightIndex TRY_TLREF = OvernightIndex.of("TRY-TLREF");

  private static final OvernightTermIndex USD_SOFR_3M_INDEX = OvernightTermIndex.of("USD-SOFR-3M");
  private static final OvernightTermIndex USD_SOFR_6M_INDEX = OvernightTermIndex.of("USD-SOFR-6M");

  // AED

  private static final ImmutableIrIndexCurveConvention AED_1M =
      irCurve(
          "AED 1M",
          AED_EIBOR_1M,
          AED_EIBOR_1M_EIBOR_3M,
          IborFixingDepositConvention.of(AED_EIBOR_1M));

  private static final ImmutableIrIndexCurveConvention AED_3M =
      irCurve(
          "AED 3M",
          AED_EIBOR_3M,
          AED_FIXED_1Y_EIBOR_3M,
          FraConvention.of(AED_EIBOR_3M),
          IborFixingDepositConvention.of(AED_EIBOR_3M));

  private static final ImmutableIrIndexCurveConvention AED_6M =
      irCurve(
          "AED 6M",
          AED_EIBOR_6M,
          AED_EIBOR_3M_EIBOR_6M,
          IborFixingDepositConvention.of(AED_EIBOR_6M));

  private static final ImmutableIrIndexCurveConvention AED_12M =
      irCurve(
          "AED 12M",
          AED_EIBOR_12M,
          AED_EIBOR_3M_EIBOR_12M,
          IborFixingDepositConvention.of(AED_EIBOR_12M));

  // AUD
  private static final ImmutableIrIndexCurveConvention AUD_OIS =
      irCurve(
          "AUD AONIA",
          AUD_AONIA,
          AUD_FIXED_TERM_AONIA_OIS,
          AUD_FIXED_1Y_AONIA_OIS,
          AUD_FIXED_SHORTTERM_AONIA_OIS);

  private static final ImmutableIrIndexCurveConvention AUD_1M =
      irCurve(
          "AUD 1M",
          AUD_BBSW_1M,
          AUD_BBSW_1M_BBSW_3M,
          AUD_DEPOSIT_T0,
          AUD_DEPOSIT_T1,
          AUD_DEPOSIT_T2,
          IborFixingDepositConvention.of(AUD_BBSW_1M));

  private static final ImmutableIrIndexCurveConvention AUD_3M =
      irCurve(
          "AUD 3M",
          AUD_BBSW_3M,
          AUD_BBSW_1M_BBSW_3M,
          AUD_BBSW_3M_BBSW_6M,
          AUD_DEPOSIT_T0,
          AUD_DEPOSIT_T1,
          AUD_DEPOSIT_T2,
          AUD_BBSW_3M_QUARTERLY_FUTURE,
          AUD_BBSW_3M_QUARTERLY_FUTURE_SD,
          AUD_FIXED_3M_BBSW_3M,
          IborFixingDepositConvention.of(AUD_BBSW_3M));

  private static final ImmutableIrIndexCurveConvention AUD_6M =
      irCurve(
          "AUD 6M",
          AUD_BBSW_6M,
          AUD_FIXED_6M_BBSW_6M,
          AUD_BBSW_3M_BBSW_6M,
          FraConvention.of(AUD_BBSW_6M),
          AUD_DEPOSIT_T0,
          AUD_DEPOSIT_T1,
          AUD_DEPOSIT_T2,
          IborFixingDepositConvention.of(AUD_BBSW_6M));

  // BRL
  private static final ImmutableIrIndexCurveConvention BRL_OIS =
      irCurve("BRL CDI", BRL_CDI, BRL_FIXED_TERM_CDI_OIS);

  private static final ImmutableIrIndexCurveConvention BRL_CDI_OFFSHORE =
      irCurve(
          "BRL CDI Offshore", OffshoreIndices.BRL_CDI_OFFSHORE, BRL_FIXED_TERM_CDI_OIS_OFFSHORE);

  // CAD
  private static final ImmutableIrIndexCurveConvention CAD_OIS =
      irCurve(
          "CAD CORRA",
          CAD_CORRA,
          CAD_FIXED_TERM_CORRA_OIS,
          CAD_FIXED_6M_CORRA_OIS,
          CAD_FIXED_SHORTTERM_CORRA_OIS,
          CAD_CORRA_OIS_6M_CDOR_3M);

  private static final ImmutableIrIndexCurveConvention CAD_1M =
      irCurve(
          "CAD 1M", CAD_CDOR_1M, CAD_CDOR_1M_CDOR_3M, IborFixingDepositConvention.of(CAD_CDOR_1M));

  private static final ImmutableIrIndexCurveConvention CAD_3M =
      irCurve(
          "CAD 3M",
          CAD_CDOR_3M,
          CAD_FIXED_6M_CDOR_3M,
          CAD_CDOR_3M_IMM_MSE,
          IborFixingDepositConvention.of(CAD_CDOR_3M));

  // CHF

  private static final ImmutableIrIndexCurveConvention CHF_OIS =
      irCurve(
          "CHF SARON",
          OvernightIndices.CHF_SARON,
          CHF_FIXED_TERM_SARON_OIS,
          CHF_FIXED_1Y_SARON_OIS,
          CHF_FIXED_SHORTTERM_SARON_OIS);

  private static final ImmutableIrIndexCurveConvention CHF_1M =
      irCurve(
          "CHF 1M",
          CHF_LIBOR_1M,
          CHF_FIXED_1Y_LIBOR_1M,
          CHF_LIBOR_1M_LIBOR_6M,
          IborFixingDepositConvention.of(CHF_LIBOR_1M));

  private static final ImmutableIrIndexCurveConvention CHF_3M =
      irCurve(
          "CHF 3M",
          CHF_LIBOR_3M,
          CHF_FIXED_1Y_LIBOR_3M,
          IborFutureContractSpec.of("CHF-LIBOR-3M-IMM-ICE"),
          CHF_LIBOR_3M,
          CHF_LIBOR_3M_LIBOR_6M,
          IborFixingDepositConvention.of(CHF_LIBOR_3M));

  private static final ImmutableIrIndexCurveConvention CHF_6M =
      irCurve(
          "CHF 6M",
          CHF_LIBOR_6M,
          CHF_FIXED_1Y_LIBOR_3M,
          FraConvention.of(CHF_LIBOR_6M),
          CHF_FIXED_1Y_LIBOR_6M,
          CHF_LIBOR_3M_LIBOR_6M,
          CHF_LIBOR_1M_LIBOR_6M,
          IborFixingDepositConvention.of(CHF_LIBOR_6M));

  // CNY
  private static final ImmutableIrIndexCurveConvention CNY_1W =
      irCurve(
          "CNY 1W", CNY_REPO_1W, CNY_FIXED_3M_REPO_1W, IborFixingDepositConvention.of(CNY_REPO_1W));

  private static final ImmutableIrIndexCurveConvention CNY_1W_OFFSHORE =
      irCurve(
          "CNY 1W Offshore",
          CNY_REPO_OFFSHORE_1W,
          CNY_FIXED_3M_REPO_1W_OFFSHORE,
          IborFixingDepositConvention.of(CNY_REPO_1W));

  private static final ImmutableIrIndexCurveConvention CNY_3M =
      irCurve(
          "CNY 3M",
          IborIndex.of("CNY-SHIBOR-3M"),
          IborFixingDepositConvention.of(IborIndex.of("CNY-SHIBOR-3M")),
          CNY_FIXED_3M_SHIBOR_3M);

  private static final ImmutableIrIndexCurveConvention CNH_3M =
      irCurve(
          "CNH 3M",
          IborIndex.of("CNH-HIBOR-3M"),
          IborFixingDepositConvention.of(IborIndex.of("CNH-HIBOR-3M")),
          CNH_FIXED_3M_HIBOR_3M);

  // CZK
  private static final ImmutableIrIndexCurveConvention CZK_OIS =
      irCurve(
          "CZK CZEONIA",
          CZK_CZEONIA,
          CZK_FIXED_TERM_CZEONIA_OIS,
          CZK_FIXED_1Y_CZEONIA_OIS,
          CZK_FIXED_SHORTTERM_CZEONIA_OIS);

  private static final ImmutableIrIndexCurveConvention CZK_6M =
      irCurve(
          "CZK 6M",
          CZK_PRIBOR_6M,
          CZK_FIXED_1Y_PRIBOR_6M,
          FraConvention.of(CZK_PRIBOR_6M),
          IborFixingDepositConvention.of(CZK_PRIBOR_6M));

  private static final ImmutableIrIndexCurveConvention CZK_3M =
      irCurve(
          "CZK 3M",
          CZK_PRIBOR_3M,
          CZK_FIXED_1Y_PRIBOR_3M,
          CZK_PRIBOR_3M_PRIBOR_6M,
          FraConvention.of(CZK_PRIBOR_3M),
          IborFixingDepositConvention.of(CZK_PRIBOR_3M));

  // DKK
  private static final ImmutableIrIndexCurveConvention DKK_3M =
      irCurve(
          "DKK 3M",
          DKK_CIBOR_3M,
          DKK_CIBOR_3M_CIBOR_6M,
          DKK_FIXED_1Y_CIBOR_3M,
          ExtendedImmutableImmFraConventions.DKK_CIBOR_3M_IMM,
          IborFixingDepositConvention.of(DKK_CIBOR_3M));

  private static final ImmutableIrIndexCurveConvention DKK_6M =
      irCurve(
          "DKK 6M",
          DKK_CIBOR_6M,
          DKK_FIXED_1Y_CIBOR_6M,
          FraConvention.of(DKK_CIBOR_6M),
          ExtendedImmutableImmFraConventions.DKK_CIBOR_6M_IMM,
          IborFixingDepositConvention.of(DKK_CIBOR_6M));

  // EUR
  private static final ImmutableIrIndexCurveConvention EUR_EONIA_OIS =
      irCurve(
          "EUR EONIA",
          EUR_EONIA,
          EUR_EONIA_OIS_1Y_EURIBOR_3M,
          EUR_FIXED_TERM_EONIA_OIS,
          EUR_FIXED_1Y_EONIA_OIS,
          EUR_FIXED_SHORTTERM_EONIA_OIS);

  private static final ImmutableIrIndexCurveConvention EUR_ESTR_OIS =
      irCurve(
          "EUR ESTR",
          EUR_ESTR,
          EUR_ESTR_OIS_1Y_EURIBOR_3M,
          EUR_FIXED_1Y_ESTR_OIS,
          EUR_FIXED_TERM_ESTR_OIS,
          EUR_FIXED_SHORTTERM_ESTR_OIS);

  private static final ImmutableIrIndexCurveConvention EUR_1M =
      irCurve(
          "EUR 1M",
          EUR_EURIBOR_1M,
          EUR_FIXED_1Y_EURIBOR_1M,
          EUR_EURIBOR_1M_EURIBOR_3M,
          IborFixingDepositConvention.of(EUR_EURIBOR_1M));

  private static final ImmutableIrIndexCurveConvention EUR_3M =
      irCurve(
          "EUR 3M",
          EUR_EURIBOR_3M,
          EUR_EURIBOR_3M_IMM_ICE,
          EUR_FIXED_1Y_EURIBOR_3M,
          EUR_EURIBOR_1M_EURIBOR_3M,
          EUR_EURIBOR_3M_EURIBOR_6M,
          IborFixingDepositConvention.of(EUR_EURIBOR_3M));

  private static final ImmutableIrIndexCurveConvention EUR_6M =
      irCurve(
          "EUR 6M",
          EUR_EURIBOR_6M,
          EUR_FIXED_1Y_EURIBOR_6M,
          EUR_EURIBOR_3M_EURIBOR_6M,
          EUR_EURIBOR_6M_EURIBOR_12M,
          FraConvention.of(EUR_EURIBOR_6M),
          IborFixingDepositConvention.of(EUR_EURIBOR_6M));

  private static final ImmutableIrIndexCurveConvention EUR_12M =
      irCurve(
          "EUR 12M",
          EUR_EURIBOR_12M,
          EUR_EURIBOR_6M_EURIBOR_12M,
          IborFixingDepositConvention.of(EUR_EURIBOR_12M));

  // GBP
  private static final ImmutableIrIndexCurveConvention GBP_OIS =
      irCurve(
          "GBP SONIA",
          GBP_SONIA,
          GBP_FIXED_TERM_SONIA_OIS,
          GBP_FIXED_1Y_SONIA_OIS,
          GBP_FIXED_SHORTTERM_SONIA_OIS,
          GBP_SONIA_OIS_1Y_LIBOR_3M);

  private static final ImmutableIrIndexCurveConvention GBP_1M =
      irCurve(
          "GBP 1M",
          GBP_LIBOR_1M,
          GBP_FIXED_6M_LIBOR_1M,
          GBP_LIBOR_1M_LIBOR_3M,
          IborFixingDepositConvention.of(GBP_LIBOR_1M));

  private static final ImmutableIrIndexCurveConvention GBP_3M =
      irCurve(
          "GBP 3M",
          GBP_LIBOR_3M,
          GBP_FIXED_3M_LIBOR_3M,
          GBP_LIBOR_3M_IMM_ICE,
          GBP_LIBOR_3M_LIBOR_6M,
          GBP_LIBOR_1M_LIBOR_3M,
          IborFixingDepositConvention.of(GBP_LIBOR_3M));

  private static final ImmutableIrIndexCurveConvention GBP_6M =
      irCurve(
          "GBP 6M",
          GBP_LIBOR_6M,
          GBP_FIXED_6M_LIBOR_6M,
          GBP_LIBOR_3M_LIBOR_6M,
          GBP_LIBOR_6M_LIBOR_12M,
          FraConvention.of(GBP_LIBOR_6M),
          IborFixingDepositConvention.of(GBP_LIBOR_6M));

  private static final ImmutableIrIndexCurveConvention GBP_12M =
      irCurve(
          "GBP 12M",
          GBP_LIBOR_12M,
          GBP_LIBOR_6M_LIBOR_12M,
          IborFixingDepositConvention.of(GBP_LIBOR_12M));

  // HKD
  private static final ImmutableIrIndexCurveConvention HKD_OIS =
      irCurve(
          "HKD HONIA",
          HKD_HONIA,
          HKD_FIXED_TERM_HONIA_OIS,
          HKD_FIXED_3M_HONIA_OIS,
          HKD_FIXED_SHORTTERM_HONIA_OIS);

  private static final ImmutableIrIndexCurveConvention HKD_3M =
      irCurve(
          "HKD 3M",
          HKD_HIBOR_3M,
          HKD_FIXED_3M_HIBOR_3M,
          FraConvention.of(HKD_HIBOR_3M),
          IborFixingDepositConvention.of(HKD_HIBOR_3M));

  // HUF
  private static final ImmutableIrIndexCurveConvention HUF_6M =
      irCurve(
          "HUF 6M",
          HUF_BUBOR_6M,
          HUF_FIXED_1Y_BUBOR_6M,
          FraConvention.of(HUF_BUBOR_6M),
          IborFixingDepositConvention.of(HUF_BUBOR_6M));

  private static final ImmutableIrIndexCurveConvention HUF_3M =
      irCurve(
          "HUF 3M",
          HUF_BUBOR_3M,
          HUF_FIXED_3M_BUBOR_3M,
          HUF_BUBOR_3M_BUBOR_6M,
          FraConvention.of(HUF_BUBOR_3M),
          IborFixingDepositConvention.of(HUF_BUBOR_3M));

  // ILS
  private static final ImmutableIrIndexCurveConvention ILS_3M =
      irCurve(
          "ILS 3M",
          ILS_TLBOR_3M,
          ILS_FIXED_1Y_TLBOR_3M,
          FraConvention.of(ILS_TLBOR_3M),
          IborFixingDepositConvention.of(ILS_TLBOR_3M));

  // IDR
  private static final ImmutableIrIndexCurveConvention IDR_OIS =
      irCurve(
          "IDR INDONIA",
          IDR_INDONIA,
          IDR_FIXED_TERM_INDONIA_OIS,
          IDR_FIXED_1Y_INDONIA_OIS,
          IDR_FIXED_SHORTTERM_INDONIA_OIS);

  // INR
  private static final ImmutableIrIndexCurveConvention INR_OIS =
      irCurve(
          "INR OMIBOR",
          INR_OMIBOR,
          INR_FIXED_TERM_OMIBOR_OIS,
          INR_FIXED_6M_OMIBOR_OIS,
          INR_FIXED_SHORTTERM_OMIBOR_OIS,
          INR_DEPOSIT_T0);

  private static final ImmutableIrIndexCurveConvention INR_OIS_OFFSHORE =
      irCurve(
          "INR OMIBOR Offshore",
          INR_OMIBOR_OFFSHORE,
          INR_FIXED_6M_OMIBOR_OIS_OFFSHORE,
          INR_FIXED_SHORTTERM_OMIBOR_OIS_OFFSHORE,
          INR_FIXED_TERM_OMIBOR_OIS_OFFSHORE);

  // JPY
  private static final ImmutableIrIndexCurveConvention JPY_OIS =
      irCurve(
          "JPY TONAR",
          JPY_TONAR,
          JPY_FIXED_TERM_TONAR_OIS,
          JPY_FIXED_1Y_TONAR_OIS,
          JPY_FIXED_SHORTTERM_TONAR_OIS);

  private static final ImmutableIrIndexCurveConvention JPY_1M =
      irCurve(
          "JPY 1M",
          JPY_LIBOR_1M,
          JPY_LIBOR_1M_LIBOR_6M,
          IborFixingDepositConvention.of(JPY_LIBOR_1M));

  private static final ImmutableIrIndexCurveConvention JPY_3M =
      irCurve(
          "JPY 3M",
          JPY_LIBOR_3M,
          JPY_LIBOR_3M_LIBOR_6M,
          IborFixingDepositConvention.of(JPY_LIBOR_3M));

  private static final ImmutableIrIndexCurveConvention JPY_6M =
      irCurve(
          "JPY 6M",
          JPY_LIBOR_6M,
          JPY_FIXED_6M_LIBOR_6M,
          FraConvention.of(JPY_LIBOR_6M),
          IborFixingDepositConvention.of(JPY_LIBOR_6M));

  // KRW
  private static final ImmutableIrIndexCurveConvention KRW_13W =
      irCurve(
          "KRW 13W", KRW_CD_13W, KRW_FIXED_3M_CD_13W, IborFixingDepositConvention.of(KRW_CD_13W));

  private static final ImmutableIrIndexCurveConvention KRW_13W_OFFSHORE =
      irCurve(
          "KRW 13W Offshore",
          KRW_CD_OFFSHORE_13W,
          KRW_FIXED_3M_CD_13W_OFFSHORE,
          IborFixingDepositConvention.of(KRW_CD_13W));

  // MXN
  private static final ImmutableIrIndexCurveConvention MXN_OIS =
      irCurve(
          "MXN F TIIE",
          MXN_F_TIIE,
          FixedOvernightSwapConvention.of("MXN-FIXED-TERM-F-TIIE-OIS"),
          FixedOvernightSwapConvention.of("MXN-FIXED-4W-F-TIIE-OIS"),
          FixedOvernightSwapConvention.of("MXN-FIXED-SHORTTERM-F-TIIE-OIS"));

  private static final ImmutableIrIndexCurveConvention MXN_OIS_OFFSHORE =
      irCurve(
          "MXN F TIIE Offshore",
          MXN_F_TIIE_OFFSHORE,
          FixedOvernightSwapConvention.of("MXN-FIXED-TERM-F-TIIE-OIS-OFFSHORE"),
          FixedOvernightSwapConvention.of("MXN-FIXED-4W-F-TIIE-OIS-OFFSHORE"),
          FixedOvernightSwapConvention.of("MXN-FIXED-SHORTTERM-F-TIIE-OIS-OFFSHORE"));

  private static final ImmutableIrIndexCurveConvention MXN_4W =
      irCurve(
          "MXN 4W", MXN_TIIE_4W, MXN_FIXED_4W_TIIE_4W, IborFixingDepositConvention.of(MXN_TIIE_4W));

  private static final ImmutableIrIndexCurveConvention MXN_4W_OFFSHORE =
      irCurve(
          "MXN 4W Offshore",
          MXN_TIIE_OFFSHORE_4W,
          MXN_FIXED_4W_TIIE_4W_OFFSHORE,
          IborFixingDepositConvention.of(MXN_TIIE_4W));

  private static final ImmutableIrIndexCurveConvention MXN_13W =
      irCurve(
          "MXN 13W",
          MXN_TIIE_13W,
          MXN_FIXED_13W_TIIE_13W,
          IborFixingDepositConvention.of(MXN_TIIE_13W));

  private static final ImmutableIrIndexCurveConvention MXN_26W =
      irCurve(
          "MXN 26W",
          MXN_TIIE_26W,
          MXN_FIXED_26W_TIIE_26W,
          IborFixingDepositConvention.of(MXN_TIIE_26W));

  // MYR
  private static final ImmutableIrIndexCurveConvention MYR_OIS =
      irCurve(
          "MYR MYOR",
          MYR_MYOR,
          MYR_FIXED_TERM_MYOR_OIS,
          MYR_FIXED_3M_MYOR_OIS,
          MYR_FIXED_SHORTTERM_MYOR_OIS);

  private static final ImmutableIrIndexCurveConvention MYR_OIS_OFFSHORE =
      irCurve(
          "MYR MYOR Offshore",
          MYR_MYOR_OFFSHORE,
          MYR_FIXED_TERM_MYOR_OIS_OFFSHORE,
          MYR_FIXED_3M_MYOR_OIS_OFFSHORE,
          MYR_FIXED_SHORTTERM_MYOR_OIS_OFFSHORE);

  private static final ImmutableIrIndexCurveConvention MYR_3M =
      irCurve(
          "MYR 3M",
          MYR_KLIBOR_3M,
          MYR_FIXED_3M_KLIBOR_3M,
          IborFixingDepositConvention.of(MYR_KLIBOR_3M));

  private static final ImmutableIrIndexCurveConvention MYR_3M_OFFSHORE =
      irCurve(
          "MYR 3M Offshore",
          MYR_KLIBOR_OFFSHORE_3M,
          MYR_FIXED_3M_KLIBOR_3M_OFFSHORE,
          IborFixingDepositConvention.of(MYR_KLIBOR_3M));

  // NOK

  private static final ImmutableIrIndexCurveConvention NOK_6M =
      irCurve(
          "NOK 6M",
          NOK_NIBOR_6M,
          NOK_FIXED_1Y_NIBOR_6M,
          FraConvention.of(NOK_NIBOR_6M),
          ExtendedImmutableImmFraConventions.NOK_NIBOR_6M_IMM,
          IborFixingDepositConvention.of(NOK_NIBOR_6M));

  private static final ImmutableIrIndexCurveConvention NOK_3M =
      irCurve(
          "NOK 3M",
          NOK_NIBOR_3M,
          NOK_FIXED_1Y_NIBOR_3M,
          NOK_NIBOR_3M_NIBOR_6M,
          FraConvention.of(NOK_NIBOR_3M),
          ExtendedImmutableImmFraConventions.NOK_NIBOR_3M_IMM,
          IborFixingDepositConvention.of(NOK_NIBOR_3M));

  // NZD
  private static final ImmutableIrIndexCurveConvention NZD_NZIONA_OIS =
      irCurve(
          "NZD NZIONA",
          NZD_NZIONA,
          NZD_FIXED_TERM_NZIONA_OIS,
          NZD_FIXED_1Y_NZIONA_OIS,
          NZD_FIXED_SHORTTERM_NZIONA_OIS);

  private static final ImmutableIrIndexCurveConvention NZD_3M =
      irCurve(
          "NZD 3M",
          NZD_BKBM_3M,
          NZD_FIXED_6M_BKBM_3M,
          NZD_BKBM_3M_QUARTERLY_FUTURE,
          NZD_DEPOSIT_T0,
          NZD_DEPOSIT_T1,
          NZD_DEPOSIT_T2,
          IborFixingDepositConvention.of(NZD_BKBM_3M));

  // PLN
  private static final ImmutableIrIndexCurveConvention PLN_WIRON_OIS =
      irCurve(
          "PLN WIRON",
          PLN_WIRON,
          PLN_FIXED_TERM_WIRON_OIS,
          PLN_FIXED_1Y_WIRON_OIS,
          PLN_FIXED_SHORTTERM_WIRON_OIS);

  // PLN
  private static final ImmutableIrIndexCurveConvention PLN_OIS =
      irCurve(
          "PLN POLONIA",
          PLN_POLONIA,
          PLN_FIXED_1Y_POLONIA_OIS,
          PLN_FIXED_TERM_POLONIA_OIS,
          PLN_FIXED_SHORTTERM_POLONIA_OIS);

  private static final ImmutableIrIndexCurveConvention PLN_3M =
      irCurve(
          "PLN 3M",
          PLN_WIBOR_3M,
          PLN_FIXED_1Y_WIBOR_3M,
          FraConvention.of(PLN_WIBOR_3M),
          IborFixingDepositConvention.of(PLN_WIBOR_3M));

  private static final ImmutableIrIndexCurveConvention PLN_6M =
      irCurve(
          "PLN 6M",
          PLN_WIBOR_6M,
          PLN_FIXED_1Y_WIBOR_6M,
          FraConvention.of(PLN_WIBOR_6M),
          IborFixingDepositConvention.of(PLN_WIBOR_6M));

  // SAR
  private static final ImmutableIrIndexCurveConvention SAR_3M =
      irCurve(
          "SAR 3M",
          SAR_SAIBOR_3M,
          SAR_FIXED_1Y_SAIBOR_3M,
          FraConvention.of(SAR_SAIBOR_3M),
          IborFixingDepositConvention.of(SAR_SAIBOR_3M));

  // SEK
  private static final ImmutableIrIndexCurveConvention SEK_3M =
      irCurve(
          "SEK 3M",
          SEK_STIBOR_3M,
          SEK_FIXED_1Y_STIBOR_3M,
          ExtendedIborContractSpecs.SEK_STIBOR_3M_IMM,
          ExtendedImmutableImmFraConventions.SEK_STIBOR_3M_IMM,
          IborFixingDepositConvention.of(SEK_STIBOR_3M));

  private static final ImmutableIrIndexCurveConvention SEK_6M =
      irCurve(
          "SEK 6M",
          SEK_STIBOR_6M,
          SEK_STIBOR_3M_STIBOR_6M,
          IborFixingDepositConvention.of(SEK_STIBOR_6M));

  // SGD
  private static final ImmutableIrIndexCurveConvention SGD_SORA_OIS =
      irCurve(
          "SGD SORA",
          SGD_SORA,
          SGD_FIXED_TERM_SORA_OIS,
          SGD_FIXED_6M_SORA_OIS,
          SGD_FIXED_SHORTTERM_SORA_OIS);

  private static final ImmutableIrIndexCurveConvention SGD_1M =
      irCurve(
          "SGD 1M",
          SGD_SOR_1M,
          SGD_FIXED_6M_SOR_1M,
          SGD_FIXED_1M_SOR_1M,
          SGD_SOR_1M_SOR_6M,
          IborFixingDepositConvention.of(SGD_SOR_1M));

  private static final ImmutableIrIndexCurveConvention SGD_3M =
      irCurve(
          "SGD 3M",
          SGD_SOR_3M,
          SGD_FIXED_3M_SOR_3M,
          SGD_FIXED_6M_SOR_3M,
          IborFixingDepositConvention.of(SGD_SOR_3M));

  private static final ImmutableIrIndexCurveConvention SGD_6M =
      irCurve(
          "SGD 6M",
          SGD_SOR_6M,
          SGD_FIXED_6M_SOR_6M,
          SGD_SOR_1M_SOR_6M,
          IborFixingDepositConvention.of(SGD_SOR_6M));

  // THB
  private static final ImmutableIrIndexCurveConvention THB_THOR_OIS =
      irCurve(
          "THB THOR",
          THB_THOR,
          THB_FIXED_TERM_THOR_OIS,
          THB_FIXED_3M_THOR_OIS,
          THB_FIXED_SHORTTERM_THOR_OIS);

  private static final ImmutableIrIndexCurveConvention THB_THOR_OIS_OFFSHORE =
      irCurve(
          "THB THOR Offshore",
          THB_THOR_OFFSHORE,
          THB_FIXED_3M_THOR_OIS_OFFSHORE,
          THB_FIXED_SHORTTERM_THOR_OIS_OFFSHORE,
          THB_FIXED_TERM_THOR_OIS_OFFSHORE);

  private static final ImmutableIrIndexCurveConvention THB_6M =
      irCurve(
          "THB 6M",
          THB_THBFIX_6M,
          THB_FIXED_6M_THBFIX_6M,
          IborFixingDepositConvention.of(THB_THBFIX_6M));

  private static final ImmutableIrIndexCurveConvention THB_6M_OFFSHORE =
      irCurve(
          "THB 6M Offshore",
          THB_THBFIX_OFFSHORE_6M,
          THB_FIXED_6M_THBFIX_6M_OFFSHORE,
          IborFixingDepositConvention.of(THB_THBFIX_6M));

  // TRY
  private static final ImmutableIrIndexCurveConvention TRY_TLREF_OIS =
      irCurve(
          "TRY TLREF",
          TRY_TLREF,
          TRY_FIXED_TERM_TLREF_OIS,
          TRY_FIXED_3M_TLREF_OIS,
          TRY_FIXED_SHORTTERM_TLREF_OIS);

  private static final ImmutableIrIndexCurveConvention TRY_3M =
      irCurve(
          "TRY 3M",
          TRY_TRILBOR_3M,
          TRY_FIXED_1Y_TRLIBOR_3M,
          IborFixingDepositConvention.of(TRY_TRILBOR_3M));

  // TWD
  private static final ImmutableIrIndexCurveConvention TWD_3M =
      irCurve(
          "TWD 3M",
          TWD_TAIBOR_3M,
          TWD_FIXED_3M_TAIBOR_3M,
          IborFixingDepositConvention.of(TWD_TAIBOR_3M));

  private static final ImmutableIrIndexCurveConvention TWD_3M_OFFSHORE =
      irCurve(
          "TWD 3M Offshore",
          TWD_TAIBOR_OFFSHORE_3M,
          TWD_FIXED_3M_TAIBOR_3M_OFFSHORE,
          IborFixingDepositConvention.of(TWD_TAIBOR_3M));

  // USD
  private static final ImmutableIrIndexCurveConvention USD_FEDFUNDS_OIS =
      irCurve(
          "USD FEDFUNDS",
          USD_FED_FUND,
          USD_FED_FUND_AA_LIBOR_3M,
          USD_FIXED_TERM_FED_FUND_OIS,
          USD_FIXED_1Y_FED_FUND_OIS,
          USD_FIXED_SHORTTERM_FED_FUND_OIS);

  private static final ImmutableIrIndexCurveConvention USD_SOFR_OIS =
      irCurve(
          "USD SOFR",
          USD_SOFR,
          USD_SOFR_OIS_3M_LIBOR,
          USD_FIXED_1Y_SOFR_OIS,
          USD_FIXED_TERM_SOFR_OIS,
          USD_FIXED_SHORTTERM_SOFR_OIS);

  private static final ImmutableIrIndexCurveConvention USD_1M =
      irCurve(
          "USD 1M",
          USD_LIBOR_1M,
          USD_LIBOR_1M_LIBOR_3M,
          IborFixingDepositConvention.of(USD_LIBOR_1M));

  private static final ImmutableIrIndexCurveConvention USD_3M =
      irCurve(
          "USD 3M",
          USD_LIBOR_3M,
          USD_LIBOR_3M_IMM_CME,
          USD_FIXED_6M_LIBOR_3M,
          USD_FIXED_1Y_LIBOR_3M,
          IborFixingDepositConvention.of(USD_LIBOR_3M));

  private static final ImmutableIrIndexCurveConvention USD_6M =
      irCurve(
          "USD 6M",
          USD_LIBOR_6M,
          USD_LIBOR_3M_LIBOR_6M,
          IborFixingDepositConvention.of(USD_LIBOR_6M));

  private static final ImmutableIrIndexCurveConvention USD_SOFR_3M =
      irCurve("USD SOFR 3M", USD_SOFR_3M_INDEX, USD_SOFR_3M_TERM_OIS, USD_SOFR_OIS_SOFR_3M);

  private static final ImmutableIrIndexCurveConvention USD_SOFR_6M =
      irCurve("USD SOFR 6M", USD_SOFR_6M_INDEX, USD_SOFR_6M_TERM_OIS, USD_SOFR_OIS_SOFR_6M);

  // ZAR
  private static final ImmutableIrIndexCurveConvention ZAR_OIS =
      irCurve(
          "ZAR ZARONIA",
          ZAR_ZARONIA,
          ZAR_FIXED_TERM_ZARONIA_OIS,
          ZAR_FIXED_1Y_ZARONIA_OIS,
          ZAR_FIXED_SHORTTERM_ZARONIA_OIS);

  private static final ImmutableIrIndexCurveConvention ZAR_3M =
      irCurve(
          "ZAR 3M",
          ZAR_JIBAR_3M,
          ZAR_FIXED_3M_JIBAR_3M,
          FraConvention.of(ZAR_JIBAR_3M),
          IborFixingDepositConvention.of(ZAR_JIBAR_3M));

  // COP
  private static final ImmutableIrIndexCurveConvention COP_OIBR =
      irCurve(
          "COP OIBR",
          OvernightIndex.of("COP-OIBR"),
          COP_FIXED_3M_OIBR_OIS,
          COP_FIXED_SHORTTERM_OIBR_OIS,
          COP_FIXED_TERM_OIBR_OIS);

  private static final ImmutableIrIndexCurveConvention COP_OIBR_OFFSHORE =
      irCurve(
          "COP OIBR Offshore",
          OffshoreIndices.COP_OIBR_OFFSHORE,
          COP_FIXED_3M_OIBR_OIS_OFFSHORE,
          COP_FIXED_SHORTTERM_OIBR_OIS_OFFSHORE,
          COP_FIXED_TERM_OIBR_OIS_OFFSHORE);

  // CLP
  private static final ImmutableIrIndexCurveConvention CLP_TNA =
      irCurve(
          "CLP TNA",
          OvernightIndex.of("CLP-TNA"),
          CLP_FIXED_6M_TNA_OIS,
          CLP_FIXED_TERM_TNA_OIS,
          CLP_FIXED_SHORTTERM_TNA_OIS);

  private static final ImmutableIrIndexCurveConvention CLP_TNA_OFFSHORE =
      irCurve(
          "CLP TNA Offshore",
          OffshoreIndices.CLP_TNA_OFFSHORE,
          CLP_FIXED_6M_TNA_OIS_OFFSHORE,
          CLP_FIXED_TERM_TNA_OIS_OFFSHORE,
          CLP_FIXED_SHORTTERM_TNA_OIS_OFFSHORE);

  // NOK
  private static final ImmutableIrIndexCurveConvention NOK_OIS =
      irCurve(
          "NOK NOWA",
          NOK_NOWA,
          NOK_FIXED_1Y_NOWA_OIS,
          NOK_FIXED_TERM_NOWA_OIS,
          NOK_FIXED_SHORTTERM_NOWA_OIS);
  // SEK
  private static final ImmutableIrIndexCurveConvention SEK_OIS =
      irCurve(
          "SEK SWESTR",
          SEK_SWESTR,
          SEK_FIXED_1Y_SWESTR_OIS,
          SEK_FIXED_TERM_SWESTR_OIS,
          SEK_FIXED_SHORTTERM_SWESTR_OIS);

  // DKK
  private static final ImmutableIrIndexCurveConvention DKK_OIS =
      irCurve(
          "DKK DESTR",
          DKK_DESTR,
          DKK_FIXED_1Y_DESTR_OIS,
          DKK_FIXED_TERM_DESTR_OIS,
          DKK_FIXED_SHORTTERM_DESTR_OIS);

  // HUF
  private static final ImmutableIrIndexCurveConvention HUF_OIS =
      irCurve(
          "HUF BUBORON",
          HUF_BUBORON,
          HUF_FIXED_1Y_BUBORON_OIS,
          HUF_FIXED_TERM_BUBORON_OIS,
          HUF_FIXED_SHORTTERM_BUBORON_OIS);

  // RUB
  private static final ImmutableIrIndexCurveConvention RUB_OIS =
      irCurve(
          "RUB RUONIA",
          RUB_RUONIA,
          RUB_FIXED_1Y_RUONIA_OIS,
          RUB_FIXED_TERM_RUONIA_OIS,
          RUB_FIXED_SHORTTERM_RUONIA_OIS);

  private static final ImmutableIrIndexCurveConvention RUB_3M =
      irCurve(
          "RUB 3M",
          RUB_MOSPRIME_3M,
          RUB_FIXED_1Y_MOSPRIME_3M,
          FraConvention.of(RUB_MOSPRIME_3M),
          IborFixingDepositConvention.of(RUB_MOSPRIME_3M));

  // ILS

  private static final ImmutableIrIndexCurveConvention ILS_OIS =
      irCurve(
          "ILS SHIR",
          ILS_SHIR,
          ILS_FIXED_TERM_SHIR_OIS,
          ILS_FIXED_SHORTTERM_SHIR_OIS,
          ILS_FIXED_1Y_SHIR_OIS);

  private static final ImmutableIrIndexCurveConvention ILS_OIS_OFFSHORE =
      irCurve(
          "ILS SHIR Offshore",
          ILS_SHIR_OFFSHORE,
          ILS_FIXED_TERM_SHIR_OIS_OFFSHORE,
          ILS_FIXED_SHORTTERM_SHIR_OIS_OFFSHORE,
          ILS_FIXED_1Y_SHIR_OIS_OFFSHORE);

  // KRW

  private static final ImmutableIrIndexCurveConvention KRW_OIS =
      irCurve(
          "KRW KOFR",
          KRW_KOFR,
          KRW_FIXED_TERM_KOFR_OIS,
          KRW_FIXED_SHORTTERM_KOFR_OIS,
          KRW_FIXED_3M_KOFR_OIS);

  private static final ImmutableIrIndexCurveConvention KRW_OIS_OFFSHORE =
      irCurve(
          "KRW KOFR Offshore",
          KRW_KOFR_OFFSHORE,
          KRW_FIXED_TERM_KOFR_OIS_OFFSHORE,
          KRW_FIXED_SHORTTERM_KOFR_OIS_OFFSHORE,
          KRW_FIXED_3M_KOFR_OIS_OFFSHORE);

  public static final List<ConventionalCurveConvention> INDEX_CURVES =
      of(
          AED_1M,
          AED_3M,
          AED_6M,
          AED_12M,
          AUD_1M,
          AUD_3M,
          AUD_6M,
          AUD_OIS,
          BRL_CDI_OFFSHORE,
          BRL_OIS,
          CAD_1M,
          CAD_3M,
          CAD_OIS,
          CHF_1M,
          CHF_3M,
          CHF_6M,
          CHF_OIS,
          CLP_TNA_OFFSHORE,
          CLP_TNA,
          CNH_3M,
          CNY_1W_OFFSHORE,
          CNY_1W,
          CNY_3M,
          COP_OIBR_OFFSHORE,
          COP_OIBR,
          CZK_3M,
          CZK_6M,
          CZK_OIS,
          DKK_3M,
          DKK_6M,
          DKK_OIS,
          EUR_1M,
          EUR_3M,
          EUR_6M,
          EUR_12M,
          EUR_EONIA_OIS,
          EUR_ESTR_OIS,
          GBP_1M,
          GBP_3M,
          GBP_6M,
          GBP_12M,
          GBP_OIS,
          HKD_3M,
          HKD_OIS,
          HUF_3M,
          HUF_6M,
          HUF_OIS,
          IDR_OIS,
          ILS_3M,
          INR_OIS_OFFSHORE,
          INR_OIS,
          JPY_1M,
          JPY_3M,
          JPY_6M,
          JPY_OIS,
          KRW_13W_OFFSHORE,
          KRW_13W,
          MXN_13W,
          MXN_26W,
          MXN_4W_OFFSHORE,
          MXN_4W,
          MXN_OIS_OFFSHORE,
          MXN_OIS,
          MYR_3M_OFFSHORE,
          MYR_3M,
          MYR_OIS_OFFSHORE,
          MYR_OIS,
          NOK_3M,
          NOK_6M,
          NOK_OIS,
          NZD_3M,
          NZD_NZIONA_OIS,
          PLN_3M,
          PLN_6M,
          PLN_OIS,
          PLN_WIRON_OIS,
          RUB_3M,
          RUB_OIS,
          SAR_3M,
          SEK_3M,
          SEK_6M,
          SEK_OIS,
          SGD_1M,
          SGD_3M,
          SGD_6M,
          SGD_SORA_OIS,
          THB_6M_OFFSHORE,
          THB_6M,
          THB_THOR_OIS_OFFSHORE,
          THB_THOR_OIS,
          TRY_3M,
          TRY_TLREF_OIS,
          TWD_3M_OFFSHORE,
          TWD_3M,
          USD_1M,
          USD_3M,
          USD_6M,
          USD_FEDFUNDS_OIS,
          USD_SOFR_3M,
          USD_SOFR_6M,
          USD_SOFR_OIS,
          ZAR_3M,
          ZAR_OIS,
          ILS_OIS,
          ILS_OIS_OFFSHORE,
          KRW_OIS,
          KRW_OIS_OFFSHORE);

  public static List<ConventionalCurveConvention> OFFSHORE_INDEX_CURVES =
      offshoreIndexCurveConventions();

  private static List<ConventionalCurveConvention> offshoreIndexCurveConventions() {
    return INDEX_CURVES.stream()
        .filter(
            c -> {
              if (c instanceof IndexCurveConvention indexCurveConvention) {
                return OffshoreIndices.isOffshore(indexCurveConvention.getIndex());
              }
              return false;
            })
        .toList();
  }

  public static Optional<String> indexCurveNameByIndex(FloatingRateIndex index) {
    return INDEX_CURVES.stream()
        .filter(
            c -> {
              if (c instanceof IndexCurveConvention indexCurveConvention) {
                return indexCurveConvention.getIndex() == index;
              }
              return false;
            })
        .findFirst()
        .map(ConventionalCurveConvention::getName);
  }
}
