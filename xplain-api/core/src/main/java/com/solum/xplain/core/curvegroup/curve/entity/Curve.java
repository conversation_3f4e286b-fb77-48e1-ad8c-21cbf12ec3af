package com.solum.xplain.core.curvegroup.curve.entity;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.solum.xplain.core.common.CollectionUtils.nullSafeIsEqualCollection;
import static com.solum.xplain.core.curvegroup.curve.classifier.CurveType.INFLATION_INDEX;
import static com.solum.xplain.core.curvegroup.curve.entity.Curve.CURVE_COLLECTION;
import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup.SIMPLE_YMD_TIME_REGEX;
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static com.solum.xplain.core.error.Error.CALIBRATION_WARNING;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static com.solum.xplain.core.market.SemanticMarketDataKeyBuilder.semanticMarketDataKey;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.util.Comparator.comparing;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toUnmodifiableList;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;
import static org.slf4j.LoggerFactory.getLogger;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Ordering;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.collect.Messages;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.loader.LoaderUtils;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.CurveDefinition;
import com.opengamma.strata.market.curve.CurveName;
import com.opengamma.strata.market.curve.CurveNodeClashAction;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.market.curve.InterpolatedNodalCurveDefinition;
import com.opengamma.strata.market.curve.NodalCurveDefinition;
import com.opengamma.strata.market.curve.RatesCurveGroupEntry;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolator;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolator;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntry;
import com.solum.xplain.core.curvegroup.HasClearingHouse;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.fx.ImmutableXccyCurveConvention;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper;
import com.solum.xplain.core.curvemarket.node.NonOverlappingXccyNodesFilter;
import com.solum.xplain.core.curvemarket.node.ValidMarketTenorNodesFilter;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.utils.DayCountUtils;
import com.solum.xplain.extensions.index.OffshoreIndices;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.ToDoubleFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = CURVE_COLLECTION)
@FieldNameConstants
@Slf4j
public class Curve extends VersionedNamedEntity implements CurveProjectionIndex, CurveGroupEntry {
  public static final String CURVE_COLLECTION = "curve";
  private static final Logger LOG = getLogger(Curve.class);
  private static final String CURVE_NOT_FOUND = "Invalid curve name!";
  private static final String NO_NODES_ERROR_TEMPLATE = "Curve %s must have at least 2 nodes";
  private static final String NO_VALID_NODES_ERROR_TEMPLATE =
      "Curve %s must have at least 2 nodes with data for " + "the selected valuation date";
  private static final String CURVE_CALIBRATION_FAILED = "Curve %s calibration failed: %s";
  private static final String NODE_DROPPED = "Curve %s clashing node %s was dropped";

  public static final DayCount DEFAULT_DAY_COUNT = DayCounts.ACT_365F;
  private static final ValueType DEFAULT_X_VALUE_TYPE = ValueType.YEAR_FRACTION;
  private static final ValueType DEFAULT_Y_VALUE_TYPE = ValueType.ZERO_RATE;
  private static final ValueType INFLATION_X_VALUE_TYPE = ValueType.MONTHS;
  private static final ValueType INFLATION_Y_VALUE_TYPE = ValueType.PRICE_INDEX;

  private String curveGroupId;

  private CurveType curveType;

  private String interpolator;

  private String extrapolatorLeft;

  private String extrapolatorRight;

  private String yInterpolationMethod;

  private String minGap;

  private String clashAction;

  private List<CurveNode> nodes = new ArrayList<>();

  public static Curve newOf() {
    var c = new Curve();
    c.setEntityId(ObjectId.get().toString());
    c.setState(State.ACTIVE);
    c.setRecordDate(LocalDateTime.now());
    return c;
  }

  public CurveName curveName() {
    return CurveName.of(getName());
  }

  public List<CurveNode> orderedNodes(LocalDate valuationDate, ReferenceData referenceData) {
    var ordering = Ordering.from(curveNodeDateOrder(valuationDate, referenceData));
    return ordering.sortedCopy(emptyIfNull(nodes));
  }

  private Comparator<CurveNode> curveNodeDateOrder(LocalDate valDt, ReferenceData referenceData) {
    var dateOrder = this.parseDateOrder(valDt);
    var isOffshoreCurve = isOffshoreCurve();
    var clearingHouse = clearingHouse();
    return comparing(
        n ->
            DayCountUtils.yearFraction(
                valDt,
                n.date(isOffshoreCurve, clearingHouse, valDt, dateOrder, referenceData),
                DEFAULT_DAY_COUNT));
  }

  public Either<ErrorItem, RatesCurveGroupEntry> curveGroupEntry(
      Currency resolvedDiscountCurrency) {
    try {
      var builder = RatesCurveGroupEntry.builder().curveName(curveName());
      if (getIndices() != null) {
        builder.indices(LoaderUtils.findIndex(getIndices()));
      }

      if (resolvedDiscountCurrency != null) {
        builder.discountCurrencies(resolvedDiscountCurrency);
      }
      return right(builder.build());
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return left(new ErrorItem(PARSING_ERROR, ex.getMessage()));
    }
  }

  public Either<ErrorItem, CurveDefinition> curveDefinition(
      ToDoubleFunction<String> additionalSpread,
      LocalDate valuationDate,
      ValidNodesFilter nodesFilter,
      MarketData marketData,
      ReferenceData referenceData,
      Consumer<List<ErrorItem>> warningsConsumer,
      boolean forceIsdaInterpolators,
      boolean allowDropMissingMdNodes) {
    try {
      var orderedNodes = orderedNodes(valuationDate, referenceData);
      var nodeDateOrder = parseDateOrder(valuationDate);
      var marketTenorNodesFilter =
          ValidMarketTenorNodesFilter.ofValidMarketTenor(
              valuationDate, referenceData, nodeDateOrder, warningsConsumer);
      ValidNodesFilter clashingNodesFilter =
          NonOverlappingXccyNodesFilter.ofValidNodes(
              valuationDate, referenceData, nodeDateOrder, warningsConsumer);
      return validateNodesSize(orderedNodes, NO_NODES_ERROR_TEMPLATE)
          .map(ImmutableList::copyOf)
          .map(n -> filterNodes(n, nodesFilter))
          .map(n -> filterNodes(n, clashingNodesFilter))
          .map(n -> filterNodes(n, marketTenorNodesFilter))
          .map(n -> toStrataNodes(n, additionalSpread, valuationDate, referenceData))
          .map(n -> filterMissingMdNodes(n, marketData, allowDropMissingMdNodes))
          .flatMap(Eithers::sequenceRight)
          .flatMap(n -> validateNodesSize(n, NO_VALID_NODES_ERROR_TEMPLATE))
          .flatMap(
              n ->
                  Either.<ErrorItem, NodalCurveDefinition>right(
                          toStrataDefinition(n, forceIsdaInterpolators))
                      .map(def -> def.filtered(valuationDate, referenceData))
                      .map(def -> logDroppedNodes(warningsConsumer, def, n)));
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return left(
          new ErrorItem(
              CALIBRATION_ERROR,
              String.format(CURVE_CALIBRATION_FAILED, getName(), ex.getMessage())));
    }
  }

  private NodalCurveDefinition logDroppedNodes(
      Consumer<List<ErrorItem>> warningsConsumer,
      NodalCurveDefinition def,
      Iterable<com.opengamma.strata.market.curve.CurveNode> n) {
    if (CollectionUtils.size(n) > def.getNodes().size()) {
      warningsConsumer.accept(
          ImmutableList.copyOf(n).stream()
              .map(com.opengamma.strata.market.curve.CurveNode::getLabel)
              .filter(i -> def.getNodes().stream().noneMatch(dn -> dn.getLabel().equals(i)))
              .map(i -> CALIBRATION_WARNING.entity(String.format(NODE_DROPPED, getName(), i)))
              .toList());
    }
    return def;
  }

  private <T> Either<ErrorItem, Iterable<T>> validateNodesSize(
      Iterable<T> nodes, String errorTemplate) {
    return Eithers.cond(
        CollectionUtils.size(nodes) > 1,
        new ErrorItem(CALIBRATION_ERROR, String.format(errorTemplate, getName())),
        nodes);
  }

  private List<CurveNode> filterNodes(List<CurveNode> sortedNodes, ValidNodesFilter nodesFilter) {
    var clearingHouse = clearingHouse();
    return sortedNodes.stream()
        .map(v -> NodeInstrumentWrapper.of(v, instrument(v, clearingHouse)))
        .collect(Collectors.collectingAndThen(toUnmodifiableList(), nodesFilter::filterNodes));
  }

  /**
   * Filter out nodes where market data is missing, only applicable if allowDropMissingMdNodes is
   * true, which can be useful for scenarios where the md is not available, but we still want to
   * allow for curves to calibrate. One exmaple of this is when we want to ignore null values in
   * market data exception management, but still be able to use the curves with preliminary/overlay
   * market data. TODO: SXSD-9178 - add reference to DROP_MISSING_MD_NODES resolution
   *
   * @param nodes the nodes to filter
   * @param marketData the market data
   * @param allowDropMissingMdNodes whether to allow dropping nodes with missing market data
   * @return the filtered nodes
   */
  private List<Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>> filterMissingMdNodes(
      List<Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>> nodes,
      MarketData marketData,
      boolean allowDropMissingMdNodes) {

    return nodes.stream()
        .filter(
            nodeEither -> {
              if (nodeEither.isRight() && allowDropMissingMdNodes) {
                com.opengamma.strata.market.curve.CurveNode node = nodeEither.right().get();
                var requirements = node.requirements();
                for (var requirement : requirements) {
                  // TODO: SXSD-9176 - populate overlay / preliminary market data with Double.NaN
                  //                   where you want to drop the node value
                  // if value is NaN, drop the node, since NaN values are used to indicate missing
                  // data
                  // that we are happy to ignore
                  if (marketData.getValue(requirement).equals(Double.NaN)) {
                    return false;
                  }
                }
              }

              return true;
            })
        .toList();
  }

  private List<Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>> toStrataNodes(
      List<CurveNode> nodes,
      ToDoubleFunction<String> additionalSpread,
      LocalDate valuationDate,
      ReferenceData referenceData) {
    var order = parseDateOrder(valuationDate);
    var clearingHouse = clearingHouse();
    return nodes.stream()
        .map(
            n ->
                n.node(
                    additionalSpread.applyAsDouble(semanticMarketDataKey(n, clearingHouse)),
                    isOffshoreCurve(),
                    clearingHouse,
                    order,
                    valuationDate,
                    referenceData))
        .toList();
  }

  public boolean isOffshoreCurve() {
    return Optional.ofNullable(getIndices())
        .map(FloatingRateIndex::of)
        .map(OffshoreIndices::isOffshore)
        .orElse(false);
  }

  public boolean isOvernightTermCurve() {
    return Optional.ofNullable(getIndices())
        .map(FloatingRateIndex::of)
        .map(i -> i instanceof OvernightTermIndex)
        .orElse(false);
  }

  private InterpolatedNodalCurveDefinition toStrataDefinition(
      Iterable<com.opengamma.strata.market.curve.CurveNode> n, boolean forceIsdaInterpolators) {
    return InterpolatedNodalCurveDefinition.builder()
        .name(CurveName.of(getName()))
        .dayCount(dayCount())
        .xValueType(xValueType())
        .yValueType(yValueType())
        .interpolator(
            forceIsdaInterpolators
                ? CurveInterpolators.PRODUCT_LINEAR
                : CurveInterpolator.of(interpolator))
        .extrapolatorLeft(
            forceIsdaInterpolators
                ? CurveExtrapolators.FLAT
                : CurveExtrapolator.of(extrapolatorLeft))
        .extrapolatorRight(
            forceIsdaInterpolators
                ? CurveExtrapolators.PRODUCT_LINEAR
                : CurveExtrapolator.of(extrapolatorRight))
        .nodes(ImmutableList.copyOf(n))
        .build();
  }

  public boolean isProjectionCurve() {
    return getIndices() != null;
  }

  public Optional<Currency> projectionCurrency() {
    return index().map(FloatingRateIndex::getCurrency);
  }

  public Optional<CurrencyPair> xccyPair() {
    return ConventionalCurveConfigurations.lookupByName(getName(), getCurveType())
        .filter(ImmutableXccyCurveConvention.class::isInstance)
        .map(ImmutableXccyCurveConvention.class::cast)
        .map(c -> CurrencyPair.of(c.getBaseCurrency(), c.getCounterCurrency()));
  }

  public Stream<Currency> xccyCurrencies() {
    return xccyPair().map(x -> Set.of(x.getBase(), x.getCounter())).orElse(Set.of()).stream();
  }

  public List<FloatingRateIndex> requiredIndices() {
    var isOffshoreCurve = isOffshoreCurve();
    return ofNullable(nodes)
        .map(ns -> ns.stream().flatMap(n -> n.resolveConventionIndexes(isOffshoreCurve)).toList())
        .orElse(List.of());
  }

  public boolean isOIS(Currency baseCurrency) {
    if (getIndices() != null
        && OvernightIndex.extendedEnum().lookupAll().containsKey(getIndices())) {
      return OvernightIndex.of(getIndices()).getCurrency().equals(baseCurrency);
    } else {
      return false;
    }
  }

  public Optional<Currency> oisCurveCurrency() {
    return ofNullable(getIndices())
        .filter(i -> OvernightIndex.extendedEnum().lookupAll().containsKey(i))
        .map(i -> OvernightIndex.of(i).getCurrency());
  }

  public ValueType xValueType() {
    return curveType == INFLATION_INDEX ? INFLATION_X_VALUE_TYPE : DEFAULT_X_VALUE_TYPE;
  }

  public ValueType yValueType() {
    if (curveType == INFLATION_INDEX) {
      return INFLATION_Y_VALUE_TYPE;
    }
    var valueType = ValueType.of(yInterpolationMethod);
    if (valueType.equals(ValueType.DISCOUNT_FACTOR)) {
      return DEFAULT_Y_VALUE_TYPE;
    }
    return valueType;
  }

  public DayCount dayCount() {
    return DEFAULT_DAY_COUNT;
  }

  public CurveNodeDateOrder parseDateOrder(LocalDate valuationDate) {
    CurveNodeClashAction clashActionObj =
        isNullOrEmpty(clashAction)
            ? CurveNodeClashAction.DROP_OTHER
            : CurveNodeClashAction.of(clashAction);
    if (isNullOrEmpty(minGap)) {
      return CurveNodeDateOrder.of(7, clashActionObj);
    }
    var matcher = SIMPLE_YMD_TIME_REGEX.matcher(minGap.toUpperCase(Locale.ENGLISH));
    if (!matcher.matches()) {
      throw new IllegalArgumentException(
          Messages.format("Invalid days format for minimum gap, should be 2D or P2D: {}", minGap));
    }
    var minGapPeriod = Period.parse("P" + matcher.group(1));
    long days = ChronoUnit.DAYS.between(valuationDate, valuationDate.plus(minGapPeriod));
    return CurveNodeDateOrder.of((int) days, clashActionObj);
  }

  private InstrumentDefinition instrument(CurveNode node, ClearingHouse clearingHouse) {
    var curve =
        ConventionalCurveConfigurations.lookupByName(getName(), getCurveType())
            .orElseThrow(() -> new IllegalArgumentException(CURVE_NOT_FOUND));

    if (curve instanceof ImmutableXccyCurveConvention xccyConvention) {
      var fxPair =
          CurrencyPair.of(xccyConvention.getBaseCurrency(), xccyConvention.getCounterCurrency());
      return node.instrumentFx(getName(), fxPair.toString());
    } else {
      return node.instrument(getName(), curve.getCurrency().getCode(), clearingHouse);
    }
  }

  public ClearingHouse clearingHouse() {
    return ConventionalCurveConfigurations.lookupByName(getName(), getCurveType())
        .filter(HasClearingHouse.class::isInstance)
        .map(HasClearingHouse.class::cast)
        .map(HasClearingHouse::getClearingHouse)
        .orElse(ClearingHouse.NONE);
  }

  @Override
  public List<InstrumentDefinition> allInstruments() {
    var clearingHouse = clearingHouse();
    return ofNullable(nodes).stream()
        .flatMap(List::stream)
        .map(n -> instrument(n, clearingHouse))
        .toList();
  }

  @Override
  public boolean valueEquals(Object object) {
    Curve entity = (Curve) object;
    return super.valueEquals(entity)
        && Objects.equals(this.clashAction, entity.clashAction)
        && Objects.equals(this.curveGroupId, entity.curveGroupId)
        && Objects.equals(this.curveType, entity.curveType)
        && Objects.equals(this.extrapolatorLeft, entity.extrapolatorLeft)
        && Objects.equals(this.yInterpolationMethod, entity.yInterpolationMethod)
        && Objects.equals(this.extrapolatorRight, entity.extrapolatorRight)
        && Objects.equals(this.interpolator, entity.interpolator)
        && Objects.equals(this.minGap, entity.minGap)
        && nullSafeIsEqualCollection(this.nodes, entity.nodes);
  }
}
