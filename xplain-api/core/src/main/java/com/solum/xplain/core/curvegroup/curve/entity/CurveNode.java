package com.solum.xplain.core.curvegroup.curve.entity;

import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_INFLATION_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_IBOR_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IMM_FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.OVERNIGHT_IBOR_BASIS_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_OIS_FIXING_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_FIXED_OVERNIGHT_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_OIS_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_OIS_OIS_SWAP_NODE;
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofFxCurve;
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofIrCurve;
import static com.solum.xplain.core.market.SemanticMarketDataKeyBuilder.semanticMarketDataKey;
import static org.slf4j.LoggerFactory.getLogger;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.product.deposit.type.IborFixingDepositConvention;
import com.opengamma.strata.product.fra.type.FraConvention;
import com.opengamma.strata.product.index.type.IborFutureContractSpec;
import com.opengamma.strata.product.swap.type.FixedIborSwapConvention;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention;
import com.opengamma.strata.product.swap.type.IborIborSwapConvention;
import com.opengamma.strata.product.swap.type.OvernightIborSwapConvention;
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConvention;
import com.opengamma.strata.product.swap.type.XCcyOvernightOvernightSwapConvention;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.curvegroup.curve.extension.IborFutureCurveNode;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.utils.FrequencyUtils;
import com.solum.xplain.core.utils.TenorUtils;
import com.solum.xplain.extensions.immfra.ImmFraConvention;
import com.solum.xplain.extensions.index.OffshoreIndices;
import com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConvention;
import com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConvention;
import com.solum.xplain.extensions.xccyfixedois.XCcyFixedOvernightSwapConvention;
import com.solum.xplain.extensions.xccyiborois.XCcyIborOvernightSwapConvention;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.function.Function;
import java.util.stream.Stream;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

@Data
@FieldNameConstants
public class CurveNode implements CurveNodeInstrument {

  private static final Logger LOG = getLogger(CurveNode.class);
  private static final String FX_SWAP_MD_NAME_PATTERN = "%S FX SWAP %S";
  private static final String FIXED_IBOR_SWAP_MD_NAME_PATTERN = "%S %S %S VS %S SWAP %S";
  private static final String FIXED_IBOR_OFFSHORE_SWAP_MD_NAME_PATTERN =
      "%S %S OFFSHORE %S VS %S SWAP %S";
  private static final String FIXED_INFLATION_SWAP_MD_NAME_PATTERN = "%S SWAP %S";
  private static final String FIXED_OVERNIGHT_SWAP_MD_NAME_PATTERN = "%S OIS SWAP %S";
  private static final String FIXED_OVERNIGHT_OFFSHORE_SWAP_MD_NAME_PATTERN =
      "%S OIS OFFSHORE SWAP %S";
  private static final String FRA_MD_NAME_PATTERN = "%S FRA %S";
  private static final String IMM_FRA_MD_NAME_PATTERN = "%S IMM FRA %S";
  private static final String TERM_OIS_FIXING_DEPOSIT_MD_NAME_PATTERN = "%S %S TERM OIS DEPOSIT";
  private static final String IBOR_FIXING_DEPOSIT_MD_NAME_PATTERN = "%S %S DEPOSIT";
  private static final String IBOR_FIXING_OFFSHORE_DEPOSIT_MD_NAME_PATTERN =
      "%S %S OFFSHORE DEPOSIT";
  private static final String IBOR_FUTURE_MD_NAME_PATTERN = "%S %S FUTURE %S";
  private static final String IBOR_IBOR_SWAP_MD_NAME_PATTERN = "%S %S VS %S %S";
  private static final String OVERNIGHT_IBOR_BASIS_SWAP_MD_NAME_PATTERN = "%S VS %S %S %S";
  private static final String XCCY_IBOR_IBOR_SWAP_MD_NAME_PATTERN = "%S %S VS %S %S %S";
  private static final String XCCY_OIS_OIS_SWAP_MD_NAME_PATTERN = "%S VS %S %S";
  private static final String XCCY_IBOR_OIS_SWAP_MD_NAME_PATTERN = "%S VS %S %S";
  private static final String XCCY_FIXED_OIS_SWAP_MD_NAME_PATTERN = "%S FX SWAP %S";

  private String type;
  private String convention;
  private String period;
  private String fraSettlement;
  private String serialFuture;

  public static CurveNode newOf() {
    return new CurveNode();
  }

  public LocalDate date(
      boolean isOffshoreCurve,
      ClearingHouse clearingHouse,
      LocalDate valuationDate,
      CurveNodeDateOrder futureNodeDateOrder,
      ReferenceData referenceData) {
    return node(
            0d, isOffshoreCurve, clearingHouse, futureNodeDateOrder, valuationDate, referenceData)
        .map(node -> date(node, valuationDate, referenceData))
        .getOrElse(valuationDate);
  }

  private LocalDate date(
      com.opengamma.strata.market.curve.CurveNode node,
      LocalDate valuationDate,
      ReferenceData referenceData) {
    return Checked.now(() -> node.date(valuationDate, referenceData))
        .fold(
            ex -> {
              LOG.debug(ex.getMessage(), ex);
              return valuationDate;
            },
            Function.identity());
  }

  public LocalDate iborFutureFutureEndDate(
      boolean isOffshoreCurve,
      ClearingHouse clearingHouse,
      LocalDate valuationDate,
      CurveNodeDateOrder futureNodeDateOrder,
      ReferenceData referenceData) {
    return iborFutureNode(
            0d, isOffshoreCurve, clearingHouse, futureNodeDateOrder, valuationDate, referenceData)
        .map(node -> iborFutureFutureEndDate(node, valuationDate, referenceData))
        .getOrElse(valuationDate);
  }

  private LocalDate iborFutureFutureEndDate(
      IborFutureCurveNode node, LocalDate valuationDate, ReferenceData referenceData) {
    return Checked.now(() -> node.futureEndInfoDate(valuationDate, referenceData))
        .fold(
            ex -> {
              LOG.debug(ex.getMessage(), ex);
              return valuationDate;
            },
            Function.identity());
  }

  public Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> node(
      double additionalSpread,
      boolean isOffshoreCurve,
      ClearingHouse clearingHouse,
      CurveNodeDateOrder futureNodeDateOrder,
      LocalDate valuationDate,
      ReferenceData referenceData) {
    return CurveNodeStrataDecorator.newOf(this, isOffshoreCurve, clearingHouse, referenceData)
        .node(additionalSpread, futureNodeDateOrder, valuationDate);
  }

  public Either<ErrorItem, IborFutureCurveNode> iborFutureNode(
      double additionalSpread,
      boolean isOffshoreCurve,
      ClearingHouse clearingHouse,
      CurveNodeDateOrder futureNodeDateOrder,
      LocalDate valuationDate,
      ReferenceData referenceData) {
    return node(
            additionalSpread,
            isOffshoreCurve,
            clearingHouse,
            futureNodeDateOrder,
            valuationDate,
            referenceData)
        .flatMap(
            node -> {
              if (node instanceof IborFutureCurveNode iborFutureNode) {
                return Either.right(iborFutureNode);
              }
              return Either.left(
                  new ErrorItem(
                      Error.UNEXPECTED_ERROR,
                      String.format(
                          "Expected node of type %s but got %s",
                          IborFutureCurveNode.class.getSimpleName(),
                          node.getClass().getSimpleName())));
            });
  }

  public Stream<FloatingRateIndex> resolveConventionIndexes(boolean isOffshoreCurve) {
    if (FIXED_OVERNIGHT_SWAP_NODE.equalsIgnoreCase(type)) {
      return Stream.of(
          FixedOvernightSwapConvention.of(this.convention).getFloatingLeg().getIndex());
    } else if (XCCY_IBOR_IBOR_SWAP_NODE.equalsIgnoreCase(type)) {
      var xCcyIborIborSwapConvention = XCcyIborIborSwapConvention.of(this.convention);
      return Stream.of(
          xCcyIborIborSwapConvention.getSpreadLeg().getIndex(),
          xCcyIborIborSwapConvention.getFlatLeg().getIndex());
    } else if (XCCY_OIS_OIS_SWAP_NODE.equalsIgnoreCase(type)) {
      var xCcyOisOisSwapConvention = XCcyOvernightOvernightSwapConvention.of(this.convention);
      var spreadLegIndex =
          adjustForOffshoreOvernight(
              xCcyOisOisSwapConvention.getSpreadLeg().getIndex(), isOffshoreCurve);
      var flatLegIndex =
          adjustForOffshoreOvernight(
              xCcyOisOisSwapConvention.getFlatLeg().getIndex(), isOffshoreCurve);
      return Stream.of(spreadLegIndex, flatLegIndex);
    } else if (XCCY_IBOR_OIS_SWAP_NODE.equalsIgnoreCase(type)) {
      var xCcyIborOisSwapConvention = XCcyIborOvernightSwapConvention.of(this.convention);
      return Stream.of(
          xCcyIborOisSwapConvention.getIborLeg().getIndex(),
          xCcyIborOisSwapConvention.getOvernightLeg().getIndex());
    } else if (XCCY_FIXED_OVERNIGHT_SWAP_NODE.equalsIgnoreCase(type)) {
      var xCcyFixedOisSwapConvention = XCcyFixedOvernightSwapConvention.of(this.convention);
      return Stream.of(xCcyFixedOisSwapConvention.getFloatingLeg().getIndex());
    } else if (FIXED_IBOR_SWAP_NODE.equalsIgnoreCase(type)) {
      var fixedIborSwapConvention = FixedIborSwapConvention.of(this.convention);
      return Stream.of(fixedIborSwapConvention.getFloatingLeg().getIndex());
    } else if (IBOR_FIXING_DEPOSIT_NODE.equalsIgnoreCase(type)) {
      var iborFixingDepositConvention = IborFixingDepositConvention.of(this.convention);
      var index = iborFixingDepositConvention.getIndex();
      if (isOffshoreCurve) {
        return Stream.of(OffshoreIndices.lookupOffshoreIbor(index).orElse(index));
      }
      return Stream.of(index);
    } else if (TERM_OIS_FIXING_DEPOSIT_NODE.equalsIgnoreCase(type)) {
      var termOisConvention = TermOisFixingDepositConvention.of(this.convention);
      return Stream.of(termOisConvention.getIndex());
    } else if (OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE.equalsIgnoreCase(type)) {
      var otosConvention = OvernightTermOvernightSwapConvention.of(this.convention);
      return Stream.of(otosConvention.getTermOvernightLeg().getIndex());
    } else if (FRA_NODE.equalsIgnoreCase(type)) {
      var fraConvention = FraConvention.of(this.convention);
      return Stream.of(fraConvention.getIndex());
    } else if (IMM_FRA_NODE.equalsIgnoreCase(type)) {
      var fraConvention = ImmFraConvention.of(this.convention);
      return Stream.of(fraConvention.getIndex());
    } else if (IBOR_FUTURE_NODE.equalsIgnoreCase(type)) {
      var iborFutureSpec = IborFutureContractSpec.of(this.convention);
      return Stream.of(iborFutureSpec.getIndex());
    } else if (IBOR_IBOR_SWAP_NODE.equalsIgnoreCase(type)) {
      var iborSwapConvention = IborIborSwapConvention.of(this.convention);
      return Stream.of(
          iborSwapConvention.getSpreadLeg().getIndex(), iborSwapConvention.getFlatLeg().getIndex());
    } else if (OVERNIGHT_IBOR_BASIS_SWAP_NODE.equalsIgnoreCase(type)) {
      var overnightIborSwapConvention = OvernightIborSwapConvention.of(this.convention);
      return Stream.of(
          overnightIborSwapConvention.getOvernightLeg().getIndex(),
          overnightIborSwapConvention.getIborLeg().getIndex());
    } else if (FIXED_INFLATION_SWAP_NODE.equalsIgnoreCase(type)) {
      var fixedInflationSwapConvention = FixedInflationSwapConvention.of(this.convention);
      return Stream.of(fixedInflationSwapConvention.getFloatingLeg().getIndex());
    } else {
      return Stream.of();
    }
  }

  private FloatingRateIndex adjustForOffshoreOvernight(
      OvernightIndex index, boolean isOffshoreCurve) {
    return isOffshoreCurve ? OffshoreIndices.lookupOffshoreOvernight(index).orElse(index) : index;
  }

  public InstrumentDefinition instrument(String curve, String ccy, ClearingHouse clearingHouse) {
    var instrumentType = CoreInstrumentType.ofLabel(type);
    return ofIrCurve(
        ccy,
        curve,
        instrumentType,
        getTenor(),
        getInstrument(),
        semanticMarketDataKey(this, clearingHouse),
        getMdkName(type, curve));
  }

  public InstrumentDefinition instrumentFx(String curve, String ccyPair) {
    var marketTenor =
        TenorUtils.parseMarketTenor(period)
            .toOptional()
            .orElseThrow(() -> new IllegalArgumentException("Invalid node period"));
    var normalizedPeriod = marketTenor.getTenor().toString();
    return ofFxCurve(
        ccyPair,
        curve,
        CoreInstrumentType.ofLabel(type),
        normalizedPeriod,
        getInstrument(),
        semanticMarketDataKey(this, ClearingHouse.NONE), // No clearing house for FX
        String.format(FX_SWAP_MD_NAME_PATTERN, getConvention(), getInstrument()));
  }

  private String getMdkName(String type, String curve) {
    boolean isOffshore = curve.contains("Offshore");
    return switch (type) {
      case FIXED_IBOR_SWAP_NODE -> {
        var fisConvention = FixedIborSwapConvention.of(getConvention());
        yield String.format(
            isOffshore ? FIXED_IBOR_OFFSHORE_SWAP_MD_NAME_PATTERN : FIXED_IBOR_SWAP_MD_NAME_PATTERN,
            fisConvention.getFixedLeg().getCurrency(),
            FrequencyUtils.toStringNoPrefix(fisConvention.getFixedLeg().getAccrualFrequency()),
            fisConvention.getFixedLeg().getDayCount(),
            fisConvention.getFloatingLeg().getIndex().getTenor(),
            getInstrument());
      }
      case FIXED_INFLATION_SWAP_NODE ->
          String.format(
              FIXED_INFLATION_SWAP_MD_NAME_PATTERN,
              FixedInflationSwapConvention.of(getConvention()).getFloatingLeg().getIndex(),
              getInstrument());
      case FIXED_OVERNIGHT_SWAP_NODE ->
          String.format(
              isOffshore
                  ? FIXED_OVERNIGHT_OFFSHORE_SWAP_MD_NAME_PATTERN
                  : FIXED_OVERNIGHT_SWAP_MD_NAME_PATTERN,
              FixedOvernightSwapConvention.of(getConvention()).getFloatingLeg().getIndex(),
              getInstrument());
      case FRA_NODE ->
          String.format(
              FRA_MD_NAME_PATTERN,
              FraConvention.of(getConvention()).getIndex().getCurrency(),
              getInstrument());
      case IMM_FRA_NODE ->
          String.format(
              IMM_FRA_MD_NAME_PATTERN,
              ImmFraConvention.of(getConvention()).getIndex().getCurrency(),
              getInstrument());
      case IBOR_FIXING_DEPOSIT_NODE -> {
        var ifdConvention = IborFixingDepositConvention.of(getConvention());
        yield String.format(
            isOffshore
                ? IBOR_FIXING_OFFSHORE_DEPOSIT_MD_NAME_PATTERN
                : IBOR_FIXING_DEPOSIT_MD_NAME_PATTERN,
            ifdConvention.getIndex().getCurrency(),
            ifdConvention.getIndex().getTenor());
      }
      case IBOR_FUTURE_NODE -> {
        var ifContract = IborFutureContractSpec.of(getConvention());
        yield String.format(
            IBOR_FUTURE_MD_NAME_PATTERN,
            ifContract.getIndex().getCurrency(),
            ifContract.getIndex().getTenor(),
            StringUtils.substringAfter(getSerialFuture(), "+"));
      }
      case IBOR_IBOR_SWAP_NODE -> {
        var iisConvention = IborIborSwapConvention.of(getConvention());
        yield String.format(
            IBOR_IBOR_SWAP_MD_NAME_PATTERN,
            iisConvention.getSpreadLeg().getCurrency(),
            iisConvention.getSpreadLeg().getIndex().getTenor(),
            iisConvention.getFlatLeg().getIndex().getTenor(),
            getInstrument());
      }
      case OVERNIGHT_IBOR_BASIS_SWAP_NODE -> {
        var oisConvention = OvernightIborSwapConvention.of(getConvention());
        yield String.format(
            OVERNIGHT_IBOR_BASIS_SWAP_MD_NAME_PATTERN,
            oisConvention.getOvernightLeg().getIndex(),
            oisConvention.getOvernightLeg().getCurrency(),
            oisConvention.getIborLeg().getIndex().getTenor(),
            getInstrument());
      }
      case XCCY_IBOR_IBOR_SWAP_NODE -> {
        var xiisConvention = XCcyIborIborSwapConvention.of(getConvention());
        yield String.format(
            XCCY_IBOR_IBOR_SWAP_MD_NAME_PATTERN,
            xiisConvention.getSpreadLeg().getCurrency(),
            xiisConvention.getSpreadLeg().getIndex().getTenor(),
            xiisConvention.getFlatLeg().getCurrency(),
            xiisConvention.getFlatLeg().getIndex().getTenor(),
            getInstrument());
      }
      case XCCY_OIS_OIS_SWAP_NODE -> {
        var xiisOisOisConvention = XCcyOvernightOvernightSwapConvention.of(getConvention());
        yield String.format(
            XCCY_OIS_OIS_SWAP_MD_NAME_PATTERN,
            xiisOisOisConvention.getSpreadLeg().getIndex(),
            xiisOisOisConvention.getFlatLeg().getIndex(),
            getInstrument());
      }
      case XCCY_IBOR_OIS_SWAP_NODE -> {
        var xiisIborOisConvention = XCcyIborOvernightSwapConvention.of(getConvention());
        yield String.format(
            XCCY_IBOR_OIS_SWAP_MD_NAME_PATTERN,
            xiisIborOisConvention.getIborLeg().getIndex(),
            xiisIborOisConvention.getOvernightLeg().getIndex(),
            getInstrument());
      }
      case XCCY_FIXED_OVERNIGHT_SWAP_NODE ->
          String.format(
              XCCY_FIXED_OIS_SWAP_MD_NAME_PATTERN,
              XCcyFixedOvernightSwapConvention.of(getConvention()),
              getInstrument());
      case TERM_DEPOSIT_NODE -> convention.replace("-", " ");
      case TERM_OIS_FIXING_DEPOSIT_NODE -> {
        var toisConvention = TermOisFixingDepositConvention.of(getConvention());
        yield String.format(
            TERM_OIS_FIXING_DEPOSIT_MD_NAME_PATTERN,
            toisConvention.getIndex().getCurrency(),
            toisConvention.getIndex().getTenor());
      }
      case OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE -> {
        var otosConvention = OvernightTermOvernightSwapConvention.of(getConvention());
        yield String.format(
            OVERNIGHT_IBOR_BASIS_SWAP_MD_NAME_PATTERN,
            otosConvention.getOvernightLeg().getIndex(),
            otosConvention.getTermOvernightLeg().getCurrency(),
            otosConvention.getOvernightLeg().getIndex().getTenor(),
            getInstrument());
      }
      default -> throw new IllegalArgumentException("Unsupported instrument type: " + type);
    };
  }
}
