package com.solum.xplain.core.curvegroup.curve.entity;

import static com.opengamma.strata.basics.date.Tenor.TENOR_1D;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_INFLATION_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_IBOR_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IMM_FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.OVERNIGHT_IBOR_BASIS_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_OIS_FIXING_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_FIXED_OVERNIGHT_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_OIS_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_OIS_OIS_SWAP_NODE;
import static com.solum.xplain.core.curvegroup.curve.entity.BrlDateSequence.QUARTERLY_BRL_IMM;
import static com.solum.xplain.core.market.SemanticMarketDataKeyBuilder.semanticMarketDataKey;
import static com.solum.xplain.core.utils.TenorUtils.getOisFutureAnnualPeriodMatcher;
import static com.solum.xplain.core.utils.TenorUtils.getOisFutureQuarterPeriodMatcher;
import static com.solum.xplain.core.utils.TenorUtils.getOisFutureSemiAnnualPeriodMatcher;
import static com.solum.xplain.core.utils.TenorUtils.parseOisFutureTenorSeqNo;
import static io.atlassian.fugue.Either.right;
import static org.slf4j.LoggerFactory.getLogger;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.MarketTenor;
import com.opengamma.strata.basics.date.SequenceDate;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.market.curve.CurveNodeDate;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.market.curve.node.FixedIborSwapCurveNode;
import com.opengamma.strata.market.curve.node.FixedInflationSwapCurveNode;
import com.opengamma.strata.market.curve.node.FixedOvernightSwapCurveNode;
import com.opengamma.strata.market.curve.node.FraCurveNode;
import com.opengamma.strata.market.curve.node.IborFixingDepositCurveNode;
import com.opengamma.strata.market.curve.node.IborIborSwapCurveNode;
import com.opengamma.strata.market.curve.node.OvernightIborSwapCurveNode;
import com.opengamma.strata.market.curve.node.TermDepositCurveNode;
import com.opengamma.strata.market.curve.node.XCcyIborIborSwapCurveNode;
import com.opengamma.strata.market.curve.node.XCcyOvernightOvernightSwapCurveNode;
import com.opengamma.strata.market.observable.QuoteId;
import com.opengamma.strata.product.deposit.type.IborFixingDepositConvention;
import com.opengamma.strata.product.deposit.type.IborFixingDepositTemplate;
import com.opengamma.strata.product.deposit.type.TermDepositConvention;
import com.opengamma.strata.product.deposit.type.TermDepositTemplate;
import com.opengamma.strata.product.fra.type.FraConvention;
import com.opengamma.strata.product.fra.type.FraTemplate;
import com.opengamma.strata.product.index.type.IborFutureContractSpec;
import com.opengamma.strata.product.index.type.IborFutureTemplate;
import com.opengamma.strata.product.swap.type.FixedIborSwapConvention;
import com.opengamma.strata.product.swap.type.FixedIborSwapTemplate;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.FixedInflationSwapTemplate;
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention;
import com.opengamma.strata.product.swap.type.FixedOvernightSwapTemplate;
import com.opengamma.strata.product.swap.type.IborIborSwapConvention;
import com.opengamma.strata.product.swap.type.IborIborSwapTemplate;
import com.opengamma.strata.product.swap.type.OvernightIborSwapConvention;
import com.opengamma.strata.product.swap.type.OvernightIborSwapTemplate;
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConvention;
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapTemplate;
import com.opengamma.strata.product.swap.type.XCcyOvernightOvernightSwapConvention;
import com.opengamma.strata.product.swap.type.XCcyOvernightOvernightSwapTemplate;
import com.solum.xplain.core.classifiers.CurveNodeTypes;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.curvegroup.curve.extension.FxSwapCurveNode;
import com.solum.xplain.core.curvegroup.curve.extension.IborFutureCurveNode;
import com.solum.xplain.core.curvegroup.curve.extension.MarketTenorFxSwapTemplate;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.mapping.MarketDataUtils;
import com.solum.xplain.core.utils.PeriodUtils;
import com.solum.xplain.extensions.immfra.ImmFraConvention;
import com.solum.xplain.extensions.immfra.ImmFraCurveNode;
import com.solum.xplain.extensions.immfra.ImmFraTemplate;
import com.solum.xplain.extensions.index.OffshoreIndices;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConvention;
import com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapCurveNode;
import com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapTradeTemplate;
import com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConvention;
import com.solum.xplain.extensions.termdeposit.TermOisFixingDepositCurveNode;
import com.solum.xplain.extensions.termdeposit.TermOisFixingDepositNodeTemplate;
import com.solum.xplain.extensions.xccyfixedois.XCcyFixedOvernightSwapConvention;
import com.solum.xplain.extensions.xccyfixedois.XCcyFixedOvernightSwapCurveNode;
import com.solum.xplain.extensions.xccyfixedois.XCcyFixedOvernightSwapTemplate;
import com.solum.xplain.extensions.xccyiborois.XCcyIborOvernightSwapConvention;
import com.solum.xplain.extensions.xccyiborois.XCcyIborOvernightSwapCurveNode;
import com.solum.xplain.extensions.xccyiborois.XCcyIborOvernightSwapTemplate;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.time.Month;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.slf4j.Logger;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(staticName = "newOf")
public class CurveNodeStrataDecorator {

  public static final Pattern OIS_FUT_TIME_REGEX =
      Pattern.compile("P?((\\d+Q)?(\\d+S)?(\\d+Y)?(\\d+M)?)|1D");
  private static final Pattern FRA_TIME_REGEX = Pattern.compile("P?(\\d+)M? ?X ?P?(\\d+)M?");
  private static final Pattern FUT_TIME_REGEX =
      Pattern.compile("P?((?:\\d+D)?(?:\\d+W)?(?:\\d+M)?) ?[+] ?(\\d+)");

  private static final Logger LOG = getLogger(CurveNodeStrataDecorator.class);

  private final CurveNode curveNode;
  private final boolean isOffshore;
  private final ClearingHouse clearingHouse;
  private final ReferenceData referenceData;

  public Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> node(
      double additionalSpread, CurveNodeDateOrder futureNodeDateOrder, LocalDate valuationDate) {
    var type = getType();
    try {
      if (FIXED_IBOR_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveFixedIborCurveNode(additionalSpread);
      }
      if (IBOR_FIXING_DEPOSIT_NODE.equalsIgnoreCase(type)) {
        return curveIborFixingDepositCurveNode(additionalSpread);
      }
      if (TERM_OIS_FIXING_DEPOSIT_NODE.equalsIgnoreCase(type)) {
        return curveTermOisFixingDepositCurveNode(additionalSpread);
      }
      if (OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveOvernightTermOvernightCurveNode(additionalSpread);
      }
      if (FRA_NODE.equalsIgnoreCase(type)) {
        return curveFraCurveNode(additionalSpread);
      }
      if (IMM_FRA_NODE.equalsIgnoreCase(type)) {
        return curveImmFraCurveNode(additionalSpread);
      }
      if (IBOR_FUTURE_NODE.equalsIgnoreCase(type)) {
        return curveIborFutureCurveNode(additionalSpread, futureNodeDateOrder);
      }
      if (FIXED_OVERNIGHT_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveFixedOvernightCurveNode(additionalSpread, valuationDate);
      }
      if (XCCY_FIXED_OVERNIGHT_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveXCcyFixedOvernightCurveNode(additionalSpread, valuationDate);
      }
      if (IBOR_IBOR_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveIborIborCurveNode(additionalSpread);
      }
      if (OVERNIGHT_IBOR_BASIS_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveOvernightIborCurveNode(additionalSpread);
      }
      if (XCCY_IBOR_IBOR_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveXCcyIborIborCurveNode(additionalSpread);
      }
      if (XCCY_OIS_OIS_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveXCcyOOCurveNode(additionalSpread);
      }
      if (XCCY_IBOR_OIS_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveXCcyIborOvernightCurveNode(additionalSpread);
      }
      if (CurveNodeTypes.FX_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveFxSwapCurveNode(additionalSpread);
      }
      if (FIXED_INFLATION_SWAP_NODE.equalsIgnoreCase(type)) {
        return curveFixedInflationCurveNode(additionalSpread);
      }
      if (TERM_DEPOSIT_NODE.equalsIgnoreCase(type)) {
        return termDepositNode(additionalSpread);
      }
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Unexpected type " + type + " for node " + getInstrument()));
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.PARSING_ERROR, ex.getMessage()));
    }
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>
      curveOvernightTermOvernightCurveNode(double additionalSpread) {
    return tenor()
        .map(
            p ->
                OvernightTermOvernightSwapCurveNode.builder()
                    .template(
                        OvernightTermOvernightSwapTradeTemplate.of(
                            p, OvernightTermOvernightSwapConvention.of(getConvention())))
                    .label(getInstrument())
                    .rateId(quoteId())
                    .additionalSpread(additionalSpread)
                    .date(CurveNodeDate.END)
                    .dateOrder(CurveNodeDateOrder.DEFAULT)
                    .build());
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>
      curveFixedInflationCurveNode(double additionalSpread) {
    return tenor()
        .map(
            p ->
                FixedInflationSwapCurveNode.builder()
                    .template(fixedInflationSwapTemplate(p))
                    .rateId(quoteId())
                    .additionalSpread(additionalSpread)
                    .label(getInstrument())
                    .date(CurveNodeDate.LAST_FIXING)
                    .build())
        .flatMap(Either::right);
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> termDepositNode(
      double additionalSpread) {
    return period()
        .map(
            p ->
                TermDepositCurveNode.builder()
                    .template(TermDepositTemplate.of(p, TermDepositConvention.of(getConvention())))
                    .rateId(quoteId())
                    .additionalSpread(additionalSpread)
                    .label(getInstrument())
                    .build())
        .flatMap(Either::right);
  }

  private FixedInflationSwapTemplate fixedInflationSwapTemplate(Tenor t) {
    return FixedInflationSwapTemplate.of(t, FixedInflationSwapConvention.of(getConvention()));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> curveFxSwapCurveNode(
      double additionalSpread) {
    return MarketTenorFxSwapTemplate.newOf(getPeriod(), getConvention())
        .map(
            template ->
                FxSwapCurveNode.builder()
                    .template(template)
                    .farForwardPointsId(quoteId())
                    .tnFarForwardPointsId(quoteId(MarketTenor.TN.getCode()))
                    .label(getInstrument())
                    .additionalSpread(additionalSpread)
                    .build());
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> curveXCcyIborIborCurveNode(
      double additionalSpread) {
    return tenor()
        .flatMap(
            p ->
                right(
                    XCcyIborIborSwapCurveNode.builder()
                        .template(
                            XCcyIborIborSwapTemplate.of(
                                p, XCcyIborIborSwapConvention.of(getConvention())))
                        .spreadId(quoteId())
                        .additionalSpread(additionalSpread)
                        .label(getInstrument())
                        .build()));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> curveXCcyOOCurveNode(
      double additionalSpread) {
    return tenor()
        .flatMap(
            p ->
                right(
                    XCcyOvernightOvernightSwapCurveNode.of(
                        XCcyOvernightOvernightSwapTemplate.of(
                            p, XCcyOvernightOvernightSwapConvention.of(getConvention())),
                        quoteId(),
                        additionalSpread,
                        getInstrument())));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>
      curveXCcyIborOvernightCurveNode(double additionalSpread) {
    return tenor()
        .flatMap(
            p ->
                right(
                    XCcyIborOvernightSwapCurveNode.of(
                        XCcyIborOvernightSwapTemplate.of(
                            p, XCcyIborOvernightSwapConvention.of(getConvention())),
                        quoteId(),
                        additionalSpread,
                        getInstrument())));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>
      curveOvernightIborCurveNode(double additionalSpread) {
    return tenor()
        .flatMap(
            p ->
                right(
                    OvernightIborSwapCurveNode.builder()
                        .template(
                            OvernightIborSwapTemplate.of(
                                p, OvernightIborSwapConvention.of(getConvention())))
                        .rateId(quoteId())
                        .additionalSpread(additionalSpread)
                        .label(getInstrument())
                        .build()));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> curveIborIborCurveNode(
      double additionalSpread) {
    return tenor()
        .flatMap(
            p ->
                right(
                    IborIborSwapCurveNode.builder()
                        .template(
                            IborIborSwapTemplate.of(p, IborIborSwapConvention.of(getConvention())))
                        .rateId(quoteId())
                        .additionalSpread(additionalSpread)
                        .label(getInstrument())
                        .build()));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>
      curveFixedOvernightCurveNode(double additionalSpread, LocalDate valuationDate) {
    return tenor(valuationDate)
        .flatMap(
            p ->
                right(
                    FixedOvernightSwapCurveNode.builder()
                        .template(
                            FixedOvernightSwapTemplate.of(
                                p, FixedOvernightSwapConvention.of(getConvention())))
                        .rateId(quoteId())
                        .additionalSpread(additionalSpread)
                        .label(getInstrument())
                        .build()));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>
      curveXCcyFixedOvernightCurveNode(double additionalSpread, LocalDate valuationDate) {
    return tenor(valuationDate)
        .flatMap(
            p ->
                right(
                    XCcyFixedOvernightSwapCurveNode.of(
                        XCcyFixedOvernightSwapTemplate.of(
                            p, XCcyFixedOvernightSwapConvention.of(getConvention())),
                        quoteId(),
                        additionalSpread,
                        getInstrument())));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> curveIborFutureCurveNode(
      double additionalSpread, CurveNodeDateOrder futureNodeDateOrder) {
    return parseSequenceDate()
        .map(
            sd ->
                IborFutureCurveNode.builder()
                    .template(IborFutureTemplate.of(sd, IborFutureContractSpec.of(getConvention())))
                    .rateId(quoteId())
                    .additionalSpread(additionalSpread)
                    .label(getInstrument())
                    .dateOrder(futureNodeDateOrder)
                    .build());
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> curveImmFraCurveNode(
      double additionalSpread) {
    var convention = ImmFraConvention.of(getConvention());
    return parseSequenceDate()
        .map(
            sd ->
                ImmFraCurveNode.builder()
                    .template(new ImmFraTemplate(sd, convention))
                    .rateId(quoteId())
                    .additionalSpread(additionalSpread)
                    .label(getInstrument())
                    .build());
  }

  private Either<ErrorItem, SequenceDate> parseSequenceDate() {
    var matcher = FUT_TIME_REGEX.matcher(getInstrument().toUpperCase(Locale.ENGLISH));
    if (!matcher.matches()) {
      return Either.left(
          new ErrorItem(Error.PARSING_ERROR, "Failed to parse serial future: " + getInstrument()));
    }
    var periodToStart = Period.parse("P" + matcher.group(1));
    var sequenceNumber = Integer.parseInt(matcher.group(2));
    return Either.right(SequenceDate.base(periodToStart, sequenceNumber));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> curveFraCurveNode(
      double additionalSpread) {
    Matcher matcher = FRA_TIME_REGEX.matcher(getInstrument().toUpperCase(Locale.ENGLISH));
    if (!matcher.matches()) {
      return Either.left(
          new ErrorItem(Error.PARSING_ERROR, "Failed to parse FRA period: " + getInstrument()));
    }
    Period periodToStart = Period.parse("P" + matcher.group(1) + "M");
    Period periodToEnd = Period.parse("P" + matcher.group(2) + "M");

    return right(
        FraCurveNode.builder()
            .template(FraTemplate.of(periodToStart, periodToEnd, FraConvention.of(getConvention())))
            .rateId(quoteId())
            .additionalSpread(additionalSpread)
            .label(getInstrument())
            .build());
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>
      curveIborFixingDepositCurveNode(double additionalSpread) {
    var iborIndex = IborIndex.of(getConvention());
    var offshoreIbor =
        OffshoreIndices.lookupOffshoreIbor(iborIndex).filter(i -> isOffshore).orElse(iborIndex);

    return period()
        .flatMap(
            p ->
                right(
                    IborFixingDepositCurveNode.builder()
                        .template(
                            IborFixingDepositTemplate.of(
                                p, IborFixingDepositConvention.of(offshoreIbor)))
                        .rateId(quoteId())
                        .additionalSpread(additionalSpread)
                        .label(getInstrument())
                        .build()));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode>
      curveTermOisFixingDepositCurveNode(double additionalSpread) {
    var overnightTermIndex = OvernightTermIndex.of(getConvention());

    return period()
        .flatMap(
            p ->
                right(
                    TermOisFixingDepositCurveNode.builder()
                        .template(
                            new TermOisFixingDepositNodeTemplate(
                                p, TermOisFixingDepositConvention.of(overnightTermIndex.getName())))
                        .rateId(quoteId())
                        .additionalSpread(additionalSpread)
                        .label(getInstrument())
                        .build()));
  }

  private Either<ErrorItem, com.opengamma.strata.market.curve.CurveNode> curveFixedIborCurveNode(
      double additionalSpread) {
    return tenor()
        .flatMap(
            tenor ->
                right(
                    FixedIborSwapCurveNode.builder()
                        .template(
                            FixedIborSwapTemplate.of(
                                tenor, FixedIborSwapConvention.of(getConvention())))
                        .rateId(quoteId())
                        .additionalSpread(additionalSpread)
                        .label(getInstrument())
                        .build()));
  }

  private QuoteId quoteId() {
    return MarketDataUtils.quoteId(getKey());
  }

  private QuoteId quoteId(String instrument) {
    return MarketDataUtils.quoteId(getKey(instrument));
  }

  private String getKey() {
    return semanticMarketDataKey(curveNode, clearingHouse);
  }

  private String getKey(String instrument) {
    return semanticMarketDataKey(curveNode, instrument);
  }

  private Either<ErrorItem, Tenor> tenor() {
    return period().map(Tenor::of);
  }

  private Either<ErrorItem, Tenor> tenor(LocalDate valuationDate) {
    if (!getConvention().contains(Currency.BRL.getCode())) {
      return tenor();
    }

    Matcher matcher = OIS_FUT_TIME_REGEX.matcher(getInstrument().toUpperCase(Locale.ENGLISH));
    if (!matcher.matches()) {
      return Either.left(
          new ErrorItem(
              Error.PARSING_ERROR, "Failed to parse OIS Future period: " + getInstrument()));
    }

    var seqNo = parseOisFutureTenorSeqNo(getInstrument());
    if (seqNo == 0) {
      return period()
          .map(Tenor::of)
          .map(
              t ->
                  t.equals(TENOR_1D)
                      ? t
                      : adjustedOisTenor(valuationDate.plus(t), valuationDate, referenceData));
    } else {
      var adjustedTenor =
          adjustedOisTenor(
              QUARTERLY_BRL_IMM.nth(valuationDate, seqNo), valuationDate, referenceData);
      return right(adjustedTenor);
    }
  }

  private LocalDate adjustBrlOisDate(LocalDate date) {
    var period = getPeriod();
    if (getOisFutureAnnualPeriodMatcher(period).matches()) {
      return LocalDate.of(date.getYear(), Month.JANUARY.getValue(), 1);
    } else if (getOisFutureSemiAnnualPeriodMatcher(period).matches()) {
      var month = date.getMonthValue() < Month.JULY.getValue() ? Month.JANUARY : Month.JULY;
      return LocalDate.of(date.getYear(), month.getValue(), 1);
    } else if (getOisFutureQuarterPeriodMatcher(period).matches()) {
      var month = date.getMonth().firstMonthOfQuarter();
      return LocalDate.of(date.getYear(), month.getValue(), 1);
    }
    return date;
  }

  private Tenor adjustedOisTenor(
      LocalDate date, LocalDate valuationDate, ReferenceData referenceData) {
    var adjustedDate = adjustBrlOisDate(date);
    var c = FixedOvernightSwapConvention.of(getConvention());
    var adjustedStart = c.getSpotDateOffset().adjust(valuationDate, referenceData);
    var adjustedEnd =
        c.getFloatingLeg()
            .getEndDateBusinessDayAdjustment()
            .adjust(adjustedDate.withDayOfMonth(1), referenceData);
    return Tenor.ofDays((int) ChronoUnit.DAYS.between(adjustedStart, adjustedEnd));
  }

  private Either<ErrorItem, Period> period() {
    return PeriodUtils.period(getInstrument());
  }

  private String getType() {
    return curveNode.getType();
  }

  private String getInstrument() {
    return curveNode.getInstrument();
  }

  private String getConvention() {
    return curveNode.getConvention();
  }

  private String getPeriod() {
    return curveNode.getPeriod();
  }
}
