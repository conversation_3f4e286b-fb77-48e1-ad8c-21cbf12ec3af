package com.solum.xplain.core.curvegroup.curvegroup;

import static com.solum.xplain.core.common.AggregateFields.COUNT_FIELD;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationStatus.CALIBRATED;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static io.atlassian.fugue.Either.right;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Streams;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.value.EntityNameView;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.HasInstruments;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curvebond.BondCurveRepository;
import com.solum.xplain.core.curvegroup.curvecredit.CurveGroupCreditCurveRepository;
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup;
import com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationMarketData;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupCountedView;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupFilter;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupForm;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupsCounts;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvegroup.ratefx.CurveGroupFxRatesRepository;
import com.solum.xplain.core.curvegroup.volatility.CurveGroupVolatilityRepository;
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityRepository;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.events.MarketDataGroupUpdated;
import com.solum.xplain.core.market.repository.MarketDataGroupRepository;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.users.events.UserUpdated;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@AllArgsConstructor
@Repository
@NullMarked
public class CurveGroupRepository {

  private static final String CURVE_GROUP_NOT_FOUND = "Curve group not found";
  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final MarketDataGroupRepository marketDataGroupRepository;
  private final CurveGroupMapper curveGroupMapper;
  private final CurveGroupCurveRepository curveGroupCurveRepository;
  private final CurveGroupVolatilityRepository curveGroupVolatilityRepository;
  private final CurveGroupCreditCurveRepository curveGroupCreditCurveRepository;
  private final CurveGroupFxRatesRepository curveGroupFxRateRepository;
  private final CurveGroupFxVolatilityRepository curveGroupFxVolRepository;
  private final BondCurveRepository bondCurveRepository;
  private final AuditingHandler auditingHandler;

  public Map<String, Either<ErrorItem, CurveGroupView>> getCurveGroups(Collection<String> ids) {
    var curveGroups = StreamEx.of(curveGroups(ids)).toMap(CurveGroupView::getId, identity());
    return StreamEx.of(ids)
        .toMap(
            it -> {
              var curveGroup = curveGroups.get(it);
              if (curveGroup != null) {
                return Either.right(curveGroup);
              } else {
                return Either.left(OBJECT_NOT_FOUND.entity(CURVE_GROUP_NOT_FOUND + " of id " + it));
              }
            });
  }

  public Either<ErrorItem, CurveGroupView> getGroup(String id) {
    return curveGroup(id)
        .map(Either::<ErrorItem, CurveGroupView>right)
        .orElse(Either.left(OBJECT_NOT_FOUND.entity(CURVE_GROUP_NOT_FOUND)));
  }

  public Either<ErrorItem, CurveGroupView> getEither(String id) {
    return curveGroup(id)
        .map(Either::<ErrorItem, CurveGroupView>right)
        .orElse(Either.left(OBJECT_NOT_FOUND.entity(CURVE_GROUP_NOT_FOUND)));
  }

  public Either<ErrorItem, CurveGroupCountedView> getCountedEither(LocalDate stateDate, String id) {
    var counts =
        countsForGroup(CurveGroupEntryFilter.singleGroup(stateDate, id)).countsForGroup(id);
    return getEither(id).map(view -> curveGroupMapper.toCountedView(view, counts));
  }

  public Either<ErrorItem, CurveGroup> getFull(String curveGroupId) {
    CurveGroup curveGroup = mongoOperations.findById(curveGroupId, CurveGroup.class);
    if (curveGroup == null || curveGroup.isArchived()) {
      return Either.left(OBJECT_NOT_FOUND.entity(CURVE_GROUP_NOT_FOUND));
    } else {
      return right(curveGroup);
    }
  }

  private Optional<CurveGroupView> curveGroup(String id) {
    ProjectionOperation projection = curveGroupViewProjection();
    return ofNullable(
        mongoOperations
            .aggregate(
                newAggregation(
                    CurveGroup.class, match(where(CurveGroup.Fields.id).is(id)), projection),
                CurveGroupView.class)
            .getUniqueMappedResult());
  }

  private Stream<CurveGroupView> curveGroups(Collection<String> ids) {
    ProjectionOperation projection = curveGroupViewProjection();
    return mongoOperations.aggregateStream(
        newAggregation(CurveGroup.class, match(where(CurveGroup.Fields.id).in(ids)), projection),
        CurveGroupView.class);
  }

  private ProjectionOperation curveGroupViewProjection() {
    return project()
        .and(CurveGroup.Fields.id)
        .as(CurveGroupView.Fields.id)
        .and(CurveGroup.Fields.name)
        .as(CurveGroupView.Fields.name)
        .and(CurveGroup.Fields.updatedAt)
        .as(CurveGroupView.Fields.updatedAt)
        .and(CurveGroup.Fields.createdAt)
        .as(CurveGroupView.Fields.createdAt)
        .and(propertyName(CurveGroup.Fields.createdBy, AuditUser.Fields.userId))
        .as(CurveGroupView.Fields.creatorId)
        .and(propertyName(CurveGroup.Fields.createdBy, AuditUser.Fields.name))
        .as(CurveGroupView.Fields.creatorName)
        .and(propertyName(CurveGroup.Fields.modifiedBy, AuditUser.Fields.name))
        .as(CurveGroupView.Fields.modifiedBy)
        .and(
            propertyName(
                CurveGroup.Fields.calibrationMarketDataGroup,
                CalibrationMarketData.Fields.marketDataGroupId))
        .as(CurveGroupView.Fields.calibrationMarketDataGroupId)
        .and(
            propertyName(
                CurveGroup.Fields.calibrationMarketDataGroup,
                CalibrationMarketData.Fields.curveConfigurationId))
        .as(CurveGroupView.Fields.calibrationCurveConfigId)
        .and(
            propertyName(
                CurveGroup.Fields.calibrationMarketDataGroup,
                CalibrationMarketData.Fields.sourceType))
        .as(CurveGroupView.Fields.calibrationMarketDataSourceType)
        .and(
            propertyName(
                CurveGroup.Fields.calibrationMarketDataGroup,
                CalibrationMarketData.Fields.marketDataGroupName))
        .as(CurveGroupView.Fields.calibrationMarketDataGroupName)
        .and(CurveGroup.Fields.calibrationStatus)
        .as(CurveGroupView.Fields.calibrationStatus)
        .and(CurveGroup.Fields.calibrationDate)
        .as(CurveGroupView.Fields.calibrationDate)
        .and(CurveGroup.Fields.calibratedAt)
        .as(CurveGroupView.Fields.calibratedAt)
        .and(CurveGroup.Fields.calibrationCurrency)
        .as(CurveGroupView.Fields.calibrationCurrency)
        .and(CurveGroup.Fields.calibrationStrippingType)
        .as(CurveGroupView.Fields.calibrationStrippingType)
        .and(CurveGroup.Fields.calibrationPriceRequirements)
        .as(CurveGroupView.Fields.calibrationPriceRequirements)
        .and(CurveGroup.Fields.archived)
        .as(CurveGroupView.Fields.archived)
        .and(CurveGroup.Fields.auditLogs)
        .as(CurveGroupView.Fields.auditLogs);
  }

  public boolean hasCurveGroupByName(String name, String excludeSelfId) {
    Criteria findDuplicate =
        where(CurveGroup.Fields.name).is(name).and(CurveGroup.Fields.archived).is(false);
    if (StringUtils.isNotEmpty(excludeSelfId)) {
      findDuplicate = findDuplicate.and(CurveGroup.Fields.id).ne(excludeSelfId);
    }
    return mongoOperations.exists(query(findDuplicate), CurveGroup.class);
  }

  public List<CurveGroupView> curveGroupList() {
    var agg =
        newAggregation(
            CurveGroup.class,
            List.of(
                match(where(CurveGroup.Fields.archived).is(false)), curveGroupViewProjection()));
    return mongoOperations.aggregate(agg, CurveGroupView.class).getMappedResults();
  }

  public ScrollableEntry<CurveGroupCountedView> curveGroupCountedList(
      LocalDate stateDate,
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      CurveGroupFilter filter) {
    List<AggregationOperation> baseOperations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(filter.criteria()),
                curveGroupViewProjection(),
                match(tableFilter.criteria(CurveGroupView.class, conversionService)))
            .build();

    TypedAggregation<CurveGroup> aggregation =
        newAggregation(
            CurveGroup.class,
            ImmutableList.<AggregationOperation>builder()
                .addAll(baseOperations)
                .add(count().as(COUNT_FIELD))
                .build());
    var totalRes = mongoOperations.aggregate(aggregation, Map.class);

    return ofNullable(totalRes.getUniqueMappedResult())
        .map(r -> r.get(COUNT_FIELD))
        .map(Number.class::cast)
        .map(
            t ->
                ScrollableEntry.of(
                    curveGroups(scrollRequest, baseOperations), scrollRequest, t.longValue()))
        .map(result -> toCountedViewScrollable(stateDate, result))
        .orElse(ScrollableEntry.empty());
  }

  private ScrollableEntry<CurveGroupCountedView> toCountedViewScrollable(
      LocalDate stateDate, ScrollableEntry<CurveGroupView> scrollableEntry) {
    var groupIds = scrollableEntry.getContent().stream().map(CurveGroupView::getId).toList();
    final CurveGroupsCounts counts;
    if (groupIds.isEmpty()) {
      counts = CurveGroupsCounts.empty();
    } else {
      counts = countsForGroup(CurveGroupEntryFilter.listOfGroups(stateDate, groupIds));
    }

    return scrollableEntry.map(
        cg -> curveGroupMapper.toCountedView(cg, counts.countsForGroup(cg.getId())));
  }

  private List<CurveGroupView> curveGroups(
      ScrollRequest scrollRequest, List<AggregationOperation> baseOperations) {
    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder().addAll(baseOperations);
    operations.addAll(new ScrollSortOperations(scrollRequest, CurveGroupView.Fields.id).build());

    TypedAggregation<CurveGroup> aggregation = newAggregation(CurveGroup.class, operations.build());
    return mongoOperations.aggregate(aggregation, CurveGroupView.class).getMappedResults();
  }

  public List<CurveGroup> marketDataGroupCalibratedGroups(String marketDataGroupId) {
    return mongoOperations
        .query(CurveGroup.class)
        .matching(
            where(
                    propertyName(
                        CurveGroup.Fields.calibrationMarketDataGroup,
                        CalibrationMarketData.Fields.marketDataGroupId))
                .is(marketDataGroupId))
        .all();
  }

  public Either<ErrorItem, EntityId> insert(CurveGroupForm newForm) {
    CurveGroup group = new CurveGroup();
    group.setName(newForm.getName());
    mongoOperations.insert(group);
    return right(entityId(group.getId()));
  }

  public Either<ErrorItem, EntityId> update(CurveGroupView view, CurveGroupForm edit) {
    return updateCurveGroup(
        view.getId(),
        group -> {
          group.setName(edit.getName());
          return group;
        });
  }

  public Either<ErrorItem, EntityId> archive(String id) {
    return updateCurveGroup(id, CurveGroup::archived);
  }

  public Either<ErrorItem, EntityId> updateCurveGroupCalibration(
      String id,
      CalculationDiscountingType calibrationCurrency,
      CalculationStrippingType calibrationStrippingType,
      LocalDate valuationDate,
      CurveConfigMarketStateKey stateKey) {
    return updateCurveGroup(
        id,
        curveGroup -> {
          curveGroup.setCalibrationCurrency(calibrationCurrency);
          curveGroup.setCalibrationStrippingType(calibrationStrippingType);
          curveGroup.setCalibrationStatus(CALIBRATED);
          curveGroup.setCalibratedAt(LocalDateTime.now());
          curveGroup.setCalibrationDate(valuationDate);
          curveGroup.setCalibrationPriceRequirements(stateKey.getPriceRequirements());

          marketDataGroupRepository
              .dataGroupName(stateKey.getMarketDataGroupId())
              .map(m -> calibrationMarketData(stateKey, m))
              .ifPresent(curveGroup::setCalibrationMarketDataGroup);
          return curveGroup;
        });
  }

  private CalibrationMarketData calibrationMarketData(
      CurveConfigMarketStateKey stateKey, EntityNameView marketDataGroupName) {
    return new CalibrationMarketData(
        marketDataGroupName, stateKey.getMarketDataSource(), stateKey.getConfigurationId());
  }

  private Either<ErrorItem, EntityId> updateCurveGroup(
      String curveGroupId, UnaryOperator<CurveGroup> modifyCurveGroup) {
    CurveGroup curveGroup = mongoOperations.findById(curveGroupId, CurveGroup.class);
    if (curveGroup == null) {
      return Either.left(Error.OBJECT_NOT_FOUND.entity(CURVE_GROUP_NOT_FOUND));
    } else {
      var newEntity = mongoOperations.findById(curveGroupId, CurveGroup.class);
      newEntity = modifyCurveGroup.apply(newEntity);
      var diff = curveGroup.diff(newEntity);
      if (diff.numberOfDiffs() > 0) {
        newEntity.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
      }
      mongoOperations.save(newEntity);
      return right(entityId(curveGroup.getId()));
    }
  }

  public Either<ErrorItem, EntityId> clearCalibrationResults(String groupId) {
    curveGroupCreditCurveRepository.clearCalibrationResults(groupId);
    curveGroupCurveRepository.clearCalibrationResults(groupId);
    bondCurveRepository.clearGroupCalibrationResults(groupId);
    return updateCurveGroup(groupId, CurveGroup::clearCalibrationResults);
  }

  @EventListener
  public void onMarketDataGroupUpdated(MarketDataGroupUpdated event) {
    mongoOperations.updateMulti(
        query(
            where(
                    propertyName(
                        CurveGroup.Fields.calibrationMarketDataGroup,
                        CalibrationMarketData.Fields.marketDataGroupId))
                .is(event.getEntityId())),
        Update.update(
            propertyName(
                CurveGroup.Fields.calibrationMarketDataGroup,
                CalibrationMarketData.Fields.marketDataGroupName),
            event.getForm().getName()),
        CurveGroup.class);
  }

  @EventListener
  public void onEvent(UserUpdated event) {
    mongoOperations.updateMulti(
        query(
            where(propertyName(CurveGroup.Fields.createdBy, AuditUser.Fields.userId))
                .is(event.getEntityId())),
        Update.update(
                propertyName(CurveGroup.Fields.createdBy, AuditUser.Fields.name),
                event.getForm().getName())
            .set(
                propertyName(CurveGroup.Fields.createdBy, AuditUser.Fields.username),
                event.getForm().getUsername()),
        CurveGroup.class);
    mongoOperations.updateMulti(
        query(
            where(propertyName(CurveGroup.Fields.modifiedBy, AuditUser.Fields.userId))
                .is(event.getEntityId())),
        Update.update(
                propertyName(CurveGroup.Fields.modifiedBy, AuditUser.Fields.name),
                event.getForm().getName())
            .set(
                propertyName(CurveGroup.Fields.modifiedBy, AuditUser.Fields.username),
                event.getForm().getUsername()),
        CurveGroup.class);
  }

  private CurveGroupsCounts countsForGroup(CurveGroupEntryFilter filter) {
    return CurveGroupsCounts.builder()
        .curvesCounts(curveGroupCurveRepository.activeEntriesCount(filter))
        .creditCurvesCounts(curveGroupCreditCurveRepository.activeEntriesCount(filter))
        .bondCurveCounts(bondCurveRepository.activeEntriesCount(filter))
        .fxRatesCounts(curveGroupFxRateRepository.getRatesNodesCount(filter))
        .fxVolsCounts(curveGroupFxVolRepository.getVolatilityNodesCount(filter))
        .surfacesCounts(curveGroupVolatilityRepository.activeEntriesCount(filter))
        .build();
  }

  public List<InstrumentDefinition> allInstruments(String groupId, BitemporalDate stateDate) {
    return Streams.concat(
            instruments(curveGroupCurveRepository.getActiveCurves(groupId, stateDate)),
            instruments(curveGroupVolatilityRepository.getActiveSurfaces(groupId, stateDate)),
            instruments(curveGroupFxRateRepository.getRatesNodes(groupId, stateDate)),
            instruments(curveGroupFxVolRepository.getVolatilityNodes(groupId, stateDate)),
            instruments(curveGroupCreditCurveRepository.getActiveCurves(groupId, stateDate)),
            instruments(bondCurveRepository.getActiveCurves(groupId, stateDate)))
        .toList();
  }

  private <U extends HasInstruments> Stream<InstrumentDefinition> instruments(
      List<U> hasInstruments) {
    return ofNullable(hasInstruments).stream()
        .flatMap(List::stream)
        .flatMap(v -> v.allInstruments().stream());
  }
}
