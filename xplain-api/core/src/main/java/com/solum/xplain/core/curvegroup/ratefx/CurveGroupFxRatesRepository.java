package com.solum.xplain.core.curvegroup.ratefx;

import static com.solum.xplain.core.classifiers.Constants.EXPLICIT_CURRENCIES;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntryCountSupport;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryCount;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter;
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates;
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNode;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesForm;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeValueView;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import one.util.streamex.StreamEx;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@NullMarked
public class CurveGroupFxRatesRepository
    extends GenericUniqueVersionedEntityRepository<CurveGroupFxRates> {
  private final CurveGroupFxRateMapper mapper;
  private final CurveGroupEntryCountSupport countSupport;

  public CurveGroupFxRatesRepository(
      MongoOperations mongoOperations,
      CurveGroupFxRateMapper mapper,
      CurveGroupEntryCountSupport countSupport) {
    super(mongoOperations, mapper);
    this.mapper = mapper;
    this.countSupport = countSupport;
  }

  public static Criteria uniqueEntityCriteria(String groupId) {
    return where(VersionedEntity.Fields.entityId).is(groupId);
  }

  @Override
  protected Criteria uniqueEntityCriteria(CurveGroupFxRates entity) {
    return uniqueEntityCriteria(entity.getEntityId());
  }

  public Either<ErrorItem, EntityId> createRates(String groupId, CurveGroupFxRatesForm form) {
    var entity = mapper.fromForm(form, CurveGroupFxRates.newOf(groupId));
    return insert(entity, form.getVersionForm());
  }

  public Either<ErrorItem, EntityId> updateRates(
      String groupId, LocalDate version, CurveGroupFxRatesForm form) {
    return entityExact(groupId, version)
        .map(
            entity ->
                update(
                    entity,
                    form.getVersionForm(),
                    copiedEntity -> mapper.fromForm(form, copiedEntity)));
  }

  public Either<ErrorItem, EntityId> deleteRates(String groupId, LocalDate version) {
    return entityExact(groupId, version).flatMap(this::delete);
  }

  public Either<ErrorItem, CurveGroupFxRates> getActiveRates(
      String groupId, BitemporalDate stateDate) {
    return entity(groupId, stateDate, active());
  }

  public Either<ErrorItem, CurveGroupFxRatesView> getActiveRatesView(
      String groupId, BitemporalDate stateDate) {
    return getActiveRates(groupId, stateDate).map(mapper::toView);
  }

  public List<CurveGroupFxRatesView> getRatesVersionViews(String groupId) {
    return entityVersions(groupId).stream().map(mapper::toView).toList();
  }

  public DateList getFutureVersions(String groupId, LocalDate stateDate) {
    var searchCriteria = uniqueEntityCriteria(groupId);
    return futureVersionsByCriteria(searchCriteria, stateDate);
  }

  public List<CurveGroupEntryCount> getRatesNodesCount(CurveGroupEntryFilter filter) {
    return countSupport.activeFxRateEntriesCount(filter);
  }

  public List<CurveGroupFxRatesNode> getRatesNodes(String groupId, BitemporalDate stateDate) {
    return getActiveRates(groupId, stateDate)
        .map(e -> List.copyOf(e.getNodes()))
        .getOrElse(List.of());
  }

  public VersionedList<CurveGroupFxRatesNodeValueView> getRatesNodesValuesViews(
      String groupId, LocalDate stateDate, Map<String, CalculationMarketValueFullView> fxRates) {
    return getActiveRates(groupId, new BitemporalDate(stateDate))
        .map(v -> mergeCurveGroupFxRates(fxRates, v))
        .getOrElse(VersionedList.empty());
  }

  public VersionedList<CurveGroupFxRatesNodeValueView> mergeCurveGroupFxRates(
      Map<String, CalculationMarketValueFullView> fxRates, CurveGroupFxRates v) {
    return new VersionedList<>(
        v.getValidFrom(),
        v.getNodes().stream()
            .sorted(fxRatesComparator())
            .map(n -> nodeCalculatedView(fxRates, n))
            .toList());
  }

  public Map<String, Either<ErrorItem, CurveGroupFxRates>> findActiveRates(
      Set<String> groupIds, BitemporalDate stateDate) {
    var result =
        StreamEx.of(
                entities(stateDate, active(), where(VersionedEntity.Fields.entityId).in(groupIds)))
            .toMap(VersionedEntity::getEntityId, Either::<ErrorItem, CurveGroupFxRates>right);
    for (String groupId : groupIds) {
      result.computeIfAbsent(
          groupId,
          k ->
              Either.left(
                  Error.OBJECT_NOT_FOUND.entity("No active rate found for group = " + groupId)));
    }
    return result;
  }

  private CurveGroupFxRatesNodeValueView nodeCalculatedView(
      Map<String, CalculationMarketValueFullView> fxRates, CurveGroupFxRatesNode node) {
    var rate = Optional.ofNullable(fxRates.get(node.getKey()));
    return mapper.toNodeValueView(
        node, rate.map(CalculationMarketValueFullView::getValue).orElse(null));
  }

  private static Comparator<CurveGroupFxRatesNode> fxRatesComparator() {
    return Comparator.<CurveGroupFxRatesNode>comparingInt(
            r -> EXPLICIT_CURRENCIES.indexOf(Currency.of(r.getDomesticCurrency())))
        .thenComparing(CurveGroupFxRatesNode::getForeignCurrency);
  }
}
