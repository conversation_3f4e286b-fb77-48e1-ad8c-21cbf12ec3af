package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.*;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.csv.DayCountCsvUtils;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CommonSwapCsvMapper {

  public Iterable<CsvField> toCsvFields(TradeDetails details) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder
        .add(new CsvField(TRADE_ACCRUAL_SCHEDULE_START_DATE, details.getStartDate()))
        .add(new CsvField(TRADE_ACCRUAL_SCHEDULE_END_DATE, details.getEndDate()))
        .add(new CsvField(TRADE_BUSINESS_DAY_CONVENTION, details.getBusinessDayConvention()))
        .add(
            new CsvField(
                TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE, details.getBusinessDayAdjustmentType()))
        .add(new CsvField(TRADE_ROLL_CONVETIONT, details.getRollConvention()));
    builder
        .add(
            new CsvField(
                TRADE_NOTIONAL_SCHEDULE_INITIAL_EXCHANGE,
                details.getNotionalScheduleInitialExchange()))
        .add(
            new CsvField(
                TRADE_NOTIONAL_SCHEDULE_FINAL_EXCHANGE, details.getNotionalScheduleFinalExchange()))
        .add(new CsvField(TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION, details.getStubConvention()))
        .add(
            new CsvField(
                TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE, details.getFirstRegularStartDate()))
        .add(
            new CsvField(TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE, details.getLastRegularEndDate()));

    builder.addAll(toCsvFields(details.getPayLeg(), LEG_1));
    builder.addAll(toCsvFields(details.getReceiveLeg(), LEG_2));
    return builder.build();
  }

  private Iterable<CsvField> toCsvFields(TradeLegDetails leg, String legId) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(
        new CsvField(addField(legId, LEG_IDENTIFIER), leg.getExtLegIdentifier()),
        new CsvField(addField(legId, PAY_RECEIVE), leg.getPayReceive().getName()),
        new CsvField(addField(legId, LEG_TYPE), leg.getType().getLabel()),
        new CsvField(addField(legId, ACRUAL_SCHEDULE_FREQUENCY), leg.getAccrualFrequency()),
        new CsvField(addField(legId, PAYMENT_SCHEDULE_FREQUENCY), leg.getPaymentFrequency()),
        new CsvField(addField(legId, PAYMENT_SCHEDULE_DATE_OFFSET), leg.getPaymentOffsetDays()),
        new CsvField(
            addField(legId, PAYMENT_SCHEDULE_COMPOUNDING_METHOD), leg.getPaymentCompounding()),
        new CsvField(addField(legId, LEG_CURRENCY), leg.getCurrency()),
        new CsvField(addField(legId, LEG_NOTIONAL), leg.getNotional()));

    switch (leg.getType()) {
      case OVERNIGHT -> builder.addAll(overnightFields(legId, leg));
      case FIXED -> builder.addAll(fixedLegDetails(legId, leg));
      case INFLATION -> builder.addAll(inflationFields(legId, leg));
      case IBOR -> builder.addAll(iborFields(legId, leg));
      case TERM_OVERNIGHT -> builder.addAll(termOvernightFields(legId, leg));
      default -> throw new IllegalArgumentException("Unknown calculation type: " + leg.getType());
    }
    return builder.build();
  }

  private Iterable<CsvField> overnightFields(String legId, TradeLegDetails legDetails) {
    return List.of(
        new CsvField(
            addField(legId, CALCULATION_OVERNIGHT_DAY_COUNT),
            DayCountCsvUtils.toExportLabel(legDetails.getDayCount())),
        new CsvField(addField(legId, CALCULATION_OVERNIGHT_INDEX), legDetails.getIndex()),
        new CsvField(
            addField(legId, CALCULATION_OVERNIGHT_SPREAD_VALUE), legDetails.getInitialValue()),
        new CsvField(
            addField(legId, CALCULATION_OVERNIGHT_RATE_CUTOFF_DAYS),
            legDetails.getOvernightRateCutOffDays()),
        new CsvField(
            addField(legId, CALCULATION_OVERNIGHT_ACCRUAL_METHOD), legDetails.getAccrualMethod()),
        new CsvField(addField(legId, OFFSHORE), legDetails.getIsOffshore()));
  }

  private Iterable<CsvField> inflationFields(String legId, TradeLegDetails legDetails) {
    return List.of(
        new CsvField(addField(legId, CALCULATION_INFLATION_INDEX), legDetails.getIndex()),
        new CsvField(addField(legId, CALCULATION_INFLATION_LAG), legDetails.getInflationLag()),
        new CsvField(
            addField(legId, CALCULATION_INFLATION_CALCULATION_METHOD),
            legDetails.getIndexCalculationMethod()));
  }

  private Iterable<CsvField> iborFields(String legId, TradeLegDetails legDetails) {
    return List.of(
        new CsvField(
            addField(legId, CALCULATION_IBOR_DAY_COUNT),
            DayCountCsvUtils.toExportLabel(legDetails.getDayCount())),
        new CsvField(addField(legId, CALCULATION_IBOR_INDEX), legDetails.getIndex()),
        new CsvField(
            addField(legId, CALCULATION_IBOR_FIXING_OFFSET), legDetails.getFixingDateOffsetDays()),
        new CsvField(addField(legId, CALCULATION_IBOR_SPREAD_VALUE), legDetails.getInitialValue()),
        new CsvField(addField(legId, OFFSHORE), legDetails.getIsOffshore()));
  }

  private Iterable<CsvField> fixedLegDetails(String legId, TradeLegDetails legDetails) {
    return List.of(
        new CsvField(
            addField(legId, CALCULATION_FIXED_DAY_COUNT),
            DayCountCsvUtils.toExportLabel(legDetails.getDayCount())),
        new CsvField(addField(legId, CALCULATION_FIXED_RATE_VALUE), legDetails.getInitialValue()),
        new CsvField(
            addField(legId, CALCULATION_FIXED_ACCRUAL_METHOD), legDetails.getAccrualMethod()));
  }

  private Iterable<CsvField> termOvernightFields(String legId, TradeLegDetails legDetails) {
    return List.of(
            new CsvField(
                    addField(legId, CALCULATION_TERM_OVERNIGHT_DAY_COUNT),
                    DayCountCsvUtils.toExportLabel(legDetails.getDayCount())),
            new CsvField(addField(legId, CALCULATION_TERM_OVERNIGHT_INDEX), legDetails.getIndex()),
            new CsvField(
                    addField(legId, CALCULATION_TERM_OVERNIGHT_SPREAD_VALUE), legDetails.getInitialValue()),
            new CsvField(
                    addField(legId, CALCULATION_TERM_OVERNIGHT_FIXING_OFFSET),
                    legDetails.getFixingDateOffsetDays()),
            new CsvField(
                    addField(legId, CALCULATION_TERM_OVERNIGHT_ACCRUAL_METHOD),
                    legDetails.getAccrualMethod()),
            new CsvField(addField(legId, OFFSHORE), legDetails.getIsOffshore()));
  }
}
