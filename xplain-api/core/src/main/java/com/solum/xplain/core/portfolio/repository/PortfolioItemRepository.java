package com.solum.xplain.core.portfolio.repository;

import static com.solum.xplain.core.common.AggregateFields.COUNT_FIELD;
import static com.solum.xplain.core.common.AggregateOptions.ALLOW_DISK_USE_BATCH_1000;
import static com.solum.xplain.core.common.CollectionUtils.convertCollectionTo;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntityMongoOperations.entityIdCriteria;
import static com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntityMongoOperations.latestItemsCriteria;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;
import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID_REF;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.solum.xplain.core.common.CountResult;
import com.solum.xplain.core.common.GroupRequest;
import com.solum.xplain.core.common.GroupRequestUtils;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionsSupport;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.PortfolioItemSearchForm;
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails;
import com.solum.xplain.core.portfolio.trade.OnboardingDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.value.AllocationTradeListView;
import com.solum.xplain.core.portfolio.value.PortfolioItemWithKeyView;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationExpression;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ConvertOperators.ToString;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.aggregation.StringOperators;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@NullMarked
public class PortfolioItemRepository {

  private static final int BATCH_SIZE = 10000;

  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final DateRangeVersionsSupport dateRangeVersionsSupport;

  public PortfolioItemRepository(
      MongoOperations mongoOperations,
      ConversionService conversionService,
      DateRangeVersionsSupport dateRangeVersionsSupport) {
    this.mongoOperations = mongoOperations;
    this.conversionService = conversionService;
    this.dateRangeVersionsSupport = dateRangeVersionsSupport;
  }

  public Either<ErrorItem, PortfolioItem> portfolioItemLatest(
      String portfolioId, String entityId, BitemporalDate vd) {
    return mongoOperations
        .query(PortfolioItem.class)
        .matching(entityIdCriteria(portfolioCriteria(portfolioId), vd, active(), entityId))
        .first()
        .map(Either::<ErrorItem, PortfolioItem>right)
        .orElseGet(() -> Either.left(OBJECT_NOT_FOUND.entity("Trade not found")));
  }

  public boolean hasFutureVersions(String portfolioId, LocalDate stateDate) {
    return dateRangeVersionsSupport.hasFutureVersions(
        portfolioCriteria(portfolioId), stateDate, PortfolioItem.class);
  }

  public DateList futureVersions(String portfolioId, PortfolioItemSearchForm form) {
    var futureVersionCriteria =
        portfolioCriteria(portfolioId)
            .andOperator(
                dateRangeVersionsSupport
                    .createFutureVersionsCriteria(form.getStateDate())
                    .and(VersionedTradeEntity.Fields.externalTradeId)
                    .is(form.getExternalTradeId()));
    return dateRangeVersionsSupport.futureVersions(futureVersionCriteria, PortfolioItem.class);
  }

  public boolean hasPortfolioItemByExternalTradeId(
      BitemporalDate stateDate, String portfolioId, String externalTradeId, String tradeEntityId) {
    return portfolioItemByExternalId(stateDate, portfolioId, externalTradeId)
        .filter(v -> !StringUtils.equals(tradeEntityId, v.getEntityId()))
        .isPresent();
  }

  private Optional<PortfolioItem> portfolioItemByExternalId(
      BitemporalDate stateDate, String portfolioId, String externalTradeId) {
    return mongoOperations
        .query(PortfolioItem.class)
        .matching(externalIdCriteria(portfolioId, stateDate, active(), externalTradeId))
        .first();
  }

  private Criteria externalIdCriteria(
      String portfolioId,
      BitemporalDate stateDate,
      VersionedEntityFilter entityFilter,
      String externalId) {
    return latestItemsCriteria(portfolioCriteria(portfolioId), stateDate, entityFilter)
        .and(VersionedTradeEntity.Fields.externalTradeId)
        .is(externalId);
  }

  private Set<String> findValuationDataKeys(Criteria baseCriteria, BitemporalDate bitemporalDate) {
    var operations =
        List.of(
            match(latestItemsCriteria(baseCriteria, bitemporalDate, active())),
            project()
                .and(PortfolioItem.Fields.valuationDataKey)
                .as(PortfolioItem.Fields.valuationDataKey));

    return mongoOperations
        .aggregateStream(newAggregation(operations), PortfolioItem.class, Document.class)
        .map(c -> c.getString(PortfolioItem.Fields.valuationDataKey))
        .collect(toSet());
  }

  public Set<String> resolvedValuationDataKeys(
      BitemporalDate bitemporalDate, Set<String> groupDataKeys) {
    var resolvedVDKs = new HashSet<String>();
    Iterables.partition(groupDataKeys, BATCH_SIZE)
        .forEach(
            keys -> {
              var criteria =
                  where(PortfolioItem.Fields.valuationDataKey)
                      .in(keys)
                      .and(PortfolioItem.Fields.portfolioArchivedAt)
                      .isNull();
              var result = findValuationDataKeys(criteria, bitemporalDate);
              resolvedVDKs.addAll(result);
            });

    return resolvedVDKs;
  }

  public Set<String> allValuationDataKeys(
      List<String> portfolioIds, BitemporalDate bitemporalDate) {
    var baseCriteria = portfolioCriteria(portfolioIds);
    return findValuationDataKeys(baseCriteria, bitemporalDate);
  }

  public ScrollableEntry<PortfolioItemWithKeyView> portfolioItemsWithKeyView(
      String portfolioId,
      BitemporalDate stateDate,
      TableFilter tableFilter,
      VersionedEntityFilter versionedEntityFilter,
      ScrollRequest scrollRequest,
      GroupRequest groupRequest) {
    var itemsCriteria =
        latestItemsCriteria(portfolioCriteria(portfolioId), stateDate, versionedEntityFilter);

    var operations = ImmutableList.<AggregationOperation>builder().add(match(itemsCriteria));

    operations.add(portfolioItemWithKeyViewProjection());
    operations.add(match(tableFilter.criteria(PortfolioItem.class, conversionService)));
    operations.add(match(groupRequest.criteria(PortfolioItem.class, conversionService)));
    operations.addAll(GroupRequestUtils.groupOperations(groupRequest));
    operations.addAll(
        new ScrollSortOperations(scrollRequest, DateRangeVersionedEntity.Fields.id)
            .withGroupRequest(groupRequest)
            .withLimitPlusOne()
            .build());
    var items =
        mongoOperations
            .aggregateAndReturn(PortfolioItemWithKeyView.class)
            .by(
                newAggregation(PortfolioItem.class, operations.build())
                    .withOptions(ALLOW_DISK_USE_BATCH_1000))
            .all()
            .getMappedResults();

    return ScrollableEntry.limitByPageSize(items, scrollRequest);
  }

  /** Projection from a PortfolioItem to a PortfolioItemWithKeyView */
  public ProjectionOperation portfolioItemWithKeyViewProjection() {
    return project(
            DateRangeVersionedEntity.Fields.entityId,
            VersionedTradeEntity.Fields.externalTradeId,
            DateRangeVersionedEntity.Fields.version,
            DateRangeVersionedEntity.Fields.validFrom,
            DateRangeVersionedEntity.Fields.comment,
            DateRangeVersionedEntity.Fields.recordFrom,
            DateRangeVersionedEntity.Fields.state,
            DateRangeVersionedEntity.Fields.modifiedAt,
            PortfolioItem.Fields.portfolioId,
            VersionedTradeEntity.Fields.description,
            VersionedTradeEntity.Fields.productType,
            VersionedTradeEntity.Fields.tradeDetails,
            VersionedTradeEntity.Fields.allocationTradeDetails,
            VersionedTradeEntity.Fields.clientMetrics,
            VersionedTradeEntity.Fields.onboardingDetails)
        .and(VersionedTradeEntity.Fields.customFields)
        .as(PortfolioItemWithKeyView.Fields.tradeInfoCustomFields)
        .and(VersionedTradeEntity.Fields.externalIdentifiers)
        .as(PortfolioItemWithKeyView.Fields.tradeInfoExternalIdentifiers)
        .and(propertyName(DateRangeVersionedEntity.Fields.modifiedBy, AuditUser.Fields.name))
        .as(PortfolioItemWithKeyView.Fields.modifiedBy)
        .and(valuationDataKeyExpression())
        .as(PortfolioItemWithKeyView.Fields.key);
  }

  private AggregationExpression valuationDataKeyExpression() {
    return StringOperators.valueOf(PortfolioItem.Fields.externalCompanyId)
        .concat("_")
        .concatValueOf(PortfolioItem.Fields.externalEntityId)
        .concat("_")
        .concatValueOf(PortfolioItem.Fields.externalPortfolioId)
        .concat("_")
        .concatValueOf(VersionedTradeEntity.Fields.externalTradeId);
  }

  public Stream<PortfolioItem> portfoliosItemsStream(
      @Nullable Set<String> portfolioIds, BitemporalDate stateDate) {
    return portfolioItemsStream(
        where(PortfolioItem.Fields.portfolioId)
            .in(convertCollectionTo(portfolioIds, ObjectId::new)),
        stateDate,
        active());
  }

  public Stream<PortfolioItem> portfoliosItemsStream(
      @Nullable Set<String> portfolioIds, BitemporalDate stateDate, Sort sort) {
    var portfolioIdCriteria =
        where(PortfolioItem.Fields.portfolioId)
            .in(convertCollectionTo(portfolioIds, ObjectId::new));
    var operations =
        List.of(
            match(latestItemsCriteria(portfolioIdCriteria, stateDate, active())),
            Aggregation.sort(sort));

    return mongoOperations
        .aggregateAndReturn(PortfolioItem.class)
        .by(newAggregation(PortfolioItem.class, operations).withOptions(ALLOW_DISK_USE_BATCH_1000))
        .stream();
  }

  public Stream<PortfolioItem> activePortfolioItemsStream(
      String portfolioId, BitemporalDate stateDate) {
    return portfolioItemsStream(portfolioCriteria(portfolioId), stateDate, active());
  }

  public Stream<PortfolioItem> activePortfolioItemsStream(
      Set<String> portfolioIds, BitemporalDate stateDate) {
    return portfolioItemsStream(portfolioCriteria(portfolioIds), stateDate, active());
  }

  public Stream<PortfolioItem> activePortfolioItemsStream(
      String portfolioId, BitemporalDate stateDate, List<ProductType> productTypes) {
    var criteria =
        portfolioCriteria(portfolioId)
            .and(VersionedTradeEntity.Fields.productType)
            .in(productTypes);
    return portfolioItemsStream(criteria, stateDate, active());
  }

  public boolean matchingActiveAndCompanyTreeExternalIds(
      BitemporalDate stateDate,
      String externalCompanyId,
      String externalEntityId,
      String externalPortfolioId,
      String externalTradeId) {

    Criteria baseCriteria =
        new Criteria()
            .and(PortfolioItem.Fields.externalCompanyId)
            .is(externalCompanyId)
            .and(PortfolioItem.Fields.externalEntityId)
            .is(externalEntityId)
            .and(PortfolioItem.Fields.externalPortfolioId)
            .is(externalPortfolioId)
            .and(VersionedTradeEntity.Fields.externalTradeId)
            .is(externalTradeId);

    return portfolioItemsStream(baseCriteria, stateDate, active()).findFirst().isPresent();
  }

  private Stream<PortfolioItem> portfolioItemsStream(
      Criteria baseCriteria, BitemporalDate stateDate, VersionedEntityFilter filter) {
    return mongoOperations
        .query(PortfolioItem.class)
        .matching(latestItemsCriteria(baseCriteria, stateDate, filter))
        .stream();
  }

  public List<PortfolioItem> conformityRequiredTrades(
      String portfolioId,
      BitemporalDate stateDate,
      boolean includeXplainCheck,
      boolean includeMarketCheck,
      boolean includeVendorCheck) {
    var conformity = new ArrayList<Criteria>();
    if (includeXplainCheck) {
      conformity.add(
          where(
                  joinPaths(
                      VersionedTradeEntity.Fields.onboardingDetails,
                      OnboardingDetails.Fields.xplainCostCheck))
              .is(true));
    }
    if (includeMarketCheck) {
      conformity.add(
          where(
                  joinPaths(
                      VersionedTradeEntity.Fields.onboardingDetails,
                      OnboardingDetails.Fields.marketConfCheck))
              .is(true));
    }
    if (includeVendorCheck) {
      conformity.add(
          where(
                  joinPaths(
                      VersionedTradeEntity.Fields.onboardingDetails,
                      OnboardingDetails.Fields.vendorCheck))
              .is(true));
    }
    if (conformity.isEmpty()) {
      return List.of();
    }
    var criteria =
        latestItemsCriteria(portfolioCriteria(portfolioId), stateDate, active())
            .orOperator(conformity);
    return mongoOperations.query(PortfolioItem.class).matching(criteria).all();
  }

  public Stream<PortfolioItem> portfolioItemsStream(
      String portfolioId, BitemporalDate stateDate, Sort sort, TableFilter tableFilter) {
    return itemStream(portfolioCriteria(portfolioId), stateDate, sort, tableFilter);
  }

  public Stream<PortfolioItem> allocationTradesStream(
      String referenceTradeId, BitemporalDate stateDate, Sort sort, TableFilter tableFilter) {
    return itemStream(refTradeCriteria(referenceTradeId, stateDate), stateDate, sort, tableFilter);
  }

  private Stream<PortfolioItem> itemStream(
      Criteria criteria, BitemporalDate stateDate, Sort sort, TableFilter tableFilter) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(latestItemsCriteria(criteria, stateDate, active())),
                match(tableFilter.criteria(PortfolioItem.class, conversionService)));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    }

    return mongoOperations
        .aggregateAndReturn(PortfolioItem.class)
        .by(
            newAggregation(PortfolioItem.class, operations.build())
                .withOptions(ALLOW_DISK_USE_BATCH_1000))
        .stream();
  }

  public List<String> counterparties(String portfolioId, BitemporalDate stateDate) {
    var operations =
        List.of(
            match(latestItemsCriteria(portfolioCriteria(portfolioId), stateDate, active())),
            match(
                where(
                        propertyName(
                            VersionedTradeEntity.Fields.tradeDetails,
                            TradeDetails.Fields.info,
                            TradeInfoDetails.Fields.counterParty))
                    .exists(true)),
            group(
                propertyName(
                    VersionedTradeEntity.Fields.tradeDetails,
                    TradeDetails.Fields.info,
                    TradeInfoDetails.Fields.counterParty)));

    return mongoOperations
        .aggregateAndReturn(Document.class)
        .by(newAggregation(PortfolioItem.class, operations))
        .stream()
        .map(d -> d.getString(UNDERSCORE_ID))
        .toList();
  }

  public List<PortfolioItem> getVersions(String portfolioId, String tradeEntityId) {
    return mongoOperations
        .query(PortfolioItem.class)
        .matching(
            portfolioCriteria(portfolioId)
                .and(DateRangeVersionedEntity.Fields.entityId)
                .is(tradeEntityId))
        .all();
  }

  public int portfolioTradesCount(String portfolioId, BitemporalDate stateDate) {
    return Math.toIntExact(
        mongoOperations
            .query(PortfolioItem.class)
            .matching(latestItemsCriteria(portfolioCriteria(portfolioId), stateDate, active()))
            .count());
  }

  /**
   * Counts the number of trades in the specified portfolios at the given state date. If there are
   * no trades in a portfolio, it will be missing from the map.
   */
  public Map<String, Integer> portfoliosTradesCount(
      Collection<String> portfolioIds, BitemporalDate stateDate) {
    TypedAggregation<PortfolioItem> aggregation =
        newAggregation(
            PortfolioItem.class,
            match(
                latestItemsCriteria(
                    where(PortfolioItem.Fields.portfolioId)
                        .in(convertCollectionTo(portfolioIds, ObjectId::new)),
                    stateDate,
                    active())),
            group(PortfolioItem.Fields.portfolioId).count().as(CountResult.Fields.count),
            project(CountResult.Fields.count)
                .and(ToString.toString(UNDERSCORE_ID_REF))
                .as(PortfolioItem.Fields.portfolioId));

    return mongoOperations
        .aggregateStream(aggregation, Document.class)
        .collect(
            toMap(
                doc -> doc.getString(PortfolioItem.Fields.portfolioId),
                doc -> doc.getInteger(CountResult.Fields.count)));
  }

  public ScrollableEntry<AllocationTradeListView> itemsByRefTradeId(
      String referenceTradeId,
      BitemporalDate stateDate,
      ScrollRequest scrollRequest,
      TableFilter tableFilter) {
    var baseOperations =
        List.of(
            match(
                latestItemsCriteria(
                    refTradeCriteria(referenceTradeId, stateDate), stateDate, active())),
            project()
                .and(VersionedTradeEntity.Fields.externalTradeId)
                .as(AllocationTradeListView.Fields.externalTradeId)
                .and(DateRangeVersionedEntity.Fields.entityId)
                .as(AllocationTradeListView.Fields.tradeId)
                .and(DateRangeVersionedEntity.Fields.version)
                .as(AllocationTradeListView.Fields.version)
                .and(DateRangeVersionedEntity.Fields.validFrom)
                .as(AllocationTradeListView.Fields.validFrom)
                .and(PortfolioItem.Fields.portfolioId)
                .as(AllocationTradeListView.Fields.portfolioId)
                .and(PortfolioItem.Fields.externalPortfolioId)
                .as(AllocationTradeListView.Fields.externalPortfolioId)
                .and(PortfolioItem.Fields.externalCompanyId)
                .as(AllocationTradeListView.Fields.externalCompanyId)
                .and(PortfolioItem.Fields.externalEntityId)
                .as(AllocationTradeListView.Fields.externalEntityId)
                .and(VersionedTradeEntity.Fields.clientMetrics)
                .as(AllocationTradeListView.Fields.resolvedClientMetrics)
                .and(VersionedTradeEntity.Fields.onboardingDetails)
                .as(AllocationTradeListView.Fields.onboardingDetails)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.allocationNotional))
                .as(AllocationTradeListView.Fields.allocationNotional)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.positionType))
                .as(AllocationTradeListView.Fields.positionType)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.tradeDetails,
                        TradeDetails.Fields.info,
                        TradeInfoDetails.Fields.counterParty))
                .as(AllocationTradeListView.Fields.resolvedTradeCounterparty)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.tradeDetails,
                        TradeDetails.Fields.info,
                        TradeInfoDetails.Fields.counterPartyType))
                .as(AllocationTradeListView.Fields.resolvedTradeCounterpartyType)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.tradeDetails,
                        TradeDetails.Fields.info,
                        TradeInfoDetails.Fields.tradeDate))
                .as(AllocationTradeListView.Fields.resolvedTradeDate)
                .and(VersionedTradeEntity.Fields.description)
                .as(AllocationTradeListView.Fields.resolvedDescription)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.clientMetrics))
                .as(AllocationTradeListView.Fields.clientMetrics)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.counterParty))
                .as(AllocationTradeListView.Fields.tradeCounterparty)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.counterPartyType))
                .as(AllocationTradeListView.Fields.tradeCounterpartyType)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.tradeDate))
                .as(AllocationTradeListView.Fields.tradeDate)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.description))
                .as(AllocationTradeListView.Fields.description)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.optionPosition))
                .as(AllocationTradeListView.Fields.optionPosition)
                .and(
                    joinPaths(
                        VersionedTradeEntity.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.protection))
                .as(AllocationTradeListView.Fields.protection),
            match(tableFilter.criteria(AllocationTradeListView.class, conversionService)));
    var countOperations =
        ImmutableList.<AggregationOperation>builder()
            .addAll(baseOperations)
            .add(count().as(COUNT_FIELD))
            .build();

    int count =
        mongoOperations
            .aggregateAndReturn(Document.class)
            .by(newAggregation(PortfolioItem.class, countOperations))
            .all()
            .getMappedResults()
            .stream()
            .map(v -> v.getInteger(COUNT_FIELD))
            .findFirst()
            .orElse(0);

    if (count == 0) {
      return ScrollableEntry.empty();
    }

    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder().addAll(baseOperations);
    operations.addAll(
        new ScrollSortOperations(scrollRequest, AllocationTradeListView.Fields.tradeId).build());

    var aggregation = newAggregation(PortfolioItem.class, operations.build());
    var items =
        mongoOperations.aggregate(aggregation, AllocationTradeListView.class).getMappedResults();

    return ScrollableEntry.of(items, scrollRequest, count);
  }

  public Long itemsByRefTradeIdCount(String referenceTradeId, BitemporalDate stateDate) {
    return mongoOperations
        .query(PortfolioItem.class)
        .matching(
            latestItemsCriteria(refTradeCriteria(referenceTradeId, stateDate), stateDate, active()))
        .count();
  }

  private Criteria refTradeCriteria(String referenceTradeId, BitemporalDate stateDate) {
    return new Criteria()
        .andOperator(
            where(
                    joinPaths(
                        TradeValue.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.referenceTradeId))
                .is(referenceTradeId),
            new Criteria()
                .orOperator(
                    where(PortfolioItem.Fields.portfolioArchivedAt).gt(stateDate.getRecordDate()),
                    where(PortfolioItem.Fields.portfolioArchivedAt).isNull()));
  }

  private Criteria portfolioCriteria(String portfolioId) {
    return where(PortfolioItem.Fields.portfolioId).is(new ObjectId(portfolioId));
  }

  private Criteria portfolioCriteria(Collection<String> portfolioIds) {
    return where(PortfolioItem.Fields.portfolioId)
        .in(portfolioIds.stream().map(ObjectId::new).toList());
  }
}
