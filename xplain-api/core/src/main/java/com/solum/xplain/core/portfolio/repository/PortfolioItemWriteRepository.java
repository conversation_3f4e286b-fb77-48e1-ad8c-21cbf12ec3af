package com.solum.xplain.core.portfolio.repository;

import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.EUTA;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.USNY;
import static com.solum.xplain.core.common.CollectionUtils.chunked;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static org.springframework.data.mongodb.core.BulkOperations.BulkMode.UNORDERED;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntityMongoOperations;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntityWriteRepository;
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import com.solum.xplain.core.common.versions.embedded.update.EntityUpdateOperations;
import com.solum.xplain.core.common.versions.embedded.update.EntityUpdateResult;
import com.solum.xplain.core.common.versions.embedded.update.ImportUpdatesResolver;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.PortfolioItemEntity;
import com.solum.xplain.core.portfolio.PortfolioItemEntityToViewConverterProvider;
import com.solum.xplain.core.portfolio.PortfolioItemUniqueKey;
import com.solum.xplain.core.portfolio.event.PortfolioArchived;
import com.solum.xplain.core.portfolio.event.PortfolioItemsStatesUpdated;
import com.solum.xplain.core.portfolio.event.PortfolioUpdated;
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.value.OnboardingVerificationResult;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.core.settings.value.ExceptionManagementSettingsProvider;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.Either;
import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Repository
@NullMarked
public class PortfolioItemWriteRepository
    implements EmbeddedVersionEntityWriteRepository<TradeValue, PortfolioItemEntity> {

  private final EmbeddedVersionEntityMongoOperations<TradeValue, PortfolioItemEntity, PortfolioItem>
      operations;
  private final ApplicationEventPublisher eventPublisher;
  private final MongoOperations mongoOperations;
  private final PortfolioItemEntityToViewConverterProvider portfolioItemToViewConverterProvider;
  private final ExceptionManagementSettingsProvider exceptionManagementSettingsProvider;

  private Clock clock = Clock.systemDefaultZone();

  void setClock(Clock clock) {
    this.clock = clock;
  }

  public PortfolioItemWriteRepository(
      ApplicationEventPublisher eventPublisher,
      MongoOperations mongoOperations,
      PortfolioItemEntityToViewConverterProvider portfolioItemToViewConverterProvider,
      AuditorAware<AuditUser> auditUserAuditorAware,
      ExceptionManagementSettingsProvider exceptionManagementSettingsProvider) {
    this.operations =
        new EmbeddedVersionEntityMongoOperations<>(
            PortfolioItemEntity.class,
            PortfolioItem.class,
            mongoOperations,
            portfolioItemToViewConverterProvider,
            new EntityUpdateOperations<>(auditUserAuditorAware),
            this::processUpdates);
    this.eventPublisher = eventPublisher;
    this.mongoOperations = mongoOperations;
    this.portfolioItemToViewConverterProvider = portfolioItemToViewConverterProvider;
    this.exceptionManagementSettingsProvider = exceptionManagementSettingsProvider;
  }

  private void processUpdates(
      Map<EntityUpdateResult<TradeValue, PortfolioItemEntity>, List<PortfolioItem>> items) {
    var portfolioItems =
        items.entrySet().stream()
            .filter(e -> e.getKey().isNew() || e.getKey().isRemoved())
            .flatMap(e -> e.getValue().stream())
            .collect(toSet());
    if (!portfolioItems.isEmpty()) {
      eventPublisher.publishEvent(PortfolioItemsStatesUpdated.newOf(portfolioItems));
    }
  }

  /**
   * Sets the onboarding period end date if the trade has onboarding details with a vendor
   * onboarding date.
   *
   * @param tradeValue the trade value to potentially update
   */
  private void setOnboardingPeriodEndDate(TradeValue tradeValue) {
    if (tradeValue.getOnboardingDetails() != null
        && tradeValue.getOnboardingDetails().getVendorOnboardingDate() != null) {
      LocalDate vendorOnboardingDate = tradeValue.getOnboardingDetails().getVendorOnboardingDate();
      Integer onboardingPeriod =
          exceptionManagementSettingsProvider.getOnboardingPeriod(
              BitemporalDate.newOf(
                  tradeValue.getAllocationTradeDetails() != null
                      ? tradeValue.getAllocationTradeDetails().getTradeDate()
                      : tradeValue.getTradeDetails().getInfo().getTradeDate()));

      if (onboardingPeriod != null) {
        LocalDate rawEndDate = vendorOnboardingDate.plusDays(onboardingPeriod).minusDays(1);

        BusinessDayAdjustment adjustment =
            BusinessDayAdjustment.of(FOLLOWING, USNY.combinedWith(EUTA));

        LocalDate adjustedEndDate = adjustment.adjust(rawEndDate, ReferenceData.standard());

        tradeValue.getOnboardingDetails().setOnboardingPeriodEndDate(adjustedEndDate);
      }
    }
  }

  public Stream<EntityForUpdate<TradeValue, PortfolioItemEntity>> streamEntitiesForUpdate(
      Collection<String> portfolioIds, LocalDate date) {
    if (portfolioIds.isEmpty()) {
      return Stream.empty();
    }
    var criteria = Criteria.where(PortfolioItemEntity.Fields.portfolioId).in(portfolioIds);
    return operations.streamValidEntitiesForUpdate(criteria, date);
  }

  public List<EntityForUpdate<TradeValue, PortfolioItemEntity>> entitiesForUpdate(
      String portfolioId, LocalDate date) {
    return entitiesForUpdate(portfolioId, date, false, null);
  }

  public List<EntityForUpdate<TradeValue, PortfolioItemEntity>> entitiesForUpdate(
      @Nullable String portfolioId,
      LocalDate date,
      boolean onlyAllocationTrades,
      @Nullable String referenceTradeId) {
    var criteria = new Criteria();
    if (isNotEmpty(portfolioId)) {
      criteria = criteria.and(PortfolioItemEntity.Fields.portfolioId).is(portfolioId);
    }
    if (isNotEmpty(referenceTradeId)) {
      criteria =
          criteria
              .and(
                  joinPaths(
                      PortfolioItemEntity.Fields.versions,
                      EmbeddedVersion.Fields.value,
                      TradeValue.Fields.allocationTradeDetails,
                      AllocationTradeDetails.Fields.referenceTradeId))
              .is(referenceTradeId);
    } else if (onlyAllocationTrades) {
      criteria =
          criteria
              .and(
                  joinPaths(
                      PortfolioItemEntity.Fields.versions,
                      EmbeddedVersion.Fields.value,
                      TradeValue.Fields.allocationTradeDetails,
                      AllocationTradeDetails.Fields.referenceTradeId))
              .exists(true);
    }
    var allTrades = operations.fetchValidEntitiesForUpdate(criteria, date);
    if (isNotEmpty(referenceTradeId)) {
      return allTrades.stream()
          .filter(t -> t.getVersion().getValue().getAllocationTradeDetails() != null)
          .filter(
              t ->
                  referenceTradeId.equals(
                      t.getVersion().getValue().getAllocationTradeDetails().getReferenceTradeId()))
          .toList();
    } else if (onlyAllocationTrades) {
      return allTrades.stream()
          .filter(t -> t.getVersion().getValue().getAllocationTradeDetails() != null)
          .filter(
              t ->
                  isNotEmpty(
                      t.getVersion().getValue().getAllocationTradeDetails().getReferenceTradeId()))
          .toList();
    } else {
      return allTrades;
    }
  }

  public Either<ErrorItem, EntityId> insert(String portfolioId, ParsableToTradeValue form) {
    var key = PortfolioItemUniqueKey.newOf(portfolioId, form.getExternalTradeId());
    var newDefault = new PortfolioItemEntity(key);
    return form.toTradeValue()
        .map(
            v -> {
              setOnboardingPeriodEndDate(v);
              return operations.insert(v, newDefault, form.getVersionForm());
            })
        .map(tradeId -> publishPortfolioUpdateEvent(portfolioId, tradeId));
  }

  public Either<ErrorItem, EntityId> update(
      String portfolioId, String id, LocalDate version, ParsableToTradeValue edit) {
    var criteria = entityCriteria(id, portfolioId);
    return edit.toTradeValue()
        .map(
            v -> {
              setOnboardingPeriodEndDate(v);
              return v;
            })
        .flatMap(v -> operations.update(criteria, version, v, edit.getVersionForm()))
        .map(tradeId -> publishPortfolioUpdateEvent(portfolioId, tradeId));
  }

  public Either<ErrorItem, EntityId> updateConformityChecks(
      String portfolioId,
      String id,
      LocalDate version,
      LocalDateTime recordDate,
      OnboardingVerificationResult verificationResult,
      NewVersionFormV2 versionForm) {
    var criteria = entityCriteria(id, portfolioId);
    return ofNullable(mongoOperations.findOne(query(criteria), PortfolioItemEntity.class))
        .map(
            e ->
                e.exactVersionAt(version)
                    .filter(v -> v.getRecordFrom().equals(recordDate))
                    .map(Either::<ErrorItem, EmbeddedVersion<TradeValue>>right)
                    .orElse(
                        Either.left(
                            OBJECT_NOT_FOUND.entity(
                                "Required latest trade version not found, trade has been updated "
                                    + e.getExternalTradeId())))
                    .map(EmbeddedVersion::getValue)
                    .map(
                        tradeValue -> updateOnboardingDetailsChecks(tradeValue, verificationResult))
                    .flatMap(v -> operations.update(criteria, version, v, versionForm)))
        .orElse(Either.left(OBJECT_NOT_FOUND.entity("Trade not found " + id)));
  }

  private TradeValue updateOnboardingDetailsChecks(
      TradeValue tradeValue, OnboardingVerificationResult verificationResult) {
    if (verificationResult.isXplainCheckVerified()) {
      tradeValue.getOnboardingDetails().setXplainCostCheck(false);
      tradeValue.getOnboardingDetails().setXplainCheckVerified(LocalDate.now());
    }
    if (verificationResult.isMarketCheckVerified()) {
      tradeValue.getOnboardingDetails().setMarketConfCheck(false);
      tradeValue.getOnboardingDetails().setMarketCheckVerified(LocalDate.now());
    }
    if (verificationResult.isVendorCheckVerified()) {
      tradeValue.getOnboardingDetails().setVendorCheck(false);
      tradeValue.getOnboardingDetails().setVendorCheckVerified(LocalDate.now());
    }
    return tradeValue;
  }

  public Either<ErrorItem, EntityId> archiveItem(
      String portfolioId, String id, LocalDate vd, ArchiveEntityForm form) {
    return operations
        .archive(entityCriteria(id, portfolioId), vd, form)
        .map(tradeId -> publishPortfolioUpdateEvent(portfolioId, tradeId));
  }

  public Either<ErrorItem, EntityId> deleteItem(String portfolioId, String id, LocalDate vd) {
    return operations
        .deleteItem(entityCriteria(id, portfolioId), vd)
        .map(tradeId -> publishPortfolioUpdateEvent(portfolioId, tradeId));
  }

  private EntityId publishPortfolioUpdateEvent(String portfolioId, EntityId tradeId) {
    eventPublisher.publishEvent(new PortfolioUpdated(portfolioId));
    return tradeId;
  }

  @Override
  public Integer updateFromImport(ImportUpdatesResolver<TradeValue, PortfolioItemEntity> update) {
    return operations.updateFromImport(update);
  }

  public void updateViewsAfterReferenceTradesUpdated(List<String> updatedReferenceTradeIds) {
    var affectedItems =
        operations.fetchValidEntities(
            where(
                    joinPaths(
                        PortfolioItemEntity.Fields.versions,
                        EmbeddedVersion.Fields.value,
                        TradeValue.Fields.allocationTradeDetails,
                        AllocationTradeDetails.Fields.referenceTradeId))
                .in(updatedReferenceTradeIds));

    if (affectedItems.isEmpty()) {
      return;
    }

    var allTradeValues =
        affectedItems.stream()
            .flatMap(e -> e.getVersions().stream())
            .map(EmbeddedVersion::getValue)
            .toList();

    var converter =
        portfolioItemToViewConverterProvider.provideForValues(allTradeValues, affectedItems);

    var regeneratedViews =
        affectedItems.stream().flatMap(p -> converter.generateViews(p).stream()).toList();

    var bulkOpsViews = mongoOperations.bulkOps(UNORDERED, PortfolioItem.class);
    bulkOpsViews.remove(
        query(
            where(DateRangeVersionedEntity.Fields.entityId)
                .in(
                    regeneratedViews.stream()
                        .map(DateRangeVersionedEntity::getEntityId)
                        .collect(toSet()))));
    bulkOpsViews.insert(regeneratedViews);
    bulkOpsViews.execute();
  }

  private Criteria entityCriteria(String entityId, String portfolioId) {
    return where(PortfolioItemEntity.Fields.id)
        .is(entityId)
        .and(PortfolioItemEntity.Fields.portfolioId)
        .is(portfolioId);
  }

  @EventListener
  public void onPortfolioArchived(PortfolioArchived event) {
    chunked(event.portfolioIds().stream()).forEach(this::updatePortfolioArchivedStatus);
  }

  private void updatePortfolioArchivedStatus(List<String> portfolioIds) {
    List<ObjectId> portfolioIdsAsObjectId = portfolioIds.stream().map(ObjectId::new).toList();
    // TODO(SXSD-9658): We are not deleting or archiving portfolio items, just updating the date
    // of archive.
    var now = LocalDateTime.now(clock);
    mongoOperations.updateMulti(
        query(where(PortfolioItem.Fields.portfolioId).in(portfolioIdsAsObjectId)),
        new Update().set(PortfolioItem.Fields.portfolioArchivedAt, now),
        PortfolioItem.class);
    mongoOperations.updateMulti(
        query(where(PortfolioItemEntity.Fields.portfolioId).in(portfolioIds)),
        new Update().set(PortfolioItemEntity.Fields.portfolioArchivedAt, now),
        PortfolioItemEntity.class);
  }
}
