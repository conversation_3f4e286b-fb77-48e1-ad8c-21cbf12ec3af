package com.solum.xplain.core.utils;

import com.google.common.cache.CacheLoader;
import java.util.Map;
import java.util.function.Function;
import org.jspecify.annotations.NullMarked;

/**
 * A {@link CacheLoader} that delegates to a single-key or bulk-key load delegate, and enforce the
 * presence of both delegates.
 */
@NullMarked
public final class XplainGuavaCacheLoader<KEY, VALUE> extends CacheLoader<KEY, VALUE> {
  private final Function<KEY, VALUE> singleLoadDelegate;
  private final Function<Iterable<? extends KEY>, Map<KEY, VALUE>> bulkLoadDelegate;

  public static <KEY, VALUE> XplainGuavaCacheLoader<KEY, VALUE> bulkCacheLoader(
      Function<KEY, VALUE> singleLoadDelegate,
      Function<Iterable<? extends KEY>, Map<KEY, VALUE>> bulkLoadDelegate) {
    return new XplainGuavaCacheLoader<>(singleLoadDelegate, bulkLoadDelegate);
  }

  private XplainGuavaCacheLoader(
      Function<KEY, VALUE> singleLoadDelegate,
      Function<Iterable<? extends KEY>, Map<KEY, VALUE>> bulkLoadDelegate) {
    this.singleLoadDelegate = singleLoadDelegate;
    this.bulkLoadDelegate = bulkLoadDelegate;
  }

  @Override
  public VALUE load(KEY key) throws Exception {
    return singleLoadDelegate.apply(key);
  }

  @Override
  public Map<KEY, VALUE> loadAll(Iterable<? extends KEY> keys) throws Exception {
    return bulkLoadDelegate.apply(keys);
  }
}
