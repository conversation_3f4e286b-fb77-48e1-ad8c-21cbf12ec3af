package com.solum.xplain.core.company


import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.CompanyIpvSettings
import com.solum.xplain.core.company.mapper.LegalEntityIpvSettingsMapper
import com.solum.xplain.core.company.repository.CompanyIpvSettingsRepository
import com.solum.xplain.core.company.repository.CompanyLegalEntityIpvSettingsRepository
import com.solum.xplain.core.company.repository.CompanyLegalEntityValuationSettingsRepository
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.company.repository.CompanyValuationSettingsRepository
import com.solum.xplain.core.company.value.CompanyIpvSettingsResolver
import com.solum.xplain.core.company.value.CompanyLegalEntityIpvSettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.time.LocalDate
import spock.lang.Specification

class CompanyPortfolioSettingsResolverTest extends Specification {

  static def STATE_DATE = BitemporalDate.newOf(LocalDate.now())

  def portfolioRepository = Mock(PortfolioRepository)
  def companyRepository = Mock(CompanyRepository)
  def valuationSettingsRepository = Mock(CompanyValuationSettingsRepository)
  def companyLegalEntityValuationSettingsRepository  = Mock(CompanyLegalEntityValuationSettingsRepository)
  def companyLegalEntityIpvSettingsRepository = Mock(CompanyLegalEntityIpvSettingsRepository)
  def ipvValuationSettingsRepository = Mock(CompanyIpvSettingsRepository)
  def legalEntityIpvSettingsMapper = Mock(LegalEntityIpvSettingsMapper)

  CompanyPortfolioSettingsResolver resolver = new CompanyPortfolioSettingsResolver(
  portfolioRepository,
  companyRepository,
  valuationSettingsRepository,
  companyLegalEntityValuationSettingsRepository,
  companyLegalEntityIpvSettingsRepository,
  ipvValuationSettingsRepository
  )

  def "should get single company portfolio IPV valuation settings"() {
    setup:
    1 * ipvValuationSettingsRepository.companyLegalEntityIpvSettingsResolver(
      "id",
      STATE_DATE
      ) >> new CompanyIpvSettingsResolver("id", new CompanyIpvSettings(), legalEntityIpvSettingsMapper, [])
    1 * portfolioRepository.activeCompanyPortfolios("id", STATE_DATE) >> []
    when:
    def result = resolver.companyPortfolioIpvValuationSettings(STATE_DATE, "id")
    then:
    result.isEmpty()
  }

  def "should return portfolio settings from repositories"() {
    given:
    def stateDate = BitemporalDate.newOf(LocalDate.now())
    def portfolioIds = ["P1", "P2"] as Set

    def portfolioView1 = Mock(PortfolioView) { getEntityId() >> "LE1" }
    def portfolioView2 = Mock(PortfolioView) { getEntityId() >> "LE2" }

    def valuationSettings1 = Mock(CompanyLegalEntityValuationSettingsView) {
      getEntityId() >> "LE1"
    }
    def valuationSettings2 = Mock(CompanyLegalEntityValuationSettingsView) {
      getEntityId() >> "LE2"
    }

    def ipvSettings1 = Mock(CompanyLegalEntityIpvSettingsView)
    def ipvSettings2 = Mock(CompanyLegalEntityIpvSettingsView)

    1 * portfolioRepository.activePortfolios(portfolioIds, stateDate) >> [portfolioView1, portfolioView2]

    1 * companyLegalEntityValuationSettingsRepository.getCompanyLegalEntitySettings(["LE1", "LE2"] as Set, stateDate) >>
    ["LE1": valuationSettings1, "LE2": valuationSettings2]
    1 * companyLegalEntityIpvSettingsRepository.getValuationSettingsView(["LE1", "LE2"] as Set, stateDate) >>
    ["LE1": ipvSettings1, "LE2": ipvSettings2]

    when:
    def result = resolver.portfoliosSettings(portfolioIds, stateDate)

    then:
    assert result != null
    result.size() == 2
    result*.getView() containsAll([portfolioView1, portfolioView2])
  }
}
