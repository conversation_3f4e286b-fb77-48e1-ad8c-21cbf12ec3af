package com.solum.xplain.core.curvegroup.conventions.index

import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_3M
import static com.solum.xplain.extensions.index.OffshoreIndices.BRL_CDI_OFFSHORE
import static com.solum.xplain.extensions.index.OffshoreIndices.CLP_TNA_OFFSHORE
import static com.solum.xplain.extensions.index.OffshoreIndices.CNY_REPO_OFFSHORE_1W
import static com.solum.xplain.extensions.index.OffshoreIndices.COP_OIBR_OFFSHORE
import static com.solum.xplain.extensions.index.OffshoreIndices.INR_OMIBOR_OFFSHORE
import static com.solum.xplain.extensions.index.OffshoreIndices.KRW_CD_OFFSHORE_13W
import static com.solum.xplain.extensions.index.OffshoreIndices.MXN_F_TIIE_OFFSHORE
import static com.solum.xplain.extensions.index.OffshoreIndices.MXN_TIIE_OFFSHORE_4W
import static com.solum.xplain.extensions.index.OffshoreIndices.MYR_KLIBOR_OFFSHORE_3M
import static com.solum.xplain.extensions.index.OffshoreIndices.MYR_MYOR_OFFSHORE
import static com.solum.xplain.extensions.index.OffshoreIndices.THB_THBFIX_OFFSHORE_6M
import static com.solum.xplain.extensions.index.OffshoreIndices.THB_THOR_OFFSHORE
import static com.solum.xplain.extensions.index.OffshoreIndices.TWD_TAIBOR_OFFSHORE_3M
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.AUD_FIXED_3M_BBSW_3M
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.AUD_BBSW_3M_QUARTERLY_FUTURE
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.AUD_BBSW_3M_QUARTERLY_FUTURE_SD
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T0
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T1
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T2
import static com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConventions.USD_SOFR_3M_TERM_OIS

import com.opengamma.strata.product.deposit.type.IborFixingDepositConvention
import spock.lang.Specification

class IrIndexCurveConventionsTest extends Specification {

  def "should return correct AUD 3M curve convention"() {
    setup:
    def expectedConventions = [
      IborFixingDepositConvention.of(AUD_BBSW_3M),
      AUD_DEPOSIT_T0,
      AUD_DEPOSIT_T1,
      AUD_DEPOSIT_T2,
      AUD_BBSW_3M_QUARTERLY_FUTURE,
      AUD_BBSW_3M_QUARTERLY_FUTURE_SD,
      AUD_FIXED_3M_BBSW_3M
    ]

    def convention = IrIndexCurveConventions.INDEX_CURVES.find(c -> c.name == "AUD 3M")

    expect:
    convention != null
    convention.name == "AUD 3M"
    convention.nodeConventions.containsAll(expectedConventions)
  }

  def "should return correct conventions for USD SOFR 3M curve"() {
    setup:
    def expectedConventions = [USD_SOFR_3M_TERM_OIS]

    def convention = IrIndexCurveConventions.INDEX_CURVES.find (c ->  c.name == "USD SOFR 3M")

    expect:
    convention != null
    convention.name == "USD SOFR 3M"
    convention.nodeConventions.containsAll(expectedConventions)
  }

  def "should return OFFSHORE_INDEX_CURVES"() {
    when:
    def conventions = IrIndexCurveConventions.OFFSHORE_INDEX_CURVES

    then:
    conventions.size() == 15

    ((IndexCurveConvention) conventions[2]).name == "CNY 1W Offshore"
    ((IndexCurveConvention) conventions[2]).index == CNY_REPO_OFFSHORE_1W
    ((IndexCurveConvention) conventions[4]).name == "INR OMIBOR Offshore"
    ((IndexCurveConvention) conventions[4]).index == INR_OMIBOR_OFFSHORE
    ((IndexCurveConvention) conventions[5]).name == "KRW 13W Offshore"
    ((IndexCurveConvention) conventions[5]).index == KRW_CD_OFFSHORE_13W
    ((IndexCurveConvention) conventions[9]).name == "MYR MYOR Offshore"
    ((IndexCurveConvention) conventions[9]).index == MYR_MYOR_OFFSHORE
    ((IndexCurveConvention) conventions[8]).name == "MYR 3M Offshore"
    ((IndexCurveConvention) conventions[8]).index == MYR_KLIBOR_OFFSHORE_3M
    ((IndexCurveConvention) conventions[7]).name == "MXN F TIIE Offshore"
    ((IndexCurveConvention) conventions[7]).index == MXN_F_TIIE_OFFSHORE
    ((IndexCurveConvention) conventions[6]).name == "MXN 4W Offshore"
    ((IndexCurveConvention) conventions[6]).index == MXN_TIIE_OFFSHORE_4W
    ((IndexCurveConvention) conventions[10]).name == "THB 6M Offshore"
    ((IndexCurveConvention) conventions[10]).index == THB_THBFIX_OFFSHORE_6M
    ((IndexCurveConvention) conventions[11]).name == "THB THOR Offshore"
    ((IndexCurveConvention) conventions[11]).index == THB_THOR_OFFSHORE
    ((IndexCurveConvention) conventions[12]).name == "TWD 3M Offshore"
    ((IndexCurveConvention) conventions[12]).index == TWD_TAIBOR_OFFSHORE_3M
    ((IndexCurveConvention) conventions[1]).name == "CLP TNA Offshore"
    ((IndexCurveConvention) conventions[1]).index == CLP_TNA_OFFSHORE
    ((IndexCurveConvention) conventions[3]).name == "COP OIBR Offshore"
    ((IndexCurveConvention) conventions[3]).index == COP_OIBR_OFFSHORE
    ((IndexCurveConvention) conventions[0]).name == "BRL CDI Offshore"
    ((IndexCurveConvention) conventions[0]).index == BRL_CDI_OFFSHORE
  }

  def "offshore named curves should match offshore index curves size"() {
    when:
    def conventions = IrIndexCurveConventions.OFFSHORE_INDEX_CURVES
    def namedCurves = IrIndexCurveConventions.INDEX_CURVES.stream()
    .filter(c -> c.name.contains("Offshore"))
    .count()

    then:
    conventions.size() == namedCurves
  }
}
