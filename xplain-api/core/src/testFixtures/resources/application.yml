debug: true

spring:
  cache:
    jcache:
      provider: com.hazelcast.cache.HazelcastMemberCachingProvider
  data:
    mongodb:
      uri: mongodb://${embedded.mongodb.host}:${embedded.mongodb.port}/${embedded.mongodb.database}
  hazelcast:
    config:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://somerandomauthserver.test.com/
  threads:
    virtual:
      enabled: true
app:
  simulation:
    max-days: 14
  exception-management:
    valuation-data:
      updated-values-tolerance: 1e-8
  retention:
    major-version-retention-months: 24
    minor-version-retention-months: 12
    workflow-retention-duration: P14D
  default:
    role: ROLE_ADMIN
    team: TEAM_DEFAULT
    trusted-client-ids:
  oauth2:
    inactivity-timeout-minutes: 30
    token-cache-duration-minutes: 480
    claims:
      username-claim: xplain/email
      roles-claim: xplain/roles
      teams-claim: xplain/teams
      principal-type-claim: gty
      principal-type-client-value: client-credentials
      client-id-claim: azp
  calculation:
    temp-dir: /tmp/xplain
    topics:
      valuations-topic: valuations
      metrics-topic: valuations-metrics
  cluster:
    event-topic: TEST_CLUSTER_EVENTS
  socket:
    timeout: 45000
    clean-up-rate: 15000
  evidence:
    cleanup-cron: "0 0 0 * * MON-FRI"
  xva-settings:
    sigma-step: 1
    time-gap: 0.083333
    hull-white-mean-reversion: 0.03
    time-end: 50
    simulations-for-xva: 1023
    simulations-for-pfe: 1023
    pfe-percentile: 0.95
  setup:
    environment-name: test
    reset:
      enabled: false
      aws-region: eu-west-2
      aws-lambda-function-name: arn:aws:lambda:\${app.setup.reset.aws-region}:575153581909:function:solum-xplain-setup-\${app.setup.environment-name}
      datasets:
        - XPLAIN_DEFAULT
        - LONDON
        - ALLCURVES
        - E2ETesting
      protected-collections:
        - accessLogEntry
        - auditEntry
        - auditEntryItem
        - role
        - team
  virtual-threads-monitor:
    enabled: true
  workflow:
    flush-frequency: PT1S
    cleanup-cron: "0 0 3 * * MON-FRI"
    cache: {}
