package com.solum.xplain.workflow.service

import static com.solum.xplain.workflow.value.ProcessDefinitionView.process
import static com.solum.xplain.workflow.value.ProcessFlowView.from
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.utils.async.VirtualThreadPinnedEventHandler
import com.solum.xplain.core.utils.async.VirtualThreadsMonitor
import com.solum.xplain.core.utils.async.VirtualThreadsMonitorProperties
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.event.ProcessDoneEvent
import com.solum.xplain.workflow.provider.WorkflowProvider
import com.solum.xplain.workflow.repository.DataModificationCommandQueue
import com.solum.xplain.workflow.service.engine.BlockingQueueWorkflowEngine
import com.solum.xplain.workflow.service.engine.ForkJoinWorkflowEngine
import com.solum.xplain.workflow.service.engine.MpmcAtomicArrayWorkflowEngine
import com.solum.xplain.workflow.service.engine.MpscThreadPoolWorkflowEngine
import com.solum.xplain.workflow.service.engine.VirtualThreadWorkflowEngine
import com.solum.xplain.workflow.service.engine.WorkflowEngine
import com.solum.xplain.workflow.value.MultiInstance
import com.solum.xplain.workflow.value.ProcessDefinitionView
import com.solum.xplain.workflow.value.SubprocessContextCreator
import com.solum.xplain.workflow.value.WorkflowStatus
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import java.util.function.Predicate
import java.util.stream.IntStream
import java.util.stream.Stream
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.MutablePropertyValues
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.event.EventListener
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import spock.lang.IgnoreIf
import spock.lang.PendingFeature
import spock.lang.Shared

@SpringBootTest
@ActiveProfiles("test")
@IgnoreIf(value = {
  env['CI'] == 'true'
}, reason = "Slow test, and only really useful for manual testing and viewing the results")
@TestPropertySource(
properties = [
  "app.data-grid.hazelcast.enabled=false",
  "app.data-grid.redis.enabled=true",
  "spring.data.redis.client-name=test",
  "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.hazelcast.HazelcastAutoConfiguration",
  "spring.cache.type=redis",
  "app.workflow.cache.wfProcessExecution.near-cache-ttl=PT1S",
  "app.workflow.cache.wfProcessExecution.shared-cache-ttl=PT2S",
  "app.workflow.cache.wfStepInstance.near-cache-ttl=PT1S",
  "app.workflow.cache.wfStepInstance.shared-cache-ttl=PT2S",
])
class WorkflowEnginePerformanceIntegrationTest extends IntegrationSpecification {
  static Logger log = LoggerFactory.getLogger(WorkflowEnginePerformanceIntegrationTest)

  @Autowired
  WorkflowService workflowService

  @Autowired
  MongoOperations mongoOperations

  @Autowired
  TestEventReceiver testEventReceiver

  @Autowired
  DataModificationCommandQueue commandQueue

  @Autowired
  ApplicationContext applicationContext

  @Shared
  static final ArrayList<Class<? extends WorkflowEngine>> ENGINES = [
    BlockingQueueWorkflowEngine,
    ForkJoinWorkflowEngine,
    MpscThreadPoolWorkflowEngine,
    MpmcAtomicArrayWorkflowEngine,
    VirtualThreadWorkflowEngine
  ]
  @Shared
  static final int SUBPROCESS_COUNT = 500
  @Shared
  static final int WARMUP_ITERATIONS = 2
  @Shared
  static final int TEST_ITERATIONS = 8

  @TestConfiguration
  static class TestConfig {
    @Bean
    TestEventReceiver testEventReceiver() {
      return new TestEventReceiver()
    }

    @Bean
    PerformanceTestProcessProvider performanceTestProcessProvider() {
      return new PerformanceTestProcessProvider()
    }
  }

  static class PerformanceTestProcessProvider implements WorkflowProvider {
    // Parent process with 2 service steps + parallel subprocess calls + final step
    def parentProcess = process("parentProcess")
    .startWith("start")
    .serviceStep("parentStep1", { StepStateOps<ParentState, ParentContext> ops ->
      ops.setOutcome(new MutablePropertyValues(Map.of("step1Done", true)))
    }, false)
    .serviceStep("parentStep2", { StepStateOps<ParentState, ParentContext> ops ->
      ops.setOutcome(new MutablePropertyValues(Map.of("step2Done", true)))
    }, false)
    .callStep("launchSubprocesses", "subprocess", MultiInstance.PARALLEL, new SubprocessContextCreator<ParentState, ParentContext, SubprocessState, SubprocessContext>() {
      @Override
      Stream<SubprocessContext> subprocessContext(ParentState parentState, ParentContext parentContext) {
        // Create a stream of subprocess contexts with unique IDs
        return IntStream.range(0, SUBPROCESS_COUNT)
          .mapToObj { i ->
            new SubprocessContext(id: i)
          }
      }

      @Override
      Class<SubprocessState> subprocessStateType() {
        return SubprocessState
      }
    })
    .serviceStep("parentComplete", { StepStateOps<ParentState, ParentContext> ops ->
      ops.setOutcome(new MutablePropertyValues(Map.of("allSubprocessesCompleted", true)))
    }, false)
    .flows(
    from("start").to("parentStep1"),
    from("parentStep1").to("parentStep2"),
    from("parentStep2").to("launchSubprocesses"),
    from("launchSubprocesses").to("parentComplete")
    )
    .build()

    // Subprocess with 5 service steps and conditional flow
    def subprocess = process("subprocess")
    .startWith("subStart")
    .serviceStep("subStep1", { StepStateOps<SubprocessState, SubprocessContext> ops ->
      // Simulate work with longer duration to trigger pinning detection
      Thread.sleep(25)
      ops.setOutcome(new MutablePropertyValues(Map.of("step1Result", "result_${ops.getContext().id}")))
    }, false)
    .serviceStep("subStep2", { StepStateOps<SubprocessState, SubprocessContext> ops ->
      Thread.sleep(25)
      ops.setOutcome(new MutablePropertyValues(Map.of("step2Result", "result_${ops.getContext().id}")))
    }, false)
    .serviceStep("subStep3", { StepStateOps<SubprocessState, SubprocessContext> ops ->
      Thread.sleep(25)
      def condition = (ops.getContext().id % 2 == 0)
      ops.setOutcome(new MutablePropertyValues(Map.of(
        "step3Result", "result_${ops.getContext().id}",
        "executeSteps4And5", condition
        )))
    }, false)
    .exclusiveGateway("flowDecision")
    .serviceStep("subStep4", { StepStateOps<SubprocessState, SubprocessContext> ops ->
      Thread.sleep(50)
      ops.setOutcome(new MutablePropertyValues(Map.of("step4Result", "result_${ops.getContext().id}")))
    }, false)
    .serviceStep("subStep5", { StepStateOps<SubprocessState, SubprocessContext> ops ->
      Thread.sleep(50)
      ops.setOutcome(new MutablePropertyValues(Map.of("step5Result", "result_${ops.getContext().id}")))
    }, false)
    .callStep("launchNestedSubprocesses", "nestedSubprocess", MultiInstance.PARALLEL, new SubprocessContextCreator<SubprocessState, SubprocessContext, NestedSubprocessState, NestedSubprocessContext>() {
      @Override
      Stream<NestedSubprocessContext> subprocessContext(SubprocessState parentState, SubprocessContext parentContext) {
        return Stream.of(new NestedSubprocessContext())
      }

      @Override
      Class<NestedSubprocessState> subprocessStateType() {
        return NestedSubprocessState
      }
    })
    .serviceStep("subEnd", { StepStateOps<SubprocessState, SubprocessContext> ops ->
      ops.setOutcome(new MutablePropertyValues(Map.of("completed", true)))
    }, false)
    .flows(
    from("subStart").to("subStep1"),
    from("subStep1").to("subStep2"),
    from("subStep2").to("subStep3"),
    from("subStep3").to("flowDecision"),
    from("flowDecision").when("state.executeSteps4And5 == true").to("subStep4"),
    from("flowDecision").when("state.executeSteps4And5 != true").to("launchNestedSubprocesses"),
    from("subStep4").to("subStep5"),
    from("subStep5").to("subEnd"),
    from("launchNestedSubprocesses").to("subEnd")
    )
    .build()

    def nestedSubprocess = process("nestedSubprocess")
    .startWith("nestedStart")
    .serviceStep("nestedEnd", { StepStateOps<NestedSubprocessState, NestedSubprocessContext> ops ->
      ops.setOutcome(new MutablePropertyValues(Map.of("completed", true)))
    }, false)
    .flows(
    from("nestedStart").to("nestedEnd")
    )
    .build()

    @Override
    Collection<ProcessDefinitionView> provideProcessDefinitions() {
      return [parentProcess, subprocess, nestedSubprocess]
    }
  }

  static class TestEventReceiver {
    List<ProcessDoneEvent> processDoneEvents = new CopyOnWriteArrayList<>()
    CountDownLatch processWait
    Predicate<ProcessDoneEvent> processFilter
    AtomicInteger completedSubprocesses = new AtomicInteger(0)
    String parentBusinessKey

    void configureExpectedProcess(String businessKey) {
      log.info("Configuring to wait for process: {}", businessKey)
      this.parentBusinessKey = businessKey
      processWait = new CountDownLatch(1)
      completedSubprocesses.set(0)
      processFilter = { ProcessDoneEvent e ->
        e.businessKey() == businessKey
      }

      // Check if already completed
      if (processDoneEvents.stream().anyMatch(processFilter)) {
        log.info("Process was already done when we started waiting")
        processWait.countDown()
      }
    }

    @EventListener
    void processDone(ProcessDoneEvent e) {
      processDoneEvents.add(e)
      log.debug("Process completed: {} (processId: {})", e.businessKey(), e.processId())

      // Count subprocesses - they are standalone processes created by the parent
      if (e.processId() == "subprocess") {
        def count = completedSubprocesses.incrementAndGet()
        log.debug("Subprocess completed: {} (total: {})", e.businessKey(), count)
      }

      if (processFilter?.test(e)) {
        log.info("Main process {} completed", e.businessKey())
        processWait.countDown()
      }
    }

    ProcessDoneEvent waitForProcess(long timeoutSeconds = 60) {
      log.info("Waiting for process to complete (timeout: {}s)", timeoutSeconds)
      int subprocessCount
      if (processWait.await(timeoutSeconds, TimeUnit.SECONDS)) {
        subprocessCount = completedSubprocesses.get()
        log.info("Process completed. Subprocesses completed: {}", subprocessCount)
      } else {
        subprocessCount = completedSubprocesses.get()
        log.error("Timed out waiting for process after {}s. Subprocesses completed: {}",
          timeoutSeconds, subprocessCount)
      }
      return processDoneEvents.stream().filter(processFilter).findFirst().orElse(null)
    }

    void reset() {
      processDoneEvents.clear()
      processWait = null
      processFilter = null
      completedSubprocesses.set(0)
    }
  }

  static class ParentContext implements Serializable {
    // Parent process context
  }

  static class ParentState implements Serializable {
    boolean step1Done
    boolean step2Done
    int subprocessesLaunched
    boolean allSubprocessesCompleted
  }

  static class SubprocessContext implements Serializable {
    int id
  }

  static class SubprocessState implements Serializable {
    String step1Result
    String step2Result
    String step3Result
    String step4Result
    String step5Result
    boolean executeSteps4And5
    boolean completed
  }

  static class NestedSubprocessContext implements Serializable {}

  static class NestedSubprocessState implements Serializable {
    boolean completed
  }

  def cleanup() {
    mongoOperations.remove(new Query(), ProcessExecution)
    mongoOperations.remove(new Query(), StepInstance)
    testEventReceiver.reset()
  }

  def "performance comparison between engines"() {
    given: "performance test configuration"
    Map<Class<? extends WorkflowEngine>, List<Long>> times = [:]

    when: "testing engine performance"
    ENGINES.forEach {
      times[it] = testEnginePerformance(it, it.getSimpleName())
    }

    then: "all engines complete successfully"
    times.values().every {
      it.size() == TEST_ITERATIONS
      it.every {
        it > 0
      }
    }

    and: "performance comparison is logged"
    Map<Class<? extends WorkflowEngine>, Long> avg = times.collectEntries { engine, t ->
      [engine, t.sum() / t.size()]
    }

    println "\n=== WORKFLOW ENGINE PERFORMANCE COMPARISON ==="
    println "Workflow: Parent (2 steps) + ${SUBPROCESS_COUNT} parallel subprocesses (5 steps each with conditions)"
    ENGINES.forEach {
      println ""
      println "${it.getSimpleName()}:"
      def timeStrings = times[it].collect { "${it}ms" }.join(', ')
      println "  Times: ${timeStrings}"
      println "  Average: ${String.format('%.0f', avg[it])}ms"
      println "  Min: ${times[it].min()}ms"
      println "  Max: ${times[it].max()}ms"
    }

    println ""
    def winner = ENGINES.sort(false) { avg[it] }.first
    println "Winner: ${winner}"
    ENGINES.forEach {
      if (it != winner) {
        def improvement = Math.abs(avg[it] - avg[winner]) / Math.max(avg[it], avg[winner]) * 100
        println "Compared to: ${it.getSimpleName()} (${winner.getSimpleName()} is ${String.format('%.1f', improvement)}% faster)"
      }
    }
    println "=============================================\n"

    // Verify both engines completed all subprocesses
    times.values().each {
      it.each {
        assert it < 60000
      }
    }
  }

  private List<Long> testEnginePerformance(Class<? extends WorkflowEngine> engineClass, String engineName) {
    println "Testing ${engineName}..."

    // Create engine-specific configuration
    def originalEngine = applicationContext.getBean(WorkflowEngine)
    def testEngine = applicationContext.getAutowireCapableBeanFactory().createBean(engineClass)

    try {
      // Replace the engine temporarily
      replaceWorkflowEngine(testEngine)

      // Warmup
      println "  Warmup (${WARMUP_ITERATIONS} iterations)..."
      WARMUP_ITERATIONS.times { iteration ->
        executeWorkflow("warmup_${iteration}")
        cleanup()
        println "    Warmup ${iteration + 1}/${WARMUP_ITERATIONS} complete"
      }

      // Actual test
      println "  Performance test (${TEST_ITERATIONS} iterations)..."
      def times = []
      TEST_ITERATIONS.times { iteration ->
        def startTime = System.currentTimeMillis()
        executeWorkflow("test_${iteration}")
        def duration = System.currentTimeMillis() - startTime
        times.add(duration)
        cleanup()
        println "    Test ${iteration + 1}/${TEST_ITERATIONS}: ${duration}ms"
      }

      return times
    } finally {
      // Restore original engine
      replaceWorkflowEngine(originalEngine)
      testEngine.shutdown()
    }
  }

  private void replaceWorkflowEngine(WorkflowEngine engine) {
    // Get the WorkflowService and replace its engine field via reflection
    def workflowServiceField = workflowService.class.getDeclaredField("workflowEngine")
    workflowServiceField.setAccessible(true)
    workflowServiceField.set(workflowService, engine)
  }

  private void executeWorkflow(String businessKey) {
    testEventReceiver.configureExpectedProcess(businessKey)

    // Start the parent process
    log.info("Starting parent process: {}", businessKey)
    workflowService.startProcess("parentProcess", businessKey, new ParentContext(), ParentState)

    // Wait a bit for initial steps to execute
    Thread.sleep(100)

    // Log current process state
    def allProcesses = mongoOperations.findAll(ProcessExecution)
    log.info("Found {} process executions total", allProcesses.size())
    allProcesses.each { proc ->
      log.info("Process: {} (businessKey: {}, status: {})", proc.processId, proc.businessKey, proc.status)
    }

    // Wait for completion
    log.info("Waiting for process completion...")
    def result = testEventReceiver.waitForProcess(60)
    if (!result) {
      log.error("Workflow ${businessKey} did not complete within timeout")
      log.error("Total process done events: {}", testEventReceiver.processDoneEvents.size())
      testEventReceiver.processDoneEvents.each { event ->
        log.error("Event: {} (processId: {})",
          event.businessKey(), event.processId())
      }
      throw new RuntimeException("Workflow ${businessKey} did not complete within timeout")
    }

    // Verify all subprocesses completed
    def completedCount = testEventReceiver.completedSubprocesses.get()
    log.info("Completed subprocesses: {}/{}", completedCount, SUBPROCESS_COUNT)
    if (completedCount != SUBPROCESS_COUNT) {
      log.error("Expected ${SUBPROCESS_COUNT} subprocesses, but ${completedCount} completed")
      log.error("All process done events:")
      testEventReceiver.processDoneEvents.each { event ->
        log.error("  Event: {} (processId: {})",
          event.businessKey(), event.processId())
      }
      throw new RuntimeException("Expected ${SUBPROCESS_COUNT} subprocesses, but ${completedCount} completed")
    }

    // Verify parent process completed
    commandQueue.awaitNextFlush()
    def parentExecution = mongoOperations.findOne(
      query(where(ProcessExecution.Fields.businessKey).is(businessKey)),
      ProcessExecution
      )
    assert parentExecution.status == WorkflowStatus.DONE
    log.info("Workflow ${businessKey} completed successfully")
  }

  def "should verify subprocess creation and tracking"() {
    given: "reduced subprocess count for testing"
    def testCount = 2

    and: "event receiver configured"
    testEventReceiver.configureExpectedProcess("debug_test")

    when: "starting parent process"
    log.info("=== DEBUGGING SUBPROCESS CREATION ===")
    workflowService.startProcess("parentProcess", "debug_test", new ParentContext(), ParentState)

    and: "waiting a moment for execution"
    Thread.sleep(5000)

    then: "check what processes were created"
    def allProcesses = mongoOperations.findAll(ProcessExecution)
    log.info("Total processes found: {}", allProcesses.size())
    allProcesses.each { proc ->
      log.info("Process: {} | businessKey: {} | rootBusinessKey: {} | status: {}",
        proc.processId, proc.businessKey, proc.rootBusinessKey, proc.status)
    }

    and: "check what events were received"
    log.info("Total process done events: {}", testEventReceiver.processDoneEvents.size())
    testEventReceiver.processDoneEvents.each { event ->
      log.info("Event: {} | processId: {}",
        event.businessKey(), event.processId())
    }

    true // Just verify we can create processes
  }

  def "should verify subprocess conditional flow"() {
    given: "a single subprocess execution"
    testEventReceiver.configureExpectedProcess("condition_test")

    when: "starting a subprocess with even ID (condition = true)"
    workflowService.startProcess("subprocess", "condition_test", new SubprocessContext(id: 2), SubprocessState)
    def result = testEventReceiver.waitForProcess(10)
    commandQueue.awaitNextFlush()

    then: "process completes successfully"
    result != null

    and: "all 5 steps plus gateway executed (steps 4 and 5 included)"
    def execution = mongoOperations.findOne(
      query(where(ProcessExecution.Fields.businessKey).is("condition_test")),
      ProcessExecution
      )
    execution.currentState.step1Result == "result_2"
    execution.currentState.step2Result == "result_2"
    execution.currentState.step3Result == "result_2"
    execution.currentState.step4Result == "result_2"  // Should exist due to condition
    execution.currentState.step5Result == "result_2"  // Should exist due to condition
    execution.currentState.executeSteps4And5 == true
    execution.currentState.completed == true
  }

  def "should verify subprocess conditional skip"() {
    given: "a single subprocess execution"
    testEventReceiver.configureExpectedProcess("skip_test")

    when: "starting a subprocess with odd ID (condition = false)"
    workflowService.startProcess("subprocess", "skip_test", new SubprocessContext(id: 3), SubprocessState)
    def result = testEventReceiver.waitForProcess(10)
    commandQueue.awaitNextFlush()

    then: "process completes successfully"
    result != null

    and: "only first 3 steps executed (steps 4 and 5 skipped)"
    def execution = mongoOperations.findOne(
      query(where(ProcessExecution.Fields.businessKey).is("skip_test")),
      ProcessExecution
      )
    execution.currentState.step1Result == "result_3"
    execution.currentState.step2Result == "result_3"
    execution.currentState.step3Result == "result_3"
    execution.currentState.step4Result == null  // Should be null due to condition
    execution.currentState.step5Result == null  // Should be null due to condition
    execution.currentState.executeSteps4And5 == false
    execution.currentState.completed == true
  }

  // Testing virtual thread pinning during workflow reliably identifies >800 pinning events during execution of a 500-subprocess workflow
  // After removing DataModificationCommandQueue.flush() synchronization, we only get around 20 events from com.google.common.cache.LocalCache$Segment.storeLoadedValue
  static PINNING_THRESHOLD = 10

  @PendingFeature(reason = "Awaiting remaining work in SXSD-10442 to reduce pinning")
  def "should detect virtual thread pinning with VirtualThreadWorkflowEngine"() {
    given: "virtual thread engine with normal workflow"
    def virtualEngine = applicationContext.getAutowireCapableBeanFactory().createBean(VirtualThreadWorkflowEngine)
    def originalEngine = applicationContext.getBean(WorkflowEngine)

    and: "JFR-based pinning detection"
    def pinningEvents = new CopyOnWriteArrayList<jdk.jfr.consumer.RecordedEvent>()

    // Create a custom pinning event handler
    def pinningHandler = new VirtualThreadPinnedEventHandler() {
        @Override
        void handle(jdk.jfr.consumer.RecordedEvent event) {
          pinningEvents.add(event)
        }
      }

    // Create and start monitor for the test with minimal threshold to catch any pinning
    def monitorProperties = new VirtualThreadsMonitorProperties(
      true, // enabled
      java.time.Duration.ofSeconds(30), // maxAge
      java.time.Duration.ofMillis(1), // very low threshold to catch any pinning
      new VirtualThreadsMonitorProperties.VirtualThreadsMonitorMetricsProperties(false, "test"),
      new VirtualThreadsMonitorProperties.VirtualThreadsMonitorLoggingProperties(false, org.slf4j.event.Level.INFO, 10)
      )
    def monitor = new VirtualThreadsMonitor(monitorProperties, [pinningHandler])
    monitor.start()

    when: "running normal workflow with virtual threads"
    try {
      replaceWorkflowEngine(virtualEngine)
      testEventReceiver.configureExpectedProcess("pinning_baseline_test")

      println "=== BASELINE PINNING DETECTION TEST ==="
      println "Running normal workflow (500 subprocesses) with VirtualThreadWorkflowEngine"
      println "This establishes baseline before AsyncCache implementation"

      workflowService.startProcess("parentProcess", "pinning_baseline_test", new ParentContext(), ParentState)

      // Wait for completion and cache operations
      testEventReceiver.waitForProcess(60)
      commandQueue.awaitNextFlush()

      // Wait for any delayed JFR events
      Thread.sleep(1000)
    } finally {
      // Restore original state
      monitor.stop()
      replaceWorkflowEngine(originalEngine)
      virtualEngine.shutdown()
    }

    then: "analyze baseline pinning"
    def pinningCount = pinningEvents.size()

    println "========== BASELINE PINNING RESULTS =========="
    println "Completed subprocesses: ${testEventReceiver.completedSubprocesses.get()}"
    println "Total pinning events: ${pinningCount}"

    if (pinningCount > 0) {
      println "PINNING DETECTED - This confirms issues identified in the plan"
      println "Key pinning locations:"

      // Group events by stack trace location for summary
      def locationCounts = [:]
      pinningEvents.each { event ->
        if (event.getStackTrace() && event.getStackTrace().getFrames().size() > 0) {
          def topFrame = event.getStackTrace().getFrames().findAll { !it.getMethod().getType().name.matches("^(java|jdk|sun).*") }[0]
          def location = "${topFrame.getMethod().getType().getName()}.${topFrame.getMethod().getName()}"
          locationCounts[location] = (locationCounts[location] ?: 0) + 1
        }
      }

      locationCounts.each { location, count ->
        println "  - ${location}: ${count} events"
      }

      // Show details for first few events
      println "\nFirst pinning events (sample):"
      pinningEvents.take(5).each { event ->
        def thread = event.getThread()?.getJavaName() ?: "<unknown>"
        def duration = event.getDuration()
        println "  Thread: ${thread}, Duration: ${duration.toMillis()}ms"
        if (event.getStackTrace()) {
          event.getStackTrace().getFrames().take(100).each { frame ->
            println "    at ${frame.getMethod().getType().getName()}.${frame.getMethod().getName()}():${frame.getLineNumber()}"
          }
        }
      }
    } else {
      println "NO PINNING DETECTED - Virtual threads executed without pinning"
      println "This suggests either:"
      println "  - No cache contention occurred in this run"
      println "  - Current cache usage doesn't trigger pinning conditions"
      println "  - JFR threshold may be too high"
    }

    println "===================================================\n"

    pinningCount < PINNING_THRESHOLD
  }
}
