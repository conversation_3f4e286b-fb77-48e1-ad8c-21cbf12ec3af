package com.solum.xplain.workflow;

import com.solum.xplain.core.CoreConfig;
import com.solum.xplain.shared.datagrid.LocalCacheReplica;
import com.solum.xplain.shared.datagrid.SharedCacheTtl;
import com.solum.xplain.workflow.WorkflowProperties.WorkflowDataCacheProperties;
import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.service.engine.ForkJoinWorkflowEngine;
import com.solum.xplain.workflow.service.engine.VirtualThreadWorkflowEngine;
import com.solum.xplain.workflow.service.engine.WorkflowEngine;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnThreading;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.thread.Threading;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.SpelCompilerMode;
import org.springframework.expression.spel.SpelParserConfiguration;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

@Configuration
@Import(CoreConfig.class)
@ComponentScan(
    excludeFilters = {
      @ComponentScan.Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
      @ComponentScan.Filter(
          type = FilterType.CUSTOM,
          classes = AutoConfigurationExcludeFilter.class)
    })
@EntityScan(basePackageClasses = {ProcessExecution.class})
@EnableConfigurationProperties({WorkflowProperties.class})
public class WorkflowConfig {
  public static final String WORKFLOW_SERVICE_EVALUATION_CONTEXT_BEAN_NAME =
      "workflowServiceEvaluationContext";
  @Autowired WorkflowProperties workflowProperties;

  @Bean
  SpelExpressionParser spelExpressionParser(SpelParserConfiguration spelParserConfiguration) {
    return new SpelExpressionParser(spelParserConfiguration);
  }

  @Bean
  SpelParserConfiguration spelParserConfiguration() {
    return new SpelParserConfiguration(SpelCompilerMode.IMMEDIATE, getClass().getClassLoader());
  }

  @Bean(defaultCandidate = false, name = WORKFLOW_SERVICE_EVALUATION_CONTEXT_BEAN_NAME)
  EvaluationContext evaluationContext(BeanFactory beanFactory) {
    StandardEvaluationContext standardEvaluationContext = new StandardEvaluationContext();
    standardEvaluationContext.setBeanResolver(new BeanFactoryResolver(beanFactory));
    return standardEvaluationContext;
  }

  @Bean
  LocalCacheReplica wfProcessExecutionLocalCacheReplica() {
    return localCacheReplica(ProcessExecution.PROCESS_EXECUTION_COLLECTION);
  }

  @Bean
  LocalCacheReplica wfStepInstanceLocalCacheReplica() {
    return localCacheReplica(StepInstance.STEP_INSTANCE_COLLECTION);
  }

  @Bean
  SharedCacheTtl wfProcessExecutionSharedCacheTtl() {
    return sharedCacheTtl(ProcessExecution.PROCESS_EXECUTION_COLLECTION);
  }

  @Bean
  SharedCacheTtl wfStepInstanceSharedCacheTtl() {
    return sharedCacheTtl(StepInstance.STEP_INSTANCE_COLLECTION);
  }

  @Bean
  SharedCacheTtl instrumentResultPreliminarySharedCacheTtl() {
    return sharedCacheTtl("instrumentResultPreliminary");
  }

  @Bean
  @ConditionalOnThreading(Threading.VIRTUAL)
  WorkflowEngine vtWorkflowEngine(SimpleAsyncTaskExecutor applicationTaskExecutor) {
    return new VirtualThreadWorkflowEngine(applicationTaskExecutor);
  }

  @Bean
  @ConditionalOnThreading(Threading.PLATFORM)
  WorkflowEngine ptWorkflowEngine() {
    return new ForkJoinWorkflowEngine();
  }

  private SharedCacheTtl sharedCacheTtl(String name) {
    WorkflowDataCacheProperties cacheProperties = workflowProperties.cacheProperties(name);
    return new SharedCacheTtl(name, cacheProperties.sharedCacheTtl());
  }

  private LocalCacheReplica localCacheReplica(String name) {
    WorkflowDataCacheProperties cacheProperties = workflowProperties.cacheProperties(name);
    return new LocalCacheReplica(
        name,
        cacheProperties.maxEntries(),
        cacheProperties.nearCacheTtl(),
        LocalCacheReplica.DEFAULT_IDLE_TTL,
        true);
  }
}
