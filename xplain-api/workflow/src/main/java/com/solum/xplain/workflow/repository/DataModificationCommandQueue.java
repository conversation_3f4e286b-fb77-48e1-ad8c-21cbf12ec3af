package com.solum.xplain.workflow.repository;

import static java.util.concurrent.TimeUnit.MILLISECONDS;

import java.util.Enumeration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Implements batching of database operations during workflow step execution, to minimise round
 * trips to the database and improve performance.
 *
 * <p>Multiple concurrent threads are permitted to append {@link DataModificationCommand
 * DataModificationCommands} to the queue and flushing the queued changes to the database are
 * managed to happen in parallel by using double buffering of {@link BulkOperations} instances.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataModificationCommandQueue {
  private final MongoOperations mongoOperations;
  private final WorkflowDataCacheService workflowDataCacheService;

  private final ConcurrentHashMap<Class<?>, BulkOperations> bulkOpsCache =
      new ConcurrentHashMap<>();
  private final AtomicReference<CountDownLatch> flushDoneLatch =
      new AtomicReference<>(new CountDownLatch(1));
  private final AtomicBoolean flushPending = new AtomicBoolean(false);
  private final ReentrantLock flushLock = new ReentrantLock();

  /**
   * Adds a command to the queue by allocating a {@link BulkOperations} instance for the affected
   * entity and {@link DataModificationCommand#apply(BulkOperations, Cache) applying} the command to
   * the instance. The call to {@code apply()} should also update the in-memory copy of the entity
   * and return it.
   *
   * <p>This method is thread-safe with flush operating safely in parallel.
   *
   * @param modification the modification to apply
   * @param <T> the entity type being affected by this command
   * @return the result of applying the modification
   */
  public <T> T append(DataModificationCommand<T> modification) {
    Class<? super T> entityClass = modification.getEntity();
    BulkOperations bulkOps = bulkOpsCache.computeIfAbsent(entityClass, this::newBulkOperations);
    Cache cache = workflowDataCacheService.getCache(entityClass);
    return modification.apply(bulkOps, cache);
  }

  /**
   * Flush pending database operations by executing all the queued {@link BulkOperations bulk
   * operations}. This should be called at significant lifecycle points (e.g. completion of a
   * process) to assist with database consistency.
   *
   * <p>Calling threads will be blocked if any other thread is already flushing.
   */
  public void flush() {
    flushLock.lock();
    try {
      Enumeration<Class<?>> allQueued = bulkOpsCache.keys();
      while (allQueued.hasMoreElements()) {
        Class<?> entity = allQueued.nextElement();
        BulkOperations next = bulkOpsCache.get(entity);
        next.execute();
      }
      var current = flushDoneLatch.getAndSet(new CountDownLatch(1));
      current.countDown();
      flushPending.set(false);
    } finally {
      flushLock.unlock();
    }
  }

  @Scheduled(fixedDelayString = "${app.workflow.flush-frequency}")
  public void autoFlush() {
    flush();
  }

  private BulkOperations newBulkOperations(Class<?> entity) {
    return new ReorderingBulkOperations(mongoOperations, entity);
  }

  /**
   * Waits for the next flush to happen. This allows a process step to depend on modifications from
   * previous steps in that process to be flushed, but without small flushes to happen
   * unnecessarily. If the flush does not happen within the set period, it will force a flush.
   */
  @SneakyThrows
  public void awaitNextFlush() {
    // if flush isn't pending then wait and trigger flush, otherwise just wait
    var flushPending = this.flushPending.getAndSet(true);
    if (flushPending) {
      flushDoneLatch.get().await();
    } else {
      if (!flushDoneLatch.get().await(5, MILLISECONDS)) {
        flush();
      }
    }
  }
}
