package com.solum.xplain.workflow.repository;

import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.mongodb.bulk.BulkWriteResult;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.BiConsumer;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndReplaceOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.query.UpdateDefinition;
import org.springframework.data.util.Pair;
import org.springframework.data.util.ReadWriteLock;
import org.springframework.data.util.TypeInformation;

/**
 * Collects entities to insert, coalesces updates to them in-memory if they are updated by ID, and
 * then also doesn't actually insert them if they are non-reportable StepInstance entities.
 * Operations are reordered as follows:
 *
 * <ol>
 *   <li>Inserts, and updates on inserted items
 *   <li>Any other updates including bulk
 *   <li>Removes are queued separately and executed last
 * </ol>
 *
 * <p>On execute, then inserts are done first (with non-reportable StepInstance entities filtered
 * out), then other updates if it exists, and finally the removes.
 *
 * <p>Instances of this class are intended to be long-lived and accessed by multiple threads. {@link
 * #execute()} can be called multiple times and the queued updates will be reset after each call so
 * that more operations can be added and executed.
 */
@RequiredArgsConstructor
@Slf4j
public class ReorderingBulkOperations implements BulkOperations {
  private final MongoOperations mongoOperations;
  private final Class<?> entityClass;

  /**
   * Make sure that any time we read and use the contents of one of these AtomicReferences, we use a
   * read lock. And whenever we write the reference, we use a write lock. Do not access anything
   * inside these references without a lock or you <em>will</em> get data inconsistencies later.
   */
  private final AtomicReference<ConcurrentLinkedQueue<Document>> insertQueue =
      new AtomicReference<>(new ConcurrentLinkedQueue<>());

  private final AtomicReference<ConcurrentHashMap<ObjectId, Update>> updateQueue =
      new AtomicReference<>(new ConcurrentHashMap<>());
  private final AtomicReference<ConcurrentLinkedQueue<Query>> removeQueue =
      new AtomicReference<>(new ConcurrentLinkedQueue<>());
  private final ReadWriteLock queueRefLock = ReadWriteLock.of(new ReentrantReadWriteLock());

  @Override
  public @NonNull BulkOperations insert(@NonNull Object document) {
    Document bsonDocument = toDocument(document);
    queueRefLock.readLock().executeWithoutResult(() -> insertQueue.get().add(bsonDocument));
    return this;
  }

  @Override
  public @NonNull BulkOperations insert(List<?> documents) {
    List<Document> bsonDocuments = documents.stream().map(this::toDocument).toList();

    queueRefLock.readLock().executeWithoutResult(() -> insertQueue.get().addAll(bsonDocuments));
    return this;
  }

  private Document toDocument(Object document) {
    if (document instanceof Document) {
      return (Document) document;
    }

    Document bsonDocument = new Document();
    mongoOperations.getConverter().write(document, bsonDocument);
    return bsonDocument;
  }

  @Override
  public @NonNull BulkOperations updateOne(Query query, Update update) {
    Document queryObject = query.getQueryObject();
    Document updateObject = update.getUpdateObject();

    boolean isUnderscore = queryObject.containsKey(UNDERSCORE_ID);
    boolean isSetOnly = updateObject.size() == 1 && updateObject.containsKey("$set");
    boolean isIdQueryOnly =
        queryObject.size() == 1 && (isUnderscore || queryObject.containsKey("id"));
    if (isIdQueryOnly && isSetOnly) {
      // If the query is by ID, we can coalesce the update into the insert.
      ObjectId id =
          (ObjectId) (isUnderscore ? queryObject.get(UNDERSCORE_ID) : queryObject.get("id"));

      boolean coalesced = coalesceWithExistingInsert(id, updateObject);

      if (!coalesced) {
        newOrCoalescedUpdate(id, update, updateObject);
      }
    } else {
      nonBulkUpdate(query, update, false);
    }

    return this;
  }

  /**
   * Attempt to add the changes in this update object to an existing insert of the same entity.
   *
   * @param id the ID of the entity
   * @param updateObject the update object with changed fields
   * @return true if an insert was found and could be updated
   */
  private Boolean coalesceWithExistingInsert(ObjectId id, Document updateObject) {
    return queueRefLock
        .readLock()
        .execute(
            () -> {
              for (Document existingInsert : insertQueue.get()) {
                if (existingInsert.get(UNDERSCORE_ID).equals(id)) {
                  if (log.isTraceEnabled()) {
                    log.trace(
                        "Coalescing update for {} with ID {} into insert",
                        entityClass.getSimpleName(),
                        id);
                  }
                  copyAndConvert(updateObject, existingInsert::put);
                  return true;
                }
              }
              return false;
            });
  }

  private void copyAndConvert(Document updateObject, BiConsumer<String, Object> consumer) {
    updateObject
        .get("$set", Document.class)
        .forEach(
            (k, v) -> {
              var typeInfo = TypeInformation.of(v.getClass());
              var document = mongoOperations.getConverter().convertToMongoType(v, typeInfo);
              // converter doesn't keep _class information. so we have to put it back manually.
              // It is, however, present in the original object.
              if (document instanceof Document doc) {
                doc.put("_class", v.getClass().getName());
              }
              consumer.accept(k, document);
            });
  }

  /**
   * Add the changes in this update object to an existing update of the same entity, or add this
   * update to the queue if there is no existing update.
   *
   * @param id the ID of the entity
   * @param update the update object with changed fields
   * @param updateObject the update object with changed fields, i.e. {@link Update#getUpdateObject()
   *     update.getUpdateObject()}. This is passed in separately to avoid re-serializing the update
   *     (creating a new {@code Document}) since we already have it at the time of calling.
   */
  private void newOrCoalescedUpdate(ObjectId id, Update update, Document updateObject) {
    queueRefLock
        .readLock()
        .executeWithoutResult(
            () -> {
              if (updateQueue
                      .get()
                      .compute(
                          id,
                          (key, existingUpdate) -> {
                            if (existingUpdate == null) {
                              // No existing update, use the new one
                              return update;
                            } else {
                              // Coalesce updates atomically - merge the new update into the
                              // existing one
                              updateObject.get("$set", Document.class).forEach(existingUpdate::set);
                              return existingUpdate;
                            }
                          })
                  != update) {
                if (log.isTraceEnabled()) {
                  log.trace(
                      "Coalescing update for {} with ID {} into existing update",
                      entityClass.getSimpleName(),
                      id);
                }
              }
            });
  }

  @Override
  public @NonNull BulkOperations updateOne(@NonNull Query query, @NonNull UpdateDefinition update) {
    if (update instanceof Update simpleUpdate) {
      return updateOne(query, simpleUpdate);
    } else {
      nonBulkUpdate(query, update, false);
    }
    return this;
  }

  @Override
  public @NonNull BulkOperations updateOne(List<Pair<Query, UpdateDefinition>> updates) {
    updates.forEach(
        queryUpdateDefinitionPair -> {
          Query query = queryUpdateDefinitionPair.getFirst();
          UpdateDefinition update = queryUpdateDefinitionPair.getSecond();
          updateOne(query, update);
        });
    return this;
  }

  @Override
  public @NonNull BulkOperations updateMulti(
      @NonNull Query query, @NonNull UpdateDefinition update) {
    nonBulkUpdate(query, update, true);
    return this;
  }

  @Override
  public @NonNull BulkOperations updateMulti(@NonNull List<Pair<Query, UpdateDefinition>> updates) {
    queueRefLock
        .writeLock()
        .executeWithoutResult(
            () -> {
              this.execute();
              BulkOperations bulkOps = mongoOperations.bulkOps(BulkMode.UNORDERED, entityClass);
              updates.forEach(
                  queryUpdateDefinitionPair -> {
                    Query query = queryUpdateDefinitionPair.getFirst();
                    UpdateDefinition update = queryUpdateDefinitionPair.getSecond();
                    bulkOps.updateMulti(query, update);
                  });
              bulkOps.execute();
            });
    return this;
  }

  private void nonBulkUpdate(Query query, UpdateDefinition update, boolean isMulti) {
    queueRefLock
        .writeLock()
        .executeWithoutResult(
            () -> {
              this.execute();
              if (isMulti) {
                mongoOperations.updateMulti(query, update, entityClass);
              } else {
                mongoOperations.updateFirst(query, update, entityClass);
              }
            });
  }

  @Override
  public @NonNull BulkOperations upsert(@NonNull Query query, @NonNull UpdateDefinition update) {
    throw new UnsupportedOperationException(
        "Upserts are not supported as they have poor performance.");
  }

  @Override
  public @NonNull BulkOperations upsert(@NonNull List<Pair<Query, Update>> updates) {
    throw new UnsupportedOperationException(
        "Upserts are not supported as they have poor performance.");
  }

  @Override
  public @NonNull BulkOperations remove(@NonNull Query remove) {
    queueRefLock.readLock().executeWithoutResult(() -> removeQueue.get().add(remove));
    return this;
  }

  @Override
  public @NonNull BulkOperations remove(@NonNull List<Query> removes) {
    queueRefLock.readLock().executeWithoutResult(() -> removeQueue.get().addAll(removes));
    return this;
  }

  @Override
  public @NonNull BulkOperations replaceOne(
      @NonNull Query query, @NonNull Object replacement, @NonNull FindAndReplaceOptions options) {
    throw new UnsupportedOperationException("Replace is not supported as it has poor performance.");
  }

  @Override
  public @NonNull BulkWriteResult execute() {
    return queueRefLock
        .writeLock()
        .execute(
            () -> {
              List<Document> inserts =
                  new ArrayList<>(insertQueue.getAndSet(new ConcurrentLinkedQueue<>()));
              Map<ObjectId, Update> updates =
                  new HashMap<>(updateQueue.getAndSet(new ConcurrentHashMap<>()));
              List<Query> removes =
                  new ArrayList<>(removeQueue.getAndSet(new ConcurrentLinkedQueue<>()));
              if (inserts.isEmpty() && updates.isEmpty() && removes.isEmpty())
                return BulkWriteResult.unacknowledged();

              if (log.isTraceEnabled()) {
                log.trace(
                    "Flushing data modification command queue for {} - {} inserts, {} updates, {} removes",
                    entityClass.getSimpleName(),
                    inserts.size(),
                    updates.size(),
                    removes.size());
              }
              BulkWriteResult bulkOpsResult = BulkWriteResult.unacknowledged();
              if (!inserts.isEmpty()) {
                mongoOperations.insert(inserts, entityClass);
              }
              if (!updates.isEmpty() || !removes.isEmpty()) {
                BulkOperations bulkOps = mongoOperations.bulkOps(BulkMode.UNORDERED, entityClass);
                updates.forEach(
                    (id, update) ->
                        bulkOps.updateOne(Query.query(where(UNDERSCORE_ID).is(id)), update));
                removes.forEach(bulkOps::remove);
                bulkOpsResult = bulkOps.execute();
              }

              log.trace(
                  "Flushed data modification command queue for {}", entityClass.getSimpleName());
              return bulkOpsResult;
            });
  }
}
