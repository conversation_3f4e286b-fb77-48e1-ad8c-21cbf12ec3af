package com.solum.xplain.workflow.service;

import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.repository.ProcessExecutionRepository;
import com.solum.xplain.workflow.repository.StepInstanceRepository;
import com.solum.xplain.workflow.value.WorkflowStatus;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/** Service providing workflow execution query capability to the rest of the application. */
@Service
@Slf4j
@RequiredArgsConstructor
@NullMarked
public class WorkflowQueryService {
  private final ProcessExecutionRepository processExecutionRepository;
  private final StepInstanceRepository stepInstanceRepository;

  /**
   * Finds a single step instance by ID, using an in-memory cache for efficiency.
   *
   * <p>The cache must be invalidated if any step instances are updated in a way which does not
   * reflect on an existing in-memory instance e.g. using {@link
   * com.solum.xplain.workflow.service.command.StepBulkSetOutcome}.
   *
   * @param id ID of the step instance
   * @return the step instance, or null if not found
   * @param <T> type representing the attached process state (must be serializable)
   */
  @Cacheable(cacheNames = StepInstance.STEP_INSTANCE_COLLECTION, unless = "#result == null")
  public <T extends Serializable> StepInstance<T> findStepInstanceWithCache(ObjectId id) {
    StepInstance<T> result = stepInstanceRepository.getById(id);
    if (result == null) {
      log.warn("Step instance with ID {} not found in database", id);
    }
    return result;
  }

  /**
   * Finds a single process execution by ID, using an in-memory cache for efficiency.
   *
   * @param id ID of the process execution
   * @return the process execution, or null if not found
   * @param <T> type representing the attached process state (must be serializable)
   * @param <C> type representing the immutable process context (must be serializable)
   */
  @Cacheable(cacheNames = ProcessExecution.PROCESS_EXECUTION_COLLECTION, unless = "#result == null")
  public <T extends Serializable, C extends Serializable>
      ProcessExecution<T, C> findProcessExecutionWithCache(ObjectId id) {
    return processExecutionRepository.findById(id);
  }

  /**
   * Return the root executions for a list of process business keys. The root execution is reached
   * by following {@link ProcessExecution#getParentExecutionId()} from each execution until one is
   * reached with no parent. The distinct set of root executions is then returned.
   *
   * @param businessKeys the business keys of any descendant from the root, or the root itself
   * @return the process executions corresponding to the distinct root of the execution trees for
   *     all IDs
   * @param <T> type representing the attached process state (must be serializable)
   */
  public <T extends Serializable, C extends Serializable>
      List<ProcessExecution<T, C>> getRootProcessExecutions(List<String> businessKeys) {
    return (List<ProcessExecution<T, C>>)
        (List) processExecutionRepository.getRootProcessExecutions(businessKeys);
  }

  /**
   * Return true if a process execution exists as specified.
   *
   * @param processDefinitionId the process definition ID
   * @param businessKey the business key
   * @return true if a process execution exists matching the process definition and business key
   */
  public boolean processExecutionExists(String processDefinitionId, String businessKey) {
    return processExecutionRepository.existsByProcessIdAndBusinessKey(
        processDefinitionId, businessKey);
  }

  /**
   * Return the reportable steps executed for a process in the order they were completed.
   *
   * @param processDefinitionId the process definition ID
   * @param businessKey the business key
   * @return the list of reportable steps in modified date order
   * @param <T> type representing the attached process state (must be serializable)
   */
  public <T extends Serializable> Stream<StepInstance<T>> getHistoricStepInstances(
      String processDefinitionId, String businessKey) {
    return stepInstanceRepository
        .findByProcessIdAndBusinessKeyAndReportableTrueOrderByModifiedAtAsc(
            processDefinitionId, businessKey);
  }

  /**
   * Return reportable steps for all processes matching the business keys and statuses supplied.
   *
   * @param processDefinitionId the process definition ID
   * @param businessKeys the business keys identifying the executions
   * @param statuses the statuses the steps must be in
   * @return the list of active reportable steps
   * @param <T> type representing the attached process state (must be serializable)
   */
  public <T extends Serializable> Stream<StepInstance<T>> getReportableStepInstances(
      String processDefinitionId, List<String> businessKeys, List<WorkflowStatus> statuses) {
    return stepInstanceRepository.findByProcessIdAndBusinessKeyInAndReportableTrueAndStatusIn(
        processDefinitionId, businessKeys, statuses);
  }
}
