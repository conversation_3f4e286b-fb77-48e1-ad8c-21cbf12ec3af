package com.solum.xplain.workflow.service.engine;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;

/**
 * A workflow engine implementation that uses a blocking queue and fixed thread pool to process work
 * items.
 *
 * <p>This implementation provides:
 *
 * <ul>
 *   <li>Fixed thread pool with one thread per CPU core
 *   <li>Bounded blocking queue with capacity 3x the number of CPU cores
 *   <li>Graceful shutdown with configurable timeout
 *   <li>Idle detection for synchronization with external systems
 *   <li>Exception isolation - failed work items don't crash workers
 * </ul>
 *
 * <p>The engine automatically starts worker threads upon construction and manages their lifecycle.
 * Worker threads are daemon threads to prevent JVM shutdown issues.
 *
 * @since 1.0
 * @see WorkflowEngine
 * @see WorkItem
 */
@Slf4j
public class BlockingQueueWorkflowEngine implements WorkflowEngine {
  private final BlockingQueue<WorkItem> blockingQueue;
  private final ExecutorService executorService;
  private final AtomicBoolean shutdown = new AtomicBoolean(false);
  private final AtomicInteger activeWorkers = new AtomicInteger(0);
  private volatile CountDownLatch idleLatch = new CountDownLatch(0);

  /**
   * Constructs a new BlockingQueueWorkflowEngine with default configuration.
   *
   * <p>Configuration:
   *
   * <ul>
   *   <li>Worker threads: equal to number of available processors
   *   <li>Queue capacity: 3 x the number of available processors
   *   <li>Thread names: "workflow-worker"
   *   <li>Daemon threads: true
   * </ul>
   *
   * <p>Worker threads are started immediately and begin polling for work items.
   */
  public BlockingQueueWorkflowEngine() {
    int queueSize = Runtime.getRuntime().availableProcessors() * 3;
    int workerCount = Runtime.getRuntime().availableProcessors();

    this.blockingQueue = new LinkedBlockingQueue<>(queueSize);
    this.executorService =
        Executors.newFixedThreadPool(
            workerCount,
            r -> {
              Thread t = new Thread(r, "workflow-worker");
              t.setContextClassLoader(Thread.currentThread().getContextClassLoader());
              t.setDaemon(true);
              return t;
            });

    // Start worker threads
    for (int i = 0; i < workerCount; i++) {
      executorService.submit(this::workerLoop);
    }

    log.info(
        "BlockingQueueWorkflowEngine started with {} workers and queue size {}",
        workerCount,
        queueSize);
  }

  /**
   * Submits a work item to the workflow engine for asynchronous processing.
   *
   * <p>The work item is added to the internal blocking queue where it will be picked up by the next
   * available worker thread. If the engine is shutdown or the work item is null, the work item is
   * rejected and a warning is logged.
   *
   * <p>Deadlock Prevention: If called from a worker thread and the queue is full, the work item is
   * executed directly in the current thread to prevent deadlock scenarios where worker threads
   * block waiting to add items to a full queue.
   *
   * <p>For external threads, this method blocks if the queue is full until space becomes available.
   *
   * @param workItem the work item to be processed, null items are ignored
   */
  @Override
  public void accept(WorkItem workItem) {
    if (workItem == null) {
      log.warn("Null work item submitted, ignoring");
      return;
    }

    if (shutdown.get()) {
      log.warn("Workflow engine is shutdown, rejecting work item");
      return;
    }

    try {
      // Check if current thread is a worker thread to prevent deadlock
      if (isWorkerThread()) {
        // Worker threads use non-blocking offer to prevent deadlock
        if (!blockingQueue.offer(workItem)) {
          // If queue is full, execute immediately in current worker thread
          // This prevents deadlock while maintaining work execution
          log.debug("Queue full, executing work item directly in worker thread");
          try {
            workItem.process(this);
          } catch (Exception e) {
            log.error("Error processing work item directly", e);
          }
          return;
        }
      } else {
        // External threads use blocking put
        blockingQueue.put(workItem);
      }
      updateIdleLatch();
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      log.warn("Interrupted while submitting work item", e);
    }
  }

  /**
   * Initiates a graceful shutdown of the workflow engine.
   *
   * <p>This method:
   *
   * <ol>
   *   <li>Stops accepting new work items
   *   <li>Allows currently executing work items to complete
   *   <li>Waits up to 30 seconds for graceful termination
   *   <li>Forces shutdown if graceful termination fails
   * </ol>
   *
   * <p>This method is idempotent - calling it multiple times has no additional effect. After
   * shutdown, the engine cannot be restarted.
   */
  @Override
  public void shutdown() {
    if (shutdown.compareAndSet(false, true)) {
      log.info("Shutting down BlockingQueueWorkflowEngine");

      // Stop accepting new work
      executorService.shutdown();

      try {
        // Wait for existing work to complete
        if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
          log.warn("Executor did not terminate gracefully, forcing shutdown");
          executorService.shutdownNow();
        }
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        executorService.shutdownNow();
      }

      log.info("BlockingQueueWorkflowEngine shutdown complete");
    }
  }

  /**
   * Waits until the workflow engine becomes idle or the timeout expires.
   *
   * <p>The engine is considered idle when:
   *
   * <ul>
   *   <li>The work queue is empty
   *   <li>No workers are currently processing work items
   * </ul>
   *
   * <p>If the engine is already shutdown, this method returns immediately with {@code true}.
   *
   * @param timeout the maximum time to wait for the engine to become idle
   * @param unit the time unit of the timeout argument
   * @return {@code true} if the engine became idle before the timeout, {@code false} if the timeout
   *     elapsed
   * @throws InterruptedException if the current thread is interrupted while waiting
   */
  @Override
  public boolean waitUntilIdle(long timeout, TimeUnit unit) {
    if (shutdown.get()) {
      return true;
    }

    try {
      return getCurrentIdleLatch().await(timeout, unit);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      return false;
    }
  }

  /**
   * Main loop for worker threads.
   *
   * <p>Each worker thread runs this loop, continuously polling for work items from the blocking
   * queue. When a work item is found, it's processed and the worker becomes available for more
   * work.
   *
   * <p>The loop terminates when the engine is shutdown or the thread is interrupted.
   */
  private void workerLoop() {
    while (!shutdown.get()) {
      try {
        WorkItem workItem = blockingQueue.poll(1, TimeUnit.SECONDS);
        if (workItem != null) {
          activeWorkers.incrementAndGet();
          try {
            workItem.process(this);
          } catch (Exception e) {
            log.error("Error processing work item", e);
          } finally {
            activeWorkers.decrementAndGet();
            updateIdleLatch();
          }
        }
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        break;
      }
    }
  }

  /**
   * Updates the idle latch based on current engine state.
   *
   * <p>This method is called whenever the engine state changes (work item added/completed). It
   * manages the CountDownLatch used by {@link #waitUntilIdle(long, TimeUnit)} to detect when the
   * engine becomes idle.
   *
   * <p>Thread-safe through synchronized blocks and atomic operations.
   */
  private void updateIdleLatch() {
    if (blockingQueue.isEmpty() && activeWorkers.get() == 0) {
      synchronized (this) {
        if (blockingQueue.isEmpty() && activeWorkers.get() == 0) {
          idleLatch.countDown();
        }
      }
    } else {
      synchronized (this) {
        if (!blockingQueue.isEmpty() || activeWorkers.get() > 0) {
          if (idleLatch.getCount() == 0) {
            idleLatch = new CountDownLatch(1);
          }
        }
      }
    }
  }

  /**
   * Gets the current idle latch in a thread-safe manner.
   *
   * @return the current CountDownLatch used for idle detection
   */
  private CountDownLatch getCurrentIdleLatch() {
    synchronized (this) {
      return idleLatch;
    }
  }

  /**
   * Checks if the current thread is a worker thread.
   *
   * @return true if the current thread is a workflow worker thread
   */
  private boolean isWorkerThread() {
    return Thread.currentThread().getName().startsWith("workflow-worker");
  }
}
