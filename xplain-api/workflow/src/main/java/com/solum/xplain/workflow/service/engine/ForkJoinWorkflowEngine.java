package com.solum.xplain.workflow.service.engine;

import com.solum.xplain.core.utils.async.AsyncUtils;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;

public class ForkJoinWorkflowEngine implements WorkflowEngine {
  private final ForkJoinPool stepExecutionService =
      AsyncUtils.createContextPreservingForkJoinPool();

  @Override
  public void accept(WorkItem workItem) {
    stepExecutionService.submit(() -> workItem.process(this));
  }

  @Override
  public void shutdown() {
    stepExecutionService.shutdown();
  }

  @Override
  public boolean waitUntilIdle(long timeout, TimeUnit unit) {
    return stepExecutionService.awaitQuiescence(timeout, unit);
  }
}
