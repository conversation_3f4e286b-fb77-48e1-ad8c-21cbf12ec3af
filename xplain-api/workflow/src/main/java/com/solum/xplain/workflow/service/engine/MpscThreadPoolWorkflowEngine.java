package com.solum.xplain.workflow.service.engine;

import io.netty.util.internal.shaded.org.jctools.queues.atomic.MpscAtomicArrayQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;

/**
 * A hybrid workflow engine that combines MPSC queue coordination with parallel execution.
 *
 * <p>This implementation provides:
 *
 * <ul>
 *   <li>Lock-free MPSC queue for fast work item submission
 *   <li>Single coordinator thread for minimal queue contention
 *   <li>Parallel execution via ForkJoinPool for high throughput
 *   <li>Best of both worlds: fast coordination + parallel processing
 * </ul>
 *
 * <p>The design uses a single coordinator thread to drain the MPSC queue and submit work items to a
 * ForkJoinPool for actual execution. This eliminates queue contention while maintaining parallel
 * processing capabilities.
 */
@Slf4j
public class MpscThreadPoolWorkflowEngine implements WorkflowEngine {
  private final MpscAtomicArrayQueue<WorkItem> workQueue;
  private final ExecutorService coordinatorExecutor;
  private final ForkJoinPool workExecutor;
  private final AtomicBoolean shutdown = new AtomicBoolean(false);
  private final AtomicInteger activeWork = new AtomicInteger(0);
  private volatile CountDownLatch idleLatch = new CountDownLatch(0);

  /** Constructs a new MpscThreadPoolWorkflowEngine with default configuration. */
  public MpscThreadPoolWorkflowEngine() {
    int queueCapacity = Runtime.getRuntime().availableProcessors() * 256;
    int parallelism = Runtime.getRuntime().availableProcessors();

    this.workQueue = new MpscAtomicArrayQueue<>(queueCapacity);
    this.coordinatorExecutor =
        java.util.concurrent.Executors.newSingleThreadExecutor(
            r -> {
              Thread t = new Thread(r, "mpsc-coordinator");
              t.setContextClassLoader(Thread.currentThread().getContextClassLoader());
              t.setDaemon(true);
              return t;
            });
    this.workExecutor =
        new ForkJoinPool(parallelism, ForkJoinPool.defaultForkJoinWorkerThreadFactory, null, true);

    // Start the coordinator thread
    coordinatorExecutor.submit(this::coordinatorLoop);

    log.info(
        "MpscThreadPoolWorkflowEngine started with queue capacity {} and {} worker threads",
        queueCapacity,
        parallelism);
  }

  @Override
  public void accept(WorkItem workItem) {
    if (workItem == null) {
      log.warn("Null work item submitted, ignoring");
      return;
    }

    if (shutdown.get()) {
      log.warn("Workflow engine is shutdown, rejecting work item");
      return;
    }

    // Check if current thread is coordinator to prevent deadlock
    if (isCoordinatorThread()) {
      // Coordinator thread: if queue is full, execute directly in work pool
      if (!workQueue.offer(workItem)) {
        log.debug("Queue full, submitting work item directly to thread pool");
        executeWorkItem(workItem);
        return;
      }
    } else {
      // External threads: use non-blocking offer
      if (!workQueue.offer(workItem)) {
        log.warn("Work queue is full, rejecting work item");
        return;
      }
    }

    updateIdleLatch();
  }

  @Override
  public void shutdown() {
    if (shutdown.compareAndSet(false, true)) {
      log.info("Shutting down MpscThreadPoolWorkflowEngine");

      coordinatorExecutor.shutdown();
      workExecutor.shutdown();

      try {
        if (!coordinatorExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
          coordinatorExecutor.shutdownNow();
        }
        if (!workExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
          workExecutor.shutdownNow();
        }
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        coordinatorExecutor.shutdownNow();
        workExecutor.shutdownNow();
      }

      log.info("MpscThreadPoolWorkflowEngine shutdown complete");
    }
  }

  @Override
  public boolean waitUntilIdle(long timeout, TimeUnit unit) {
    if (shutdown.get()) {
      return true;
    }

    try {
      return getCurrentIdleLatch().await(timeout, unit);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      return false;
    }
  }

  /** Coordinator loop that drains the MPSC queue and submits work to the thread pool. */
  private void coordinatorLoop() {
    while (!shutdown.get()) {
      try {
        boolean foundWork = false;

        // Drain queue in batches for efficiency
        for (int i = 0; i < 32 && !shutdown.get(); i++) {
          WorkItem workItem = workQueue.poll();
          if (workItem != null) {
            foundWork = true;
            executeWorkItem(workItem);
          } else {
            break;
          }
        }

        if (!foundWork) {
          // No work available, sleep briefly
          try {
            Thread.sleep(1);
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            break;
          }
        }

        updateIdleLatch();
      } catch (Exception e) {
        log.error("Unexpected error in coordinator loop", e);
      }
    }
  }

  /** Executes a work item in the thread pool. */
  private void executeWorkItem(WorkItem workItem) {
    activeWork.incrementAndGet();
    workExecutor.execute(
        () -> {
          try {
            workItem.process(this);
          } catch (Exception e) {
            log.error("Error processing work item", e);
          } finally {
            activeWork.decrementAndGet();
            updateIdleLatch();
          }
        });
  }

  private void updateIdleLatch() {
    boolean isIdle = workQueue.isEmpty() && activeWork.get() == 0;

    if (isIdle) {
      idleLatch.countDown();
    } else {
      synchronized (this) {
        if (idleLatch.getCount() == 0 && (!workQueue.isEmpty() || activeWork.get() > 0)) {
          idleLatch = new CountDownLatch(1);
        }
      }
    }
  }

  private CountDownLatch getCurrentIdleLatch() {
    synchronized (this) {
      return idleLatch;
    }
  }

  private boolean isCoordinatorThread() {
    return Thread.currentThread().getName().equals("mpsc-coordinator");
  }
}
