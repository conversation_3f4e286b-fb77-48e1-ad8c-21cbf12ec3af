package com.solum.xplain.workflow.service.engine;

import jakarta.annotation.PreDestroy;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.SimpleAsyncTaskExecutor;

/**
 * A Spring-managed workflow engine implementation that uses Java 21+ Virtual Threads for
 * ultra-lightweight concurrent work item processing.
 *
 * <p>This implementation provides:
 *
 * <ul>
 *   <li>Spring-managed task execution with virtual threads
 *   <li>No thread pool limits - each work item gets its own virtual thread immediately
 *   <li>Optimal for I/O-heavy workloads - virtual threads park efficiently during blocking
 *       operations
 *   <li>No queue contention - work items execute immediately without queuing
 *   <li>Automatic scaling - virtual threads are created and destroyed as needed
 *   <li>Exception isolation - failed work items don't affect other virtual threads
 * </ul>
 *
 * <p>Virtual threads are particularly well-suited for workflow processing where work items
 * frequently perform database operations, HTTP calls, or other blocking I/O operations. Each work
 * item runs in its own virtual thread without the overhead of traditional threads.
 *
 * <p><strong>Requires Java 21+</strong> - Virtual threads are a preview feature in Java 19-20 and
 * became stable in Java 21.
 *
 * <p><strong>Requires virtual threads enabled in Spring</strong> - {@code
 * spring.threads.virtual.enabled} set to {@code true}
 *
 * <p><strong>Idle tracking is disabled by default.</strong> Call {@link
 * #setIdleTrackingEnabled(boolean)} if necessary for testing.</strong>
 *
 * @since 1.0
 * @see WorkflowEngine
 * @see WorkItem
 */
@Slf4j
@RequiredArgsConstructor
public class VirtualThreadWorkflowEngine implements WorkflowEngine {
  private final SimpleAsyncTaskExecutor applicationTaskExecutor;
  private final AtomicBoolean shutdown = new AtomicBoolean(false);
  private final AtomicInteger activeWorkItems = new AtomicInteger(0);
  private volatile CountDownLatch idleLatch = new CountDownLatch(0);
  @Getter private boolean idleTrackingEnabled = false;

  /**
   * Submits a work item to the workflow engine for immediate virtual thread processing.
   *
   * <p>Each work item is immediately executed in its own virtual thread without queuing. If the
   * engine is shutdown or the work item is null, the work item is rejected and a warning is logged.
   *
   * <p>Virtual threads are extremely lightweight (2KB stack vs 2MB for platform threads) and can be
   * created in massive numbers without performance degradation. This eliminates the need for work
   * queues and thread pool management.
   *
   * <p>Benefits for workflow processing:
   *
   * <ul>
   *   <li>Zero queuing delay - work starts immediately
   *   <li>Optimal for blocking I/O - virtual threads park efficiently
   *   <li>No thread pool contention or limits
   *   <li>Automatic load balancing across CPU cores
   * </ul>
   *
   * @param workItem the work item to be processed, null items are ignored
   */
  @Override
  public void accept(WorkItem workItem) {
    if (workItem == null) {
      log.warn("Null work item submitted, ignoring");
      return;
    }

    if (shutdown.get()) {
      log.warn("Workflow engine is shutdown, rejecting work item");
      return;
    }

    // Submit work item to virtual thread executor - executes immediately
    if (idleTrackingEnabled) {
      activeWorkItems.incrementAndGet();
    }
    applicationTaskExecutor.execute(
        () -> {
          try {
            workItem.process(this);
          } catch (Exception e) {
            log.error("Error processing work item in virtual thread", e);
          } finally {
            if (idleTrackingEnabled) {
              activeWorkItems.decrementAndGet();
              updateIdleLatch();
            }
          }
        });

    if (idleTrackingEnabled) {
      updateIdleLatch();
    }
  }

  /**
   * Initiates a graceful shutdown of the workflow engine.
   *
   * <p>This method:
   *
   * <ol>
   *   <li>Stops accepting new work items
   * </ol>
   *
   * <p>This method is idempotent - calling it multiple times has no additional effect.
   */
  @Override
  @PreDestroy
  public void shutdown() {
    if (shutdown.compareAndSet(false, true)) {
      log.info("Shutting down VirtualThreadWorkflowEngine");
    }
  }

  /**
   * Waits until the workflow engine becomes idle or the timeout expires.
   *
   * <p>The engine is considered idle when no work items are currently being processed in virtual
   * threads. Since virtual threads have no queuing, this simply means all submitted work items have
   * completed.
   *
   * <p>If the engine is already shutdown, this method returns immediately with {@code true}.
   *
   * @param timeout the maximum time to wait for the engine to become idle
   * @param unit the time unit of the timeout argument
   * @return {@code true} if the engine became idle before the timeout, {@code false} if the timeout
   *     elapsed
   */
  @Override
  public boolean waitUntilIdle(long timeout, TimeUnit unit) {
    if (shutdown.get()) {
      return true;
    }

    if (!idleTrackingEnabled) {
      log.warn("waitUntilIdle() called but idle tracking is disabled - returning true immediately");
      return true;
    }

    try {
      return getCurrentIdleLatch().await(timeout, unit);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      return false;
    }
  }

  /**
   * Updates the idle latch based on current engine state.
   *
   * <p>This method is called whenever work items start or complete. Since virtual threads have no
   * queuing, the engine is idle when no work items are actively being processed.
   *
   * <p>Thread-safe through atomic operations and volatile fields.
   */
  private void updateIdleLatch() {
    boolean isIdle = activeWorkItems.get() == 0;

    if (isIdle) {
      // Engine is idle, release any waiting threads
      idleLatch.countDown();
    } else {
      // Engine is busy, reset latch if it was already released
      synchronized (this) {
        if (idleLatch.getCount() == 0 && activeWorkItems.get() > 0) {
          idleLatch = new CountDownLatch(1);
        }
      }
    }
  }

  /**
   * Gets the current idle latch in a thread-safe manner.
   *
   * @return the current CountDownLatch used for idle detection
   */
  private CountDownLatch getCurrentIdleLatch() {
    synchronized (this) {
      return idleLatch;
    }
  }

  /**
   * Enables idle tracking for testing purposes.
   *
   * <p>By default, idle tracking is disabled for optimal production performance. Tests that need to
   * use {@link #waitUntilIdle(long, TimeUnit)} or {@link #getActiveWorkItemCount()} should call
   * this method first.
   *
   * <p><strong>Note:</strong> This should only be called before submitting work items, as enabling
   * it mid-execution may produce inconsistent results.
   *
   * <p><strong>Virtual Thread Warning:</strong> Enabling idle tracking uses synchronized blocks
   * which cause virtual thread pinning in Java 21. This may severely impact performance and
   * scalability when using virtual threads with blocking operations. Only enable for testing
   * purposes and avoid in production with virtual threads. This limitation is resolved in Java 24+
   * via JEP 491.
   *
   * @param enabled true to enable idle tracking, false to disable (default)
   */
  public void setIdleTrackingEnabled(boolean enabled) {
    this.idleTrackingEnabled = enabled;
    if (enabled) {
      log.warn(
          "WARNING: Idle tracking enabled with virtual threads may cause thread pinning due to synchronized blocks. "
              + "This can severely impact performance. Only use for testing purposes.");
    } else {
      log.debug("Idle tracking disabled for production performance");
    }
  }

  /**
   * Gets the current number of active work items for monitoring purposes.
   *
   * <p><strong>Note:</strong> This method only returns accurate counts when idle tracking is
   * enabled via {@link #setIdleTrackingEnabled(boolean)}. When disabled, it returns 0.
   *
   * @return the number of work items currently being processed, or 0 if idle tracking is disabled
   */
  public int getActiveWorkItemCount() {
    if (!idleTrackingEnabled) {
      return 0;
    }
    return activeWorkItems.get();
  }
}
