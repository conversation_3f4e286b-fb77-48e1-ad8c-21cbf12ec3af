package com.solum.xplain.workflow.service

import static com.solum.xplain.workflow.service.SubprocessTracker.WF_SUBPROCESS_COUNT

import com.solum.xplain.shared.datagrid.AtomicCounter
import com.solum.xplain.shared.datagrid.DataGrid
import com.solum.xplain.workflow.entity.StepInstance
import java.util.concurrent.ExecutionException
import org.bson.types.ObjectId
import spock.lang.Specification
import spock.lang.Unroll

class SubprocessTrackerTest extends Specification {
  DataGrid dataGrid = Mock()
  SubprocessTracker tracker = new SubprocessTracker(dataGrid)

  def "should create tracker"() {
    given:
    def stepInstance = new StepInstance(id: new ObjectId(), executionId: new ObjectId(), businessKey: "parentKey")
    def counter = Mock(AtomicCounter)
    dataGrid.getAtomicCounter(WF_SUBPROCESS_COUNT + stepInstance.id.toHexString()) >> counter

    when:
    tracker.registerSubprocess(stepInstance) == 59

    then:
    1 * counter.incrementAndGet() >> 59L
  }

  def "should decrement tracker and check"() {
    given:
    def stepInstance = new StepInstance(id: new ObjectId(), executionId: new ObjectId(), businessKey: "parentKey")
    def counter = Mock(AtomicCounter)
    dataGrid.getAtomicCounter(WF_SUBPROCESS_COUNT + stepInstance.id.toHexString()) >> counter

    when:
    tracker.checkAfterSubprocessCompletes(stepInstance) == complete

    then:
    1 * counter.decrementAndGet() >> valueAfterDecrementing

    where:
    valueAfterDecrementing  || complete
    0L                      || true
    1L                      || false
    -1L                     || false
  }

  @Unroll
  def "should get registered count = #expectedCount when counter exists"() {
    given: "A step instance and a mocked counter"
    def stepInstance = new StepInstance(id: new ObjectId(), executionId: new ObjectId(), businessKey: "parentKeyGetCount")
    def counter = Mock(AtomicCounter)

    dataGrid.getAtomicCounter(WF_SUBPROCESS_COUNT + stepInstance.id.toHexString()) >> counter

    when: "The registered count is requested"
    long result = tracker.getRegisteredCount(stepInstance)

    then: "The current count is read from the counter without modification"
    1 * counter.get() >> expectedCount

    0 * counter.incrementAndGet(_)
    0 * counter.decrementAndGet(_)

    result == expectedCount

    where:
    expectedCount | _
    0L            | _
    5L            | _
    100L          | _
  }

  def "should return 0 when getting registered count fails due to ExecutionException"() {
    given: "A step instance and the datagrid call fails with ExecutionException"
    def stepInstance = new StepInstance(id: new ObjectId(), executionId: new ObjectId(), businessKey: "parentKeyGetCountFailEx")

    dataGrid.getAtomicCounter(WF_SUBPROCESS_COUNT + stepInstance.id.toHexString()) >> { throw new ExecutionException("Datagrid communication failed", null) }

    when: "The registered count is requested"
    long result = tracker.getRegisteredCount(stepInstance)

    then: "The method returns 0 as a safe default"
    1 * dataGrid.getAtomicCounter(WF_SUBPROCESS_COUNT + stepInstance.id.toHexString())
    result == 0L
  }

  def "should return 0 when getting registered count fails due to other Exception"() {
    given: "A step instance and the datagrid call fails with a generic Exception"
    def stepInstance = new StepInstance(id: new ObjectId(), executionId: new ObjectId(), businessKey: "parentKeyGetCountFailOther")

    dataGrid.getAtomicCounter(WF_SUBPROCESS_COUNT + stepInstance.id.toHexString()) >> { throw new RuntimeException("Unexpected internal error") }

    when: "The registered count is requested"
    long result = tracker.getRegisteredCount(stepInstance)

    then: "The method returns 0 as a safe default"
    1 * dataGrid.getAtomicCounter(WF_SUBPROCESS_COUNT + stepInstance.id.toHexString())
    result == 0L
  }
}
