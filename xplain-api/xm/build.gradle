plugins {
  id "java-test-fixtures"
}

dependencies {
  implementation project(":xplain-api:core")
  implementation project(":xplain-api:valuation-data")
  implementation project(":xplain-api:generic-product")
  implementation project(":xplain-api:trs")
  implementation project(":xplain-api:calculation")
  implementation project(":xplain-api:calibration")
  implementation project(":shared:spring-mongo")
  implementation project(":shared:data-grid")
  implementation project(":shared:strata-extension")
  implementation project(":shared:versions")
  implementation "jakarta.inject:jakarta.inject-api:2.0.1"
  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  runtimeOnly 'javax.cache:cache-api'
  implementation "com.opengamma.strata:strata-loader:${strataVersion}"
  implementation "com.opengamma.strata:strata-pricer:${strataVersion}"
  implementation "commons-beanutils:commons-beanutils:${commonsBeanUtilsVersion}"
  implementation "org.jeasy:easy-rules-core:${easyRulesVersion}"
  implementation "one.util:streamex:${streamExVersion}"
  implementation project(path: ':xplain-api:calculation')
  implementation project(path: ':xplain-api:calibration')
  implementation project(path: ':xplain-api:workflow')
  implementation "io.micrometer:micrometer-registry-prometheus"

  testImplementation(testFixtures(project(":shared:strata-extension")))
  testImplementation(testFixtures(project(":xplain-api:core")))
  testImplementation(testFixtures(project(':xplain-api:xm')))
  testImplementation(project(":xplain-api:calibration"))

  testFixturesImplementation "io.atlassian.fugue:fugue:${fugueVersion}"
  testFixturesImplementation "com.opengamma.strata:strata-loader:${strataVersion}"
  testFixturesImplementation "com.opengamma.strata:strata-pricer:${strataVersion}"
  testFixturesImplementation project(":xplain-api:core")
  testFixturesImplementation project(":xplain-api:valuation-data")
  testFixturesImplementation(testFixtures(project(":xplain-api:core")))
  testFixturesImplementation(testFixtures(project(":xplain-api:valuation-data")))
  testFixturesImplementation project(":xplain-api:generic-product")
  testFixturesImplementation project(":xplain-api:trs")
  testFixturesImplementation project(":xplain-api:workflow")
  testFixturesImplementation project(":shared:strata-extension")
  testFixturesImplementation project(":shared:utils")
  testFixturesImplementation project(":shared:versions")
  testFixturesImplementation "org.apache.groovy:groovy:${groovyJsonVersion}"
  testFixturesImplementation "org.mongodb:bson:5.5.1"
  testFixturesImplementation "org.spockframework:spock-core:${spockVersion}"
  testFixturesImplementation('org.springframework.boot:spring-boot-starter-test')

  integrationTestImplementation(testFixtures(project(":shared:utils")))
  integrationTestImplementation(testFixtures(project(":xplain-api:core")))
  integrationTestImplementation(testFixtures(project(":xplain-api:valuation-data")))
  integrationTestImplementation(testFixtures(project(':xplain-api:xm')))
  integrationTestImplementation project(":xplain-api:generic-product")
  integrationTestImplementation project(":xplain-api:calculation")
  integrationTestImplementation project(":xplain-api:calibration")
  integrationTestImplementation project(":xplain-api:trs")
  integrationTestImplementation project(":xplain-api:workflow")
  integrationTestImplementation project(":shared:strata-extension")
  integrationTestImplementation project(":shared:versions")
  integrationTestImplementation("com.google.guava:guava:${guavaVersion}")
  integrationTestImplementation "io.atlassian.fugue:fugue:${fugueVersion}"
  integrationTestImplementation "com.opengamma.strata:strata-loader:${strataVersion}"
  integrationTestImplementation "org.apache.commons:commons-lang3"
  integrationTestImplementation "org.apache.groovy:groovy:${groovyJsonVersion}"
  integrationTestImplementation "org.projectlombok:lombok:${lombokVersion}"
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
  integrationTestImplementation 'org.springframework.security:spring-security-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  integrationTestImplementation "org.spockframework:spock-core:${spockVersion}"
  integrationTestRuntimeOnly "org.junit.platform:junit-platform-launcher"
}

