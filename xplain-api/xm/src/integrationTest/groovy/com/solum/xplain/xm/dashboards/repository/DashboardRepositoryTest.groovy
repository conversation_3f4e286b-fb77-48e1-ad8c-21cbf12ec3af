package com.solum.xplain.xm.dashboards.repository

import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE_ANOTHER
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP_ANOTHER
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_ANOTHER
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_IPV_SETTINGS
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_IPV_SETTINGS_ANOTHER
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_NO_XPLAIN_PROVIDER_IPV_SETTINGS
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.marketDataBatchDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.marketDataDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.trsMarketDataBatchDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.trsMarketDataDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.valuationDataDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMarketDataBatchUpload
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdBatchPreliminaryClearing
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdBatchPreliminaryRun
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.marketDataBatchDashboardSteps
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.marketDataDashboardSteps
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.trsMarketDataBatchDashboardSteps
import static com.solum.xplain.xm.dashboards.enums.StepStatus.COMPLETED
import static com.solum.xplain.xm.dashboards.enums.StepStatus.IN_PROGRESS
import static com.solum.xplain.xm.dashboards.enums.StepStatus.NOT_STARTED

import com.solum.xplain.core.calculationapi.CalculationResultProvider
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.EntityReferenceView
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.daterange.DateRange
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.ipv.group.value.IpvDataGroupReference
import com.solum.xplain.core.ipv.group.value.IpvDataGroupReferenceView
import com.solum.xplain.core.sockets.events.EventType
import com.solum.xplain.shared.utils.filter.FilterOperation
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.entity.DashboardEntry
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup
import com.solum.xplain.xm.dashboards.entity.PortfolioDashboardSettings
import com.solum.xplain.xm.dashboards.entity.VdExceptionManagementSetup
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.DashboardType
import com.solum.xplain.xm.dashboards.events.DashboardDeletedEvent
import com.solum.xplain.xm.dashboards.forms.PricingSlotPortfolioForm
import com.solum.xplain.xm.dashboards.views.DashboardEntryMdBatchView
import com.solum.xplain.xm.dashboards.views.DashboardEntryView
import com.solum.xplain.xm.dashboards.views.DashboardListView
import com.solum.xplain.xm.dashboards.views.DashboardPortfolioListView
import com.solum.xplain.xm.dashboards.views.DateRangeView
import com.solum.xplain.xm.dashboards.views.MdExceptionManagementSetupView
import com.solum.xplain.xm.dashboards.views.PortfolioResultCountView
import com.solum.xplain.xm.dashboards.views.VdExceptionManagementSetupView
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultOverlay
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary
import com.solum.xplain.xm.excmngmt.processipv.data.IpvExceptionManagementResult
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTestRepository
import com.solum.xplain.xm.tasks.view.TaskCountsView
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import org.apache.commons.lang3.ObjectUtils
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Direction
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.util.ReflectionTestUtils

@SpringBootTest
@ActiveProfiles("test")
class DashboardRepositoryTest extends IntegrationSpecification implements DashboardRepositorySampleData {

  ApplicationEventPublisher publisher = Mock()

  @SpringBean
  IpvBreakTestRepository ipvBreakTestRepository = Mock()

  @Resource
  CalculationResultProvider calculationResultProvider // is a Mock

  @Resource
  MongoOperations operations

  @Resource
  DashboardRepository repository

  def creator = user("creatorId")

  def setup() {
    ReflectionTestUtils.setField(repository, "publisher", publisher)

    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), Dashboard)
    operations.remove(new Query(), DashboardEntryMdBatch)
    operations.remove(new Query(), DashboardEntryMd)
    operations.remove(new Query(), IpvExceptionManagementResult)
    operations.remove(new Query(), IpvTradeResultOverlay)
    operations.remove(new Query(), ExceptionManagementResult)
    operations.remove(new Query(), InstrumentResultPreliminary)
    operations.remove(new Query(), InstrumentResultOverlay)
    operations.remove(new Query(), ProcessExecution)
    operations.remove(new Query(), StepInstance)
  }

  def "should create dashboard"() {
    setup:
    def dashboard = new Dashboard()

    when:
    def result = repository.createDashboard(dashboard)

    then:
    1 * publisher.publishEvent({ it.eventType == EventType.DASHBOARD_CREATED && it.data == dashboard.id })

    result.isRight()
    def loaded = operations.findAll(Dashboard)
    loaded.size() == 1
    loaded[0].id == result.getOrNull().id
  }

  def "should get all dashboards"() {
    setup:
    // MARKET DATA BATCH dashboard
    def mdBatchDashboard = operations.insert(marketDataBatchDashboard())
    operations.insertAll(marketDataBatchDashboardSteps(mdBatchDashboard.id))

    // TRS MARKET DATA BATCH dashboard
    def trsMdBatchDashboard = operations.insert(trsMarketDataBatchDashboard())
    operations.insertAll(trsMarketDataBatchDashboardSteps(trsMdBatchDashboard.id))

    // MARKET DATA dashboard
    def mdDashboard = operations.insert(marketDataDashboard())
    operations.insertAll(marketDataDashboardSteps(mdDashboard.id))

    // VALUATION DATA dashboard
    def vdDashboard = operations.insert(valuationDataDashboard())

    when:
    def result = repository.getDashboards(
      TableFilter.emptyTableFilter(),
      ScrollRequest.of(0, 10, Sort.by("_id")),
      LocalDateTime.of(2021, 05, 01, 0, 0),
      LocalDate.of(2021, 05, 05)
      )

    then:
    with (result) {
      content.size() == 4
      assertDashboard(content[0], mdDashboard)
      assertDashboard(content[1], mdBatchDashboard)
      assertDashboard(content[2], trsMdBatchDashboard)
      assertDashboard(content[3], vdDashboard)
    }
  }

  def "should get dashboards based on #fromDate and #stateDate"() {
    setup:
    operations.insert(marketDataBatchDashboard(LocalDateTime.now(), DateRange.newOf(LocalDate.of(2022, 04, 30), LocalDate.of(2022, 06, 02))))
    operations.insert(marketDataDashboard(LocalDateTime.now(), DateRange.newOf(DASHBOARD_DATE, DASHBOARD_DATE_ANOTHER)))
    operations.insert(valuationDataDashboard(PORTFOLIO_NO_XPLAIN_PROVIDER_IPV_SETTINGS, LocalDateTime.now(), DateRange.newOf(LocalDate.of(2022, 04, 29), LocalDate.of(2022, 06, 02)) ))

    expect:
    repository.getDashboards(
      TableFilter.emptyTableFilter(),
      ScrollRequest.of(0, 10, Sort.by("_id")),
      fromDate, stateDate).content.size() == expectedResultsCount
    where:
    fromDate                                |   stateDate                    | expectedResultsCount
    LocalDateTime.of(2022, 04, 25, 0, 0)    | LocalDate.of(2022, 04, 30)     | 2                        // marketDataBatchDashboard(), valuationDataDashboard()
    LocalDateTime.of(2022, 04, 26, 0, 0)    | LocalDate.of(2022, 04, 29)     | 1                        // marketDataBatchDashboard()
    null                                    | LocalDate.of(2022, 04, 25)     | 3                        // marketDataBatchDashboard(), valuationDataDashboard(), // marketDataDashboard()
  }

  def "should get dashboards filtered by date"() {
    setup:
    operations.insert(marketDataBatchDashboard())
    operations.insert(marketDataDashboard())
    operations.insert(valuationDataDashboard())
    operations.insert(trsMarketDataBatchDashboard())

    expect:
    repository.getDashboards(
      new TableFilter([
        new SimpleFilterClause(
        "dateRange",
        FilterOperation.IN_RANGE,
        filterValue.toString())
      ]),
      ScrollRequest.of(0, 10),
      LocalDateTime.of(2021, 05, 01,0,0), LocalDate.of(2021, 05, 06)).content.size() == expectedResultsCount

    where:
    filterValue                | expectedResultsCount
    DASHBOARD_DATE             | 4
    DASHBOARD_DATE.plusDays(1) | 1
    DASHBOARD_DATE_ANOTHER     | 1
  }

  def "should get existing dashboard either by id"() {
    setup:
    def dashboard = operations.insert(marketDataDashboard())
    0 * publisher.publishEvent(_)

    expect:
    repository.dashboard(dashboard.id).isRight()
  }

  def "should re-attempt cleanup if trying to get missing existing dashboard by id"() {
    setup:
    def dashboardId = ObjectId.get().toString()

    when:
    def result = repository.dashboard(dashboardId)

    then:
    result.isLeft()
    1 * publisher.publishEvent({ DashboardDeletedEvent e -> e.dashboardId == dashboardId} )
  }

  def "should get MARKET_DATA_BATCH dashboard"() {
    setup:
    def dashboard = marketDataBatchDashboard()
    operations.insert(dashboard)

    when: "partial steps"
    def entry1 = dashboardStepMarketDataBatchUpload()
    def entry2 = dashboardStepMdBatchPreliminaryRun()
    operations.insertAll([entry1, entry2])

    def partialStepsResult = repository.getDashboard(dashboard.id, { -> [] })

    then:
    partialStepsResult.isRight()
    with(partialStepsResult.getOrNull()) {
      id == dashboard.id
      type == dashboard.type
      assertDateRange(dateRange, dashboard.dateRange)
      mdBatchEntries.size() == 3
      assertMdBatchEntry(mdBatchEntries[0], entry1, null)
      assertMdBatchEntry(mdBatchEntries[1], entry2, null)
      mdBatchEntries[2] == DashboardEntryMdBatchView.PLACEHOLDER_VIEWS[2]
      mdEntries.size() == 0
      vdEntries.size() == 0
    }

    when: "all steps"
    def entry3 = dashboardStepMdBatchPreliminaryClearing()
    operations.insertAll([entry3])

    def allStepsResult = repository.getDashboard(dashboard.id, { -> [] })

    then:
    allStepsResult.isRight()
    with(allStepsResult.getOrNull()) {
      id == dashboard.id
      type == dashboard.type
      assertDateRange(dateRange, dashboard.dateRange)
      mdBatchEntries.size() == 3
      assertMdBatchEntry(mdBatchEntries[0], entry1, null)
      assertMdBatchEntry(mdBatchEntries[1], entry2, null)
      assertMdBatchEntry(mdBatchEntries[2], entry3, null)
      mdEntries.size() == 0
      vdEntries.size() == 0
    }
  }

  def "should load portfolio previous dashboard id"() {
    setup:
    def result0 = new Dashboard(
      id: "id0",
      startedAt: LocalDateTime.now(),
      dateRange: DateRange.newOf(LocalDate.parse("2020-10-10")),
      vdExceptionManagementSetup: new VdExceptionManagementSetup(
      VdExceptionManagementPortfolioFilter.empty(),
      [portfolioSettings("pId")],
      )
      )

    def result1 = new Dashboard(
      id: "id1",
      finishedAt: LocalDateTime.now(),
      dateRange: DateRange.newOf(LocalDate.parse("2020-10-10")),
      vdExceptionManagementSetup: new VdExceptionManagementSetup(
      VdExceptionManagementPortfolioFilter.empty(),
      [portfolioSettings("pId")],
      )
      )
    def result2 = new Dashboard(
      id: "id2",
      finishedAt: LocalDateTime.now(),
      dateRange: DateRange.newOf(LocalDate.parse("2020-10-11")),
      vdExceptionManagementSetup: new VdExceptionManagementSetup(
      VdExceptionManagementPortfolioFilter.empty(),
      [portfolioSettings("pId2"), portfolioSettings("pId3")],
      )
      )
    def result3 = new Dashboard(
      id: "id3",
      finishedAt: LocalDateTime.now(),
      dateRange: DateRange.newOf(LocalDate.parse("2020-10-12")),
      vdExceptionManagementSetup: new VdExceptionManagementSetup(
      VdExceptionManagementPortfolioFilter.empty(),
      [portfolioSettings("pId2")],
      )
      )
    operations.insertAll([result0, result1, result2, result3])

    when:
    def dashboardId = repository.previousPortfolioDashboardId(portfolioId, valuationDate)

    then:
    dashboardId.map { i -> i.id } == expectedId

    where:
    portfolioId | valuationDate                 | expectedId
    "pId"       | LocalDate.parse("2020-10-12") | Optional.of("id1")
    "pId2"      | LocalDate.parse("2020-10-12") | Optional.of("id2")
    "pId3"      | LocalDate.parse("2020-10-12") | Optional.of("id2")
    "pId3"      | LocalDate.parse("2020-10-10") | Optional.empty()
    "pId2"      | LocalDate.parse("2020-10-12") | Optional.of("id2")
    "pId2"      | LocalDate.parse("2020-10-10") | Optional.empty()
    "pId3"      | LocalDate.parse("2020-10-12") | Optional.of("id2")
    "pId2"      | LocalDate.parse("2020-10-15") | Optional.of("id3")
  }

  def portfolioSettings(String portfolioId) {
    return new PortfolioDashboardSettings(
      portfolio: EntityReference.newOf(portfolioId, portfolioId),
      ipvDataGroupProducts: []
      )
  }

  def "should get previously-started legacy VD dashboard with no steps"() {
    setup:
    def dashboard = valuationDataDashboard().tap {
      startedAt = LocalDateTime.now()
    }
    operations.insert(dashboard)

    when: "no steps"
    def result = repository.getDashboard(dashboard.id, { -> [] })

    then:
    result.isRight()
    with(result.getOrNull()) {
      id == dashboard.id
      type == dashboard.type
      assertMdSetup(mdExceptionManagementSetup, dashboard.mdExceptionManagementSetup)
      assertVdSetup(vdExceptionManagementSetup, dashboard.vdExceptionManagementSetup)
      assertDateRange(dateRange, dashboard.dateRange)
      mdBatchEntries.size() == 0
      mdEntries.size() == 0
      vdEntries.size() == 0
    }
  }

  def "should get unstarted VD dashboard with placeholder steps"() {
    setup:
    def dashboard = valuationDataDashboard(PORTFOLIO_NO_XPLAIN_PROVIDER_IPV_SETTINGS, null)
    operations.insert(dashboard)

    when: "no steps"
    def result = repository.getDashboard(dashboard.id, { -> [] })

    then:
    1 * ipvBreakTestRepository.shouldSkipPhase(IpvExceptionManagementPhase.OVERLAY_2) >> true
    result.isRight()
    with(result.getOrNull()) {
      id == dashboard.id
      type == dashboard.type
      assertMdSetup(mdExceptionManagementSetup, dashboard.mdExceptionManagementSetup)
      assertVdSetup(vdExceptionManagementSetup, dashboard.vdExceptionManagementSetup)
      assertDateRange(dateRange, dashboard.dateRange)
      mdBatchEntries.size() == 0
      mdEntries.size() == 0
      vdEntries.size() == 3
      with(vdEntries[0]) {
        step == DashboardStep.TRADE_DATA_UPLOAD
        status == NOT_STARTED
      }
      with(vdEntries[1]) {
        step == DashboardStep.OPV_VALUATIONS
        status == NOT_STARTED
      }
      with(vdEntries[2]) {
        step == DashboardStep.IPV_OVERLAY_CLEARING
        status == NOT_STARTED
      }
    }
  }

  def "should get unstarted MD dashboard with placeholder steps"() {
    setup:
    def dashboard = marketDataDashboard()
    operations.insert(dashboard)

    when: "no steps"
    def result = repository.getDashboard(dashboard.id, { -> [] })

    then:
    result.isRight()
    with(result.getOrNull()) {
      id == dashboard.id
      type == dashboard.type
      assertMdSetup(mdExceptionManagementSetup, dashboard.mdExceptionManagementSetup)
      assertVdSetup(vdExceptionManagementSetup, dashboard.vdExceptionManagementSetup)
      assertDateRange(dateRange, dashboard.dateRange)
      mdBatchEntries.size() == 0
      mdEntries.size() == 5
      vdEntries.size() == 0
      // TODO: SXSD-9015: legacy market data dashboard - remove _RUN steps
      mdEntries*.step == [
        DashboardStep.MARKET_DATA_UPLOAD,
        DashboardStep.MD_PRELIMINARY_RUN,
        DashboardStep.MD_PRELIMINARY_CLEARING,
        DashboardStep.MD_OVERLAY_RUN,
        DashboardStep.MD_OVERLAY_CLEARING
      ]
      mdEntries*.status == [
        NOT_STARTED,
        NOT_STARTED,
        NOT_STARTED,
        NOT_STARTED,
        NOT_STARTED
      ]
    }
  }

  def "should get in-progress VD workflow dashboard with placeholder"() {
    setup:
    def dashboard = valuationDataDashboard()
    operations.insert(dashboard)
    def processExecution = new ProcessExecution(
      processId: 'vdDashboard',
      businessKey: 'urn:dashboard:' + dashboard.getId(),
      rootBusinessKey: 'urn:dashboard:' + dashboard.getId(),
      status: WorkflowStatus.ACTIVE
      )
    operations.insert(processExecution)
    def findTrades = new StepInstance(
      executionId: processExecution.id,
      processId: processExecution.processId,
      businessKey: processExecution.businessKey,
      rootBusinessKey: processExecution.rootBusinessKey,
      status: WorkflowStatus.ACTIVE,
      stepId: 'findTrades',
      reportable: true,
      startedAt: LocalDateTime.now()
      )
    operations.insert(findTrades)

    when:
    def result = repository.getDashboard(dashboard.id, { -> [] })

    then:
    1 * ipvBreakTestRepository.shouldSkipPhase(IpvExceptionManagementPhase.OVERLAY_2) >> skipPhase2
    result.isRight()
    with (result.getOrNull()) {
      id == dashboard.id
      type == dashboard.type
      vdEntries.size() == expectedSize
      with(vdEntries[0]) {
        step == DashboardStep.TRADE_DATA_UPLOAD
        status == IN_PROGRESS
      }
      with(vdEntries[1]) {
        step == DashboardStep.OPV_VALUATIONS
        status == NOT_STARTED
      }
      with(vdEntries[2]) {
        step == DashboardStep.IPV_OVERLAY_CLEARING
        status == NOT_STARTED
      }
      if (expectedSize > 3) {
        with(vdEntries[3]) {
          assert step == DashboardStep.IPV_OVERLAY_CLEARING_2
          assert status == NOT_STARTED
        }
      }
    }

    where:
    skipPhase2 || expectedSize
    true       || 3
    false      || 4
  }

  def "should get in-progress MD workflow dashboard with placeholder"() {
    setup:
    operations.insertAll([
      marketDataDashboard(),
      mdXmProcessExecution(),
      findInstrumentsStepInstance()
    ])

    when:
    def result = repository.getDashboard(MD_DASHBOARD_ID, { -> [] })

    then:
    result.isRight()
    with (result.getOrNull()) {
      id == MD_DASHBOARD_ID
      type == DashboardType.MARKET_DATA
      mdEntries.size() == 3
      mdEntries*.step == [
        DashboardStep.MARKET_DATA_UPLOAD,
        DashboardStep.MD_PRELIMINARY_CLEARING,
        DashboardStep.MD_OVERLAY_CLEARING
      ]
      mdEntries*.status == [IN_PROGRESS, NOT_STARTED, NOT_STARTED]
    }
  }

  def "should get in-progress MD workflow dashboard with placeholder and skipped preliminary"() {
    setup:
    operations.insertAll([
      marketDataDashboard(),
      mdXmProcessExecution(),
      mdXmOverlayProcessExecution(),
      findInstrumentsStepInstance().tap {
        it.status = WorkflowStatus.DONE
      }
    ])

    when:
    def result = repository.getDashboard(MD_DASHBOARD_ID, { -> [] })

    then:
    result.isRight()
    with (result.getOrNull()) {
      id == MD_DASHBOARD_ID
      type == DashboardType.MARKET_DATA
      mdEntries.size() == 3
      mdEntries*.step == [
        DashboardStep.MARKET_DATA_UPLOAD,
        DashboardStep.MD_PRELIMINARY_CLEARING,
        DashboardStep.MD_OVERLAY_CLEARING
      ]
      mdEntries*.status == [COMPLETED, COMPLETED, IN_PROGRESS]
    }
  }

  def "should get VD dashboard portfolios"() {
    setup:
    def dashboard = valuationDataDashboard()
    operations.insert(dashboard)
    def mdSteps = marketDataBatchDashboardSteps()
    operations.insertAll(mdSteps)
    var portfolioId = dashboard.vdExceptionManagementSetup.ipvValuationSettings[0].portfolio.entityId
    var portfolioId2 = dashboard.vdExceptionManagementSetup.ipvValuationSettings[1].portfolio.entityId

    def valuations = Map.of(portfolioId, ObjectId.get())
    calculationResultProvider.latestDashboardCalculationsByPortfolioId(dashboard.id) >> valuations

    when:
    def result = repository.getDashboardPortfolios(
      dashboard.id,
      TableFilter.emptyTableFilter(),
      ScrollRequest.of(0, 10, Sort.by(Direction.ASC, DashboardPortfolioListView.Fields.portfolio)),
      [new PortfolioResultCountView(portfolioId, 1, 2)]
      )

    then:
    result.isRight()

    def portfolios = result.getOrNull().content
    portfolios.size() == 2

    def dashboardIpvSettingsIdx0 = dashboard.vdExceptionManagementSetup.ipvValuationSettings[0]
    with(portfolios[1]) {
      assertEntityReference(company, dashboardIpvSettingsIdx0.company)
      assertEntityReference(entity, dashboardIpvSettingsIdx0.entity)
      assertEntityReference(portfolio, dashboardIpvSettingsIdx0.portfolio)
      assertIpvDataGroupEntityReference(ipvDataGroups[0], dashboardIpvSettingsIdx0.ipvDataGroupProducts[0].ipvDataGroup)
      resultId == valuations[portfolioId].toHexString()
      totalTradesCount == 2
      verifiedTradesCount == 1
    }

    def dashboardIpvSettingsIdx1 = dashboard.vdExceptionManagementSetup.ipvValuationSettings[1]
    with(portfolios[0]) {
      assertEntityReference(company, dashboardIpvSettingsIdx1.company)
      assertEntityReference(entity, dashboardIpvSettingsIdx1.entity)
      assertEntityReference(portfolio, dashboardIpvSettingsIdx1.portfolio)
      assertIpvDataGroupEntityReference(ipvDataGroups[0], dashboardIpvSettingsIdx1.ipvDataGroupProducts[0].ipvDataGroup)
      resultId == null
      totalTradesCount == 0
      verifiedTradesCount == 0
    }
  }

  def "should get dashboard PV calculations ids"() {
    setup:
    def dashboard = valuationDataDashboard()
    operations.insert(dashboard)

    def calculationResultId = ObjectId.get()
    var portfolioId = dashboard.vdExceptionManagementSetup.ipvValuationSettings[0].portfolio.entityId
    def valuations = Map.of(portfolioId, calculationResultId)
    calculationResultProvider.latestDashboardCalculationsByPortfolioId(dashboard.id) >> valuations

    when:
    def result = repository.getCalculationResultIds(
      dashboard.id,
      TableFilter.emptyTableFilter()
      )

    then:
    result.size() == 1
    result[0] == calculationResultId.toHexString()
  }

  def "should update status to COMPLETED"() {
    setup:
    def dashboard = marketDataDashboard()
    operations.insert(dashboard)


    when:
    repository.markFinished(dashboard.id)

    then:
    def updated = operations.findById(dashboard.id, Dashboard)
    updated.finishedAt.truncatedTo(ChronoUnit.MINUTES) == LocalDateTime.now().truncatedTo(ChronoUnit.MINUTES)
  }

  def "should update status to IN_PROGRESS"() {
    setup:
    def dashboard = marketDataDashboard()
    operations.insert(dashboard)


    when:
    repository.markInProgress(dashboard.id)

    then:
    def updated = operations.findById(dashboard.id, Dashboard)
    updated.startedAt.truncatedTo(ChronoUnit.MINUTES) == LocalDateTime.now().truncatedTo(ChronoUnit.MINUTES)
  }


  def "should find whether dashboard by date and market data group id exists"() {
    setup:
    operations.insert(dashboard)

    expect:
    repository.existsByMarketDataGroup(rangeStart, rangeEnd, marketDataGroupId, trsMdGroupId) == exists

    where:
    dashboard                        | rangeStart                 | rangeEnd                            | marketDataGroupId                  | trsMdGroupId                       | exists
    marketDataBatchDashboard()       | DASHBOARD_DATE             | DASHBOARD_DATE                      | MARKET_DATA_GROUP.entityId         | null                               | true
    marketDataBatchDashboard()       | DASHBOARD_DATE_ANOTHER     | DASHBOARD_DATE_ANOTHER              | MARKET_DATA_GROUP.entityId         | null                               | true
    marketDataBatchDashboard()       | DASHBOARD_DATE.plusDays(1) | DASHBOARD_DATE.plusDays(1)          | MARKET_DATA_GROUP.entityId         | null                               | true
    marketDataBatchDashboard()       | DASHBOARD_DATE.plusDays(1) | DASHBOARD_DATE_ANOTHER.minusDays(1) | MARKET_DATA_GROUP.entityId         | null                               | true

    marketDataDashboard()            | DASHBOARD_DATE             | DASHBOARD_DATE                      | MARKET_DATA_GROUP.entityId         | null                               | true
    marketDataDashboard()            | DASHBOARD_DATE_ANOTHER     | DASHBOARD_DATE_ANOTHER              | MARKET_DATA_GROUP.entityId         | null                               | false
    marketDataDashboard()            | DASHBOARD_DATE             | DASHBOARD_DATE                      | MARKET_DATA_GROUP_ANOTHER.entityId | null                               | false

    valuationDataDashboard()         | DASHBOARD_DATE             | DASHBOARD_DATE                      | MARKET_DATA_GROUP.entityId         | null                               | false
    trsMarketDataDashboard()        | DASHBOARD_DATE             | DASHBOARD_DATE                      | null                                | MARKET_DATA_GROUP_ANOTHER.entityId | true
    trsMarketDataDashboard()        | DASHBOARD_DATE             | DASHBOARD_DATE                      | null                               | MARKET_DATA_GROUP.entityId         | false
    trsMarketDataBatchDashboard()  | DASHBOARD_DATE             | DASHBOARD_DATE                      | null                                | MARKET_DATA_GROUP_ANOTHER.entityId | true
    trsMarketDataBatchDashboard()  | DASHBOARD_DATE             | DASHBOARD_DATE                      | null                               | MARKET_DATA_GROUP.entityId         | false
  }

  def "should find whether dashboard by date and portfolios ids exists"() {
    setup:
    operations.insert(dashboard.tap {
      vdExceptionManagementSetup?.ipvValuationSettings = [PORTFOLIO_IPV_SETTINGS, PORTFOLIO_IPV_SETTINGS_ANOTHER]
    })

    expect:
    repository.existsByPortfolios(rangeStart, rangeEnd, portfolios) == exists

    where:
    dashboard                  | rangeStart             | rangeEnd               | portfolios                                                                       | exists
    valuationDataDashboard()   | DASHBOARD_DATE         | DASHBOARD_DATE         | []                                                                               | false
    valuationDataDashboard()   | DASHBOARD_DATE         | DASHBOARD_DATE         | [new PricingSlotPortfolioForm(PORTFOLIO.entityId, PricingSlot.OTHER)]            | true
    valuationDataDashboard()   | DASHBOARD_DATE         | DASHBOARD_DATE         | [
      new PricingSlotPortfolioForm(PORTFOLIO.entityId, PricingSlot.OTHER),
      new PricingSlotPortfolioForm(PORTFOLIO_ANOTHER.entityId, PricingSlot.OTHER)
    ]    | true
    valuationDataDashboard()   | DASHBOARD_DATE         | DASHBOARD_DATE         | [new PricingSlotPortfolioForm(PORTFOLIO_ANOTHER.entityId, PricingSlot.OTHER)]    | false
    valuationDataDashboard()   | DASHBOARD_DATE         | DASHBOARD_DATE         | [new PricingSlotPortfolioForm(PORTFOLIO_ANOTHER.entityId, PricingSlot.LDN_1200)] | true
    valuationDataDashboard()   | DASHBOARD_DATE_ANOTHER | DASHBOARD_DATE_ANOTHER | [new PricingSlotPortfolioForm(PORTFOLIO.entityId, PricingSlot.OTHER)]            | false

    marketDataBatchDashboard() | DASHBOARD_DATE         | DASHBOARD_DATE         | [new PricingSlotPortfolioForm(PORTFOLIO.entityId, PricingSlot.OTHER)]            | false
    trsMarketDataBatchDashboard() | DASHBOARD_DATE         | DASHBOARD_DATE         | [new PricingSlotPortfolioForm(PORTFOLIO.entityId, PricingSlot.OTHER)]            | false
    marketDataDashboard()      | DASHBOARD_DATE         | DASHBOARD_DATE         | [new PricingSlotPortfolioForm(PORTFOLIO.entityId, PricingSlot.OTHER)]            | false
  }

  def "should delete dashboard and results"() {
    setup:
    def dashboard = marketDataDashboard(null)
    operations.insert(dashboard)

    when:
    def result = repository.deleteDashboard(dashboard.id)

    then:
    1 * publisher.publishEvent({ it.eventType == EventType.DASHBOARD_DELETED && it.data == dashboard.id })
    1 * publisher.publishEvent(DashboardDeletedEvent.newOf(dashboard.id))

    result.isRight()
    operations.findAll(Dashboard).isEmpty()
  }

  def "should return dashboards for given market data group IDs and date"() {
    given:
    def date = LocalDate.of(2024, 6, 1)
    def groupId1 = "mdg-1"
    def groupId2 = "mdg-2"
    def groupId3 = "mdg-3"
    def dashboard1 = marketDataDashboard().tap {
      it.id = "md1"
      it.dateRange = DateRange.newOf(date, date)
      it.mdExceptionManagementSetup.marketDataGroup.entityId = groupId1
    }
    operations.insert(dashboard1)
    def dashboard2 = marketDataDashboard().tap {
      it.id = "md2"
      it.dateRange = DateRange.newOf(date, date)
      it.mdExceptionManagementSetup.marketDataGroup.entityId = groupId2
    }
    operations.insert(dashboard2)
    def dashboardOtherDate = marketDataDashboard().tap {
      it.id = "mdOther"
      it.dateRange = DateRange.newOf(date.plusDays(1), date.plusDays(1))
      it.mdExceptionManagementSetup.marketDataGroup.entityId = groupId3
    }
    operations.insert(dashboardOtherDate)

    when:
    def result = repository.getSingleDayMDDashboard([groupId1, groupId2, groupId3] as Set, date).toMap()

    then:
    result.size() == 2
    result[groupId1].id == dashboard1.id
    result[groupId2].id == dashboard2.id
    !result.containsKey(groupId3)
  }

  static def assertDashboard(DashboardListView view, Dashboard entity) {
    assert view.id == entity.id
    assert view.type == entity.type
    assertDateRange(view.dateRange, entity.dateRange)
    assert view.createdBy == entity.createdBy.name
    true
  }

  static void assertDateRange(DateRangeView view, DateRange entity) {
    assert view.startDate == entity.startDate
    assert view.endDate == entity.endDate
  }

  static void assertMdSetup(MdExceptionManagementSetupView view, MdExceptionManagementSetup entity) {
    ObjectUtils.allNull(view, entity) || ObjectUtils.allNotNull(view, entity)
    if (view == null) return
      assertEntityReference(view.marketDataGroup, entity.marketDataGroup)
    assert view.previousDate == entity.previousDate
  }

  static void assertVdSetup(VdExceptionManagementSetupView view, VdExceptionManagementSetup entity) {
    ObjectUtils.allNull(view, entity) || ObjectUtils.allNotNull(view, entity)
    if (view == null) return
      assert view.portfoliosFilter.pricingSlotPortfoliosLabels == entity.portfoliosFilter.pricingSlotPortfoliosLabels
    assert view.ipvValuationSettings.size() == entity.ipvValuationSettings.size()
    view.ipvValuationSettings.eachWithIndex { v, idx ->
      assertEntityReference(v.ipvDataGroup, entity.ipvValuationSettings.get(idx).ipvDataGroupProducts[0].ipvDataGroup)
    }
  }

  static void assertMdBatchEntry(DashboardEntryMdBatchView view,
    DashboardEntryMdBatch entity,
    TaskCountsView taskCountsView) {
    assertEntry(view, entity, taskCountsView)
  }

  static void assertEntry(DashboardEntryView view, DashboardEntry entity, TaskCountsView taskCountsView) {
    assert view.dashboardId == entity.dashboardId
    assert view.step == entity.step
    assert view.startedAt != null
    assert view.finishedAt != null
    assert view.status == entity.status
    assert view.taskCounts == taskCountsView
    assert view.errorMessage == entity.error?.description
    assert view.breaksCount == entity.breaksCount
    assert view.resultId == entity.resultId
  }

  static void assertEntityReference(EntityReferenceView view, EntityReference entity) {
    ObjectUtils.allNull(view, entity) || ObjectUtils.allNotNull(view, entity)
    assert view?.entityId == entity?.entityId
    assert view?.name == entity?.name
  }

  static void assertIpvDataGroupEntityReference(IpvDataGroupReferenceView view, IpvDataGroupReference entity) {
    ObjectUtils.allNull(view, entity) || ObjectUtils.allNotNull(view, entity)
    assert view?.entityId == entity?.entityId
    assert view?.name == entity?.name
    assert view?.pricingSlot == entity?.pricingSlot
  }
}
