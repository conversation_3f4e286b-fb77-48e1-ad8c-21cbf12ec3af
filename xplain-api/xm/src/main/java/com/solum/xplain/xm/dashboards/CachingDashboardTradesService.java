package com.solum.xplain.xm.dashboards;

import static com.google.common.cache.CacheLoader.from;
import static io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics.monitor;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.Weigher;
import com.google.common.util.concurrent.UncheckedExecutionException;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import com.solum.xplain.core.utils.XplainGuavaCacheLoader;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementResultMapper;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Service;

/**
 * Service to obtain trades. When doing a bulk query the result is cached both in bulk and also the
 * individual trades are cached so that they can be retrieved individually from the cache without
 * further queries.
 *
 * <p>TODO(10608): Cache of cache is a smell.
 */
@Service
@Slf4j
@NullMarked
public class CachingDashboardTradesService implements CacheInvalidationListener {
  public static final Duration FRESHNESS_LIMIT = Duration.of(20, ChronoUnit.MINUTES);
  public static final long SIZE_LIMIT = 200_000L;
  private final PortfolioItemRepository portfolioItemRepository;
  private final IpvExceptionManagementResultMapper resultMapper;
  private final MeterRegistry meterRegistry;

  private final LoadingCache<BitemporalDate, StateDateCache> dashboardTradesCache;

  public CachingDashboardTradesService(
      PortfolioItemRepository portfolioItemRepository,
      IpvExceptionManagementResultMapper resultMapper,
      MeterRegistry meterRegistry) {
    this.portfolioItemRepository = portfolioItemRepository;
    this.resultMapper = resultMapper;
    this.meterRegistry = meterRegistry;
    this.dashboardTradesCache =
        monitor(
            meterRegistry,
            CacheBuilder.newBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .build(from(StateDateCache::new)),
            "(VDXM) StateDateCache");
  }

  @RequiredArgsConstructor
  private class StateDateCache {
    private final BitemporalDate stateDate;

    private record PortfolioTradeEntityKey(String portfolioId, String portfolioItemEntityId) {}

    private final LoadingCache<String, List<Trade>> portfolioTradesCache =
        monitor(
            meterRegistry,
            CacheBuilder.newBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .weigher((Weigher<String, List<Trade>>) (key, value) -> value.size())
                .maximumWeight(SIZE_LIMIT)
                .build(
                    XplainGuavaCacheLoader.<String, List<Trade>>bulkCacheLoader(
                        this::singleLoad, this::bulkLoad)),
            "(VDXM) PortfolioTradesCache");

    private final LoadingCache<PortfolioTradeEntityKey, Trade> portfolioTradeCache =
        monitor(
            meterRegistry,
            CacheBuilder.newBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .maximumSize(SIZE_LIMIT)
                .build(from(key -> loadTrade(key.portfolioId(), key.portfolioItemEntityId()))),
            "(VDXM) PortfolioTradeCache)");

    public Stream<Trade> dashboardTradesStream(String portfolioId) throws ExecutionException {
      return portfolioTradesCache.get(portfolioId).stream();
    }

    public Trade dashboardTrade(String portfolioId, String portfolioItemEntityId)
        throws ExecutionException {
      return portfolioTradeCache.get(
          new PortfolioTradeEntityKey(portfolioId, portfolioItemEntityId));
    }

    private Trade loadTrade(String portfolioId, String portfolioItemEntityId) {
      log.debug(
          "Loading single trade {}/{} ({})",
          portfolioId,
          portfolioItemEntityId,
          stateDate.getActualDate());
      return portfolioItemRepository
          .portfolioItemLatest(portfolioId, portfolioItemEntityId, stateDate)
          .map(resultMapper::fromPortfolioItem)
          .getOrThrow(
              () -> new IllegalArgumentException("Trade not found: " + portfolioItemEntityId));
    }

    private List<Trade> singleLoad(String portfolioId) {
      log.debug(
          "Loading active trades for portfolio {} ({})", portfolioId, stateDate.getActualDate());
      return portfolioItemRepository
          .activePortfolioItemsStream(portfolioId, stateDate)
          .map(resultMapper::fromPortfolioItem)
          .peek(
              trade -> {
                // Prime the single-trade cache here
                portfolioTradeCache.put(
                    new PortfolioTradeEntityKey(trade.getPortfolioId(), trade.getEntityId()),
                    trade);
              })
          .toList();
    }

    private Map<String, List<Trade>> bulkLoad(Iterable<? extends String> keys) {
      var allKeys = new HashSet<String>();
      keys.forEach(allKeys::add);
      return StreamEx.of(portfolioItemRepository.activePortfolioItemsStream(allKeys, stateDate))
          .map(resultMapper::fromPortfolioItem)
          .peek(
              trade -> {
                // Prime the single-trade cache here
                portfolioTradeCache.put(
                    new PortfolioTradeEntityKey(trade.getPortfolioId(), trade.getEntityId()),
                    trade);
              })
          .groupingBy(Trade::getPortfolioId);
    }
  }

  public Stream<Trade> dashboardTradesStream(String portfolioId, BitemporalDate stateDate) {
    try {
      return dashboardTradesCache.get(stateDate).dashboardTradesStream(portfolioId);
    } catch (ExecutionException | UncheckedExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  public Trade dashboardTrade(
      String portfolioId, String portfolioItemEntityId, BitemporalDate stateDate) {
    try {
      return dashboardTradesCache.get(stateDate).dashboardTrade(portfolioId, portfolioItemEntityId);
    } catch (ExecutionException | UncheckedExecutionException e) {
      throw new IllegalArgumentException("Trade not found: " + portfolioItemEntityId, e);
    }
  }

  public void prefetchDashboardTrades(Set<String> portfolioIds, BitemporalDate stateDate) {
    try {
      // ignore the results, but preload the cache.
      dashboardTradesCache.get(stateDate).portfolioTradesCache.getAll(portfolioIds);
    } catch (ExecutionException | UncheckedExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void invalidateAll() {
    this.dashboardTradesCache.invalidateAll();
  }
}
