package com.solum.xplain.xm.dashboardsteps.opvvaluations;

import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_OVERLAY_CLEARING;
import static com.solum.xplain.xm.workflow.XmWorkflowService.businessKeyFromDashboardId;

import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.workflow.repository.XmTasksDefinitionQueryRepository;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@NullMarked
public class DashboardMarketDataSourceResolver {
  private final DashboardRepository dashboardRepository;
  private final DashboardEntryRepository entryRepository;

  // TODO(SXSD-9565): Seems very inefficient to go through this MongoView,
  //  IIUC, it should be possible to just perform a simple document query on wfProcessExecution
  // collection.
  private final XmTasksDefinitionQueryRepository xmTasksDefinitionQueryRepository;

  /**
   * The default market data source type for valuation using XPLAIN Default. This is used when no
   * overlay is present.
   */
  private static final MarketDataSourceType DEFAULT_MARKET_SOURCE_TYPE =
      MarketDataSourceType.RAW_PRIMARY;

  public MarketDataSourceType resolve(String marketDataGroupId, LocalDate date) {
    return dashboardRepository
        .getSingleDayMDDashboard(marketDataGroupId, date)
        .map(this::toOverlay)
        .orElse(DEFAULT_MARKET_SOURCE_TYPE);
  }

  public Map<String, MarketDataSourceType> resolve(Set<String> marketDataGroupId, LocalDate date) {
    var result =
        dashboardRepository
            .getSingleDayMDDashboard(marketDataGroupId, date)
            .mapValues(this::toOverlay)
            .toMap();
    marketDataGroupId.forEach(id -> result.putIfAbsent(id, DEFAULT_MARKET_SOURCE_TYPE));
    return result;
  }

  private MarketDataSourceType toOverlay(Dashboard dashboard) {
    // slightly inefficient for legacy as we check for it last.
    // however we expect the new workflow to be used in most cases going forward.
    if (overlayPresent(dashboard) || legacyOverlayPresent(dashboard)) {
      return MarketDataSourceType.OVERLAY;
    } else {
      return DEFAULT_MARKET_SOURCE_TYPE;
    }
  }

  /**
   * Returns true if there are overlay clearing steps present in the dashboard.
   *
   * @param dashboard the dashboard.
   */
  @Deprecated(since = "2.0", forRemoval = true)
  private boolean legacyOverlayPresent(Dashboard dashboard) {
    return hasClearingStepCompleted(
        entryRepository.getMdEntries(dashboard.getId()), MD_OVERLAY_CLEARING);
  }

  /**
   * Returns true there are any Overlay task present in the dashboard.
   *
   * @param dashboard the dashboard.
   */
  private boolean overlayPresent(Dashboard dashboard) {
    return !xmTasksDefinitionQueryRepository
        .getDashboardTimelineTaskViews(
            businessKeyFromDashboardId(dashboard.getId()).toString(),
            dashboard.getRecordDate(),
            TaskExceptionManagementType.OVERLAY,
            Pageable.ofSize(1))
        .isEmpty();
  }

  private boolean hasClearingStepCompleted(List<DashboardEntryMd> mdSteps, DashboardStep... step) {
    var steps = Set.of(step);
    return mdSteps.stream()
        .filter(c -> steps.contains(c.getStep()))
        .anyMatch(c -> c.getStatus() == StepStatus.COMPLETED);
  }
}
