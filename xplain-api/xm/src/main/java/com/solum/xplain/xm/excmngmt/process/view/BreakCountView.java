package com.solum.xplain.xm.excmngmt.process.view;

import com.solum.xplain.workflow.value.WorkflowStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@EqualsAndHashCode
public class BreakCountView {
  private Long totalTestsCount;
  private Long appliedTestsCount;
  private Long breaksCount;
  private Long verifiedCount;
  private Long resolvedCount;
  private Long totalCount;
  private Long onboardingCount;
  private Long passedOnboardingCount;
  private Long totalOnboardingTrades;
  private Long onboardingTradesWithMissingData;

  /**
   * The count of breaks that are waiting for submit in order to progress. For workflow, this is
   * based off the status being {@link WorkflowStatus#FINALIZING} but for legacy it is based off the
   * verified vs resolved vs breaks counts.
   */
  private Long finalizingCount;

  /**
   * The count of breaks that are on hold waiting to be reset for an outcome to be applied. This is
   * based off the status being {@link WorkflowStatus#HELD}.
   */
  private Long heldCount;

  @Schema(
      description =
          "Key to be used with API ../progress to get the progress of the overall workflow.")
  private String progressKey;

  public static BreakCountView merge(BreakCountView first, BreakCountView second) {
    var merged = new BreakCountView();
    merged.setTotalTestsCount(first.totalTestsCount + second.totalTestsCount);
    merged.setAppliedTestsCount(first.appliedTestsCount + second.appliedTestsCount);
    merged.setBreaksCount(first.breaksCount + second.breaksCount);
    merged.setVerifiedCount(first.verifiedCount + second.verifiedCount);
    merged.setResolvedCount(first.resolvedCount + second.resolvedCount);
    merged.setTotalCount(first.totalCount + second.totalCount);
    merged.setTotalOnboardingTrades(first.totalOnboardingTrades + second.totalOnboardingTrades);
    merged.setOnboardingTradesWithMissingData(
        first.onboardingTradesWithMissingData + second.onboardingTradesWithMissingData);
    merged.setPassedOnboardingCount(first.passedOnboardingCount + second.passedOnboardingCount);
    merged.setFinalizingCount(first.finalizingCount + second.finalizingCount);
    merged.setHeldCount(first.heldCount + second.heldCount);
    merged.setProgressKey(first.progressKey != null ? first.progressKey : second.progressKey);
    return merged;
  }
}
