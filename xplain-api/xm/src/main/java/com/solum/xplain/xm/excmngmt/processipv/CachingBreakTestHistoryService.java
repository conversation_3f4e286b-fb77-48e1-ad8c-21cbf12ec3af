package com.solum.xplain.xm.excmngmt.processipv;

import static com.google.common.cache.CacheLoader.from;
import static io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics.monitor;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import com.solum.xplain.xm.dashboards.views.DashboardDateView;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.value.EntryBreakHistory;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Service;

/** Service to obtain previous breaks for individual trades, by querying and caching it in bulk. */
@Service
@Slf4j
@NullMarked
public class CachingBreakTestHistoryService implements CacheInvalidationListener {
  public static final Duration FRESHNESS_LIMIT = Duration.of(5, ChronoUnit.MINUTES);
  private final IpvExceptionManagementDataService ipvExceptionManagementDataService;
  private final IpvExceptionManagementCalculationRepository
      ipvExceptionManagementCalculationRepository;
  private final MeterRegistry meterRegistry;
  private final LoadingCache<BitemporalDate, StateDateCache> providerDataCache;

  public CachingBreakTestHistoryService(
      IpvExceptionManagementDataService ipvExceptionManagementDataService,
      IpvExceptionManagementCalculationRepository ipvExceptionManagementCalculationRepository,
      MeterRegistry meterRegistry) {
    this.ipvExceptionManagementDataService = ipvExceptionManagementDataService;
    this.ipvExceptionManagementCalculationRepository = ipvExceptionManagementCalculationRepository;
    this.meterRegistry = meterRegistry;
    this.providerDataCache =
        monitor(
            meterRegistry,
            CacheBuilder.newBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .build(from(StateDateCache::new)),
            "(VDXM) BreakTestHistoryCache");
  }

  /** Internal cache for a single bitemporal date. */
  @RequiredArgsConstructor
  private class StateDateCache {
    private final BitemporalDate stateDate;

    private record PreviousDayKey(String portfolioId, IpvExceptionManagementPhase phase) {}

    LoadingCache<PreviousDayKey, Map<String, EntryBreakHistory>> previousDayCache =
        monitor(
            meterRegistry,
            CacheBuilder.newBuilder()
                .expireAfterWrite(FRESHNESS_LIMIT)
                .build(from(pdk -> previousDashboardBreaks(pdk.portfolioId(), pdk.phase()))),
            "(VDXM) BreakTestHistoryCache->PreviousDayCache)");

    public EntryBreakHistory breakHistory(Trade trade, IpvExceptionManagementPhase phase)
        throws ExecutionException {
      PreviousDayKey pdk = new PreviousDayKey(trade.getPortfolioId(), phase);
      Map<String, EntryBreakHistory> previousDayBreaks = previousDayCache.get(pdk);
      return previousDayBreaks.get(trade.getKey());
    }

    private Map<String, EntryBreakHistory> previousDashboardBreaks(
        String portfolioId, IpvExceptionManagementPhase phase) {
      log.debug(
          "Loading historic breaking test data for {}/{} ({})",
          portfolioId,
          phase,
          stateDate.getActualDate());

      Optional<DashboardDateView> previousDashboard =
          ipvExceptionManagementDataService.previousDashboard(
              portfolioId, stateDate.getActualDate());

      return previousDashboard
          .map(
              d ->
                  ipvExceptionManagementCalculationRepository.portfolioDashboardBreaksHistory(
                      d.id(), phase, portfolioId))
          .map(breaks -> CollectionUtils.toMap(breaks, EntryBreakHistory::getUniqueKey))
          .orElse(Collections.emptyMap());
    }
  }

  /**
   * Check caches for previous dashboard breaks for the VDG/portfolio/phase and fetch if missing,
   * then get the data for the specific trade from those.
   *
   * @param trade the trade to retrieve valuation data for
   * @param phase the exception management phase to retrieve data for
   * @param stateDate relevant state date
   * @return historical breaks from the previous dashboard for the trade
   */
  public EntryBreakHistory previousDashboardBreaks(
      Trade trade, IpvExceptionManagementPhase phase, BitemporalDate stateDate) {
    try {
      return providerDataCache.get(stateDate).breakHistory(trade, phase);
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void invalidateAll() {
    this.providerDataCache.invalidateAll();
  }
}
