package com.solum.xplain.xm.excmngmt.processipv;

import static io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics.monitor;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toSet;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.entity.Company;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.utils.XplainGuavaCacheLoader;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverter;
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverterFactory;
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverterFactory.CompanyEntityRecordData;
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverterFactory.PortfolioIpvValueConverterParameters;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@NullMarked
public class CachingPortfolioIpvConverterService implements CacheInvalidationListener {

  public static final Duration FRESHNESS_LIMIT = Duration.of(20, ChronoUnit.MINUTES);
  private final PortfolioIpvValueConverterFactory converterFactory;
  private final CompanyRepository companyRepository;
  private final CompanyLegalEntityRepository companyLegalEntityRepository;

  private final LoadingCache<ConverterCacheKey, PortfolioIpvValueConverter> converterCache;
  private final LoadingCache<String, String> companyIdByExternalIdCache;
  private final LoadingCache<CompanyLegalEntityCacheKey, String>
      companyLegalEntityIdByExternalIdCache;

  public CachingPortfolioIpvConverterService(
      PortfolioIpvValueConverterFactory converterFactory,
      CompanyRepository companyRepository,
      CompanyLegalEntityRepository companyLegalEntityRepository,
      MeterRegistry meterRegistry) {
    this.converterFactory = converterFactory;
    this.companyRepository = companyRepository;
    this.companyLegalEntityRepository = companyLegalEntityRepository;
    this.converterCache =
        monitor(
            meterRegistry,
            createCache(
                XplainGuavaCacheLoader
                    .<ConverterCacheKey, PortfolioIpvValueConverter>bulkCacheLoader(
                        this::singleConverterLoad, this::bulkConverterLoad)),
            "(VDXM) ConverterCache");
    this.companyIdByExternalIdCache =
        monitor(
            meterRegistry,
            createCache(
                XplainGuavaCacheLoader.<String, String>bulkCacheLoader(
                    this::singleCompanyExternalIdLoad, this::bulkCompanyExternalIdLoad)),
            "(VDXM) ExternalCompanyIdCache");
    this.companyLegalEntityIdByExternalIdCache =
        monitor(
            meterRegistry,
            createCache(
                XplainGuavaCacheLoader.<CompanyLegalEntityCacheKey, String>bulkCacheLoader(
                    this::singleLegalEntityLoad, this::bulkLegalEntityLoad)),
            "(VDXM) LegalEntityExternalIdCache");
  }

  private <KEY, VALUE> LoadingCache<KEY, VALUE> createCache(CacheLoader<KEY, VALUE> loader) {
    return CacheBuilder.newBuilder().expireAfterAccess(FRESHNESS_LIMIT).build(loader);
  }

  public void preloadPortfolioIpvValueConverter(List<Trade> trades, BitemporalDate stateDate) {
    try {
      this.preloadCompanyIdByExternalIdCache(
          trades.stream().map(Trade::getExternalCompanyId).collect(toSet()));
      Set<CompanyLegalEntityCacheKey> set = new HashSet<>();
      for (Trade trade : trades) {
        CompanyLegalEntityCacheKey companyLegaLEntityCacheKey =
            new CompanyLegalEntityCacheKey(
                companyIdByExternalIdCache.get(trade.getExternalCompanyId()),
                trade.getExternalEntityId());
        set.add(companyLegaLEntityCacheKey);
      }
      this.preloadCompanyLegaLEntityCacheKey(set);
      // fetch company id from cache using company external id
      // fetch legal entity id from cache using legal entity external id and company._id
      var converterCacheKeys = new ArrayList<ConverterCacheKey>();
      for (Trade trade : trades) {
        // fetch company id from cache using company external id
        String companyId = companyIdByExternalIdCache.get(trade.getExternalCompanyId());
        // fetch legal entity id from cache using legal entity external id and company._id
        String companyLegalEntityId =
            companyLegalEntityIdByExternalIdCache.get(
                new CompanyLegalEntityCacheKey(companyId, trade.getExternalEntityId()));
        CompanyEntityRecordData recordData =
            new CompanyEntityRecordData(companyId, companyLegalEntityId);
        ConverterCacheKey apply =
            new ConverterCacheKey(recordData, trade.getPortfolioExternalId(), stateDate);
        converterCacheKeys.add(apply);
      }
      converterCache.getAll(converterCacheKeys);
    } catch (ExecutionException e) {
      log.error("Failed to preload portfolio ipv value converter", e);
      throw new IllegalArgumentException(e);
    }
  }

  /**
   * Get a converter using company and entity external ids.
   *
   * @param companyExternalId the external id of the company
   * @param companyLegalEntityExternalId the external id of the legal entity
   * @param portfolioExternalId the external id of the portfolio
   * @param stateDate the state date
   * @return PortfolioIpvValueConverter
   */
  public PortfolioIpvValueConverter getConverter(
      String companyExternalId,
      String companyLegalEntityExternalId,
      String portfolioExternalId,
      BitemporalDate stateDate) {
    try {
      // fetch company id from cache using company external id
      String companyId = companyIdByExternalIdCache.get(companyExternalId);
      // fetch legal entity id from cache using legal entity external id and company._id
      String companyLegalEntityId =
          companyLegalEntityIdByExternalIdCache.get(
              new CompanyLegalEntityCacheKey(companyId, companyLegalEntityExternalId));
      CompanyEntityRecordData recordData =
          new CompanyEntityRecordData(companyId, companyLegalEntityId);
      return converterCache.get(new ConverterCacheKey(recordData, portfolioExternalId, stateDate));
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void invalidateAll() {
    this.companyIdByExternalIdCache.invalidateAll();
    this.companyLegalEntityIdByExternalIdCache.invalidateAll();
    this.converterCache.invalidateAll();
  }

  private void preloadCompanyIdByExternalIdCache(Set<String> companyExternalIds) {
    try {
      companyIdByExternalIdCache.getAll(companyExternalIds);
    } catch (ExecutionException e) {
      log.error(
          "Failed to preload company id cache, this may be because a company id has not been found.",
          e);
      throw new IllegalStateException(e);
    }
  }

  private void preloadCompanyLegaLEntityCacheKey(Set<CompanyLegalEntityCacheKey> allCacheKeys) {
    try {
      companyLegalEntityIdByExternalIdCache.getAll(allCacheKeys);
    } catch (ExecutionException e) {
      log.error("Failed to preload company legal entity cache", e);
      throw new RuntimeException(e);
    }
  }

  private PortfolioIpvValueConverter singleConverterLoad(ConverterCacheKey key) {
    log.debug(
        "Loading currency converter for {}/{}/{} ({})",
        key.data().companyId(),
        key.data().legalEntityId(),
        key.portfolioExternalId(),
        key.stateDate().getActualDate());
    return converterFactory.converter(
        key.stateDate().getActualDate(), key.data(), key.portfolioExternalId(), key.stateDate());
  }

  private Map<ConverterCacheKey, PortfolioIpvValueConverter> bulkConverterLoad(
      Iterable<? extends ConverterCacheKey> keys) {
    var allParameters = new HashSet<PortfolioIpvValueConverterParameters>();
    BitemporalDate stateDate = null;
    for (ConverterCacheKey key : keys) {
      stateDate = key.stateDate(); // state date is unique.
      allParameters.add(
          new PortfolioIpvValueConverterParameters(key.data, key.portfolioExternalId));
    }
    final var finalStateDate = stateDate;
    return converterFactory
        .allConverters(allParameters, finalStateDate)
        .mapKeys(
            parameters ->
                new ConverterCacheKey(
                    parameters.data(), parameters.portfolioExternalId(), finalStateDate))
        .toMap();
  }

  private String singleCompanyExternalIdLoad(String key) {
    var company = companyRepository.findByExternalId(key);
    if (company == null) {
      // as per contract, must never be null. throw if not found.
      throw new IllegalArgumentException("Company with external id " + key + " not found");
    } else {
      return company.getId();
    }
  }

  private Map<String, String> bulkCompanyExternalIdLoad(Iterable<? extends String> keys) {
    var allKeys = new HashSet<String>();
    keys.forEach(allKeys::add);
    var foundKeys =
        StreamEx.of(companyRepository.findByExternalIds(allKeys))
            .toMap(Company::getExternalCompanyId, Company::getId);
    return StreamEx.of(allKeys).filter(foundKeys::containsKey).toMap(identity(), foundKeys::get);
  }

  private String singleLegalEntityLoad(CompanyLegalEntityCacheKey key) {
    var legalEntity =
        companyLegalEntityRepository.findByExternalId(
            key.companyId(), key.companyLegalEntityExternalId());
    if (legalEntity == null) {
      throw new IllegalArgumentException(
          "Legal entity with external id " + key.companyLegalEntityExternalId() + " not found");
    } else {
      return legalEntity.getId();
    }
  }

  private Map<CompanyLegalEntityCacheKey, String> bulkLegalEntityLoad(
      Iterable<? extends CompanyLegalEntityCacheKey> keys) {
    var allKeys = new HashSet<CompanyLegalEntityCacheKey>();
    keys.forEach(allKeys::add);
    Set<Pair<String, String>> allPairs =
        StreamEx.of(allKeys)
            .map(key -> Pair.of(key.companyId(), key.companyLegalEntityExternalId()))
            .toSet();

    var foundLegalEntities =
        StreamEx.of(companyLegalEntityRepository.findByCompanyIdAndExternalIds(allPairs))
            .toMap(item -> Pair.of(item.getCompanyId(), item.getExternalId()), identity());
    return StreamEx.of(allKeys.stream())
        .filter(
            key ->
                foundLegalEntities.containsKey(
                    Pair.of(key.companyId(), key.companyLegalEntityExternalId())))
        .toMap(
            Function.identity(),
            key ->
                foundLegalEntities
                    .get(Pair.of(key.companyId(), key.companyLegalEntityExternalId()))
                    .getId());
  }

  private record ConverterCacheKey(
      CompanyEntityRecordData data, String portfolioExternalId, BitemporalDate stateDate) {}

  private record CompanyLegalEntityCacheKey(
      String companyId, String companyLegalEntityExternalId) {}
}
