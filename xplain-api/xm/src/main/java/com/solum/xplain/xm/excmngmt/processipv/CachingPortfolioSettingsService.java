package com.solum.xplain.xm.excmngmt.processipv;

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics.monitor;
import static java.util.function.Function.identity;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.CompanyPortfolioSettingsResolver;
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView;
import com.solum.xplain.core.company.value.PortfolioSettings;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.utils.XplainGuavaCacheLoader;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import io.atlassian.fugue.Either;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@NullMarked
public class CachingPortfolioSettingsService implements CacheInvalidationListener {
  private final CompanyPortfolioSettingsResolver companyPortfolioSettingsResolver;

  private static final Duration FRESHNESS_LIMIT = Duration.of(1, ChronoUnit.MINUTES);

  private final LoadingCache<
          CacheKey, Either<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>>
      cache;

  public CachingPortfolioSettingsService(
      CompanyPortfolioSettingsResolver companyPortfolioSettingsResolver,
      MeterRegistry meterRegistry) {
    this.companyPortfolioSettingsResolver = companyPortfolioSettingsResolver;
    this.cache =
        monitor(
            meterRegistry,
            CacheBuilder.newBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .build(
                    XplainGuavaCacheLoader
                        .<CacheKey,
                            Either<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>>
                            bulkCacheLoader(this::singleLoad, this::bulkLoad)),
            "(VDXM) PortfolioSettings<CompanyLegalEntitySettingsView>");
  }

  public void prefetchPortfolioSettings(
      Collection<? extends PortfolioCondensedView> allPortfolios, BitemporalDate stateDate) {
    try {
      var allCacheKeys = allPortfolios.stream().map(p -> new CacheKey(p, stateDate)).toList();
      cache.getAll(
          allCacheKeys); // ignore returned values as it would expose the (private) cache key.
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  public Either<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>> portfolioSettings(
      PortfolioCondensedView portfolio, BitemporalDate stateDate) {
    try {
      return cache.get(new CacheKey(portfolio, stateDate));
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void invalidateAll() {
    this.cache.invalidateAll();
  }

  private Either<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>> singleLoad(
      CacheKey key) {
    return companyPortfolioSettingsResolver.portfolioSettings(
        key.companyId(), key.entityId(), key.portfolioId(), key.stateDate());
  }

  private Map<CacheKey, Either<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>>
      bulkLoad(Iterable<? extends CacheKey> keys) {
    var allPortfolioIds = new HashSet<String>();
    var allKeys = new ArrayList<CacheKey>();
    BitemporalDate stateDate = null;
    for (CacheKey key : keys) {
      allPortfolioIds.add(key.portfolioId());
      allKeys.add(key);
      // all state dates must be the same.
      // by construction, they are.
      stateDate = key.stateDate();
    }
    if (stateDate == null) {
      return Collections.emptyMap();
    }

    var allSettingsById =
        StreamEx.of(companyPortfolioSettingsResolver.portfoliosSettings(allPortfolioIds, stateDate))
            .valuesToMap(item -> item.getView().getId());

    return StreamEx.of(allKeys)
        .toMap(
            identity(),
            cacheKey -> {
              var setting = allSettingsById.get(cacheKey.portfolioId());
              if (setting == null) {
                return Either.left(OBJECT_NOT_FOUND.entity("Company portfolio settings not found"));
              } else {
                return Either.right(setting);
              }
            });
  }

  private record CacheKey(
      String companyId, String entityId, String portfolioId, BitemporalDate stateDate) {
    private CacheKey(PortfolioCondensedView view, BitemporalDate stateDate) {
      this(view.getCompanyId(), view.getEntityId(), view.getId(), stateDate);
    }
  }
}
