package com.solum.xplain.xm.excmngmt.processipv;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_RUN_VD_TASK_EXECUTION;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VD_EXC_MANAGEMENT_RESULT;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.Filtered;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.companydocs.validation.ValidDocumentSize;
import com.solum.xplain.core.companydocs.validation.ValidDocumentType;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xm.excmngmt.form.ApplyVerificationForm;
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm;
import com.solum.xplain.xm.excmngmt.form.UndoApprovalForm;
import com.solum.xplain.xm.excmngmt.form.UndoResolutionForm;
import com.solum.xplain.xm.excmngmt.process.view.BreakCountView;
import com.solum.xplain.xm.excmngmt.processipv.form.ApplyResolutionForm;
import com.solum.xplain.xm.excmngmt.processipv.form.TradeFilterForm;
import com.solum.xplain.xm.excmngmt.processipv.form.TradeResultTasksBreaksFilterForm;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvExceptionManagementCountedFiltersView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvXmDistributionGraphView;
import com.solum.xplain.xm.excmngmt.processipv.view.chart.IpvTaskChartData;
import com.solum.xplain.xm.workflow.view.CallActivityProgressView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/exception-management/ipv-calculations")
@RequiredArgsConstructor
public class IpvExceptionManagementController {

  private final IpvExceptionManagementControllerService service;
  private final IpvExceptionManagementGraphsService graphsService;

  @Operation(summary = "Get overlay items")
  @CommonErrors
  @PostMapping("/overlay/items")
  @Filtered
  @PreAuthorize(AUTHORITY_VIEW_VD_EXC_MANAGEMENT_RESULT)
  public ScrollableEntry<IpvTradeOverlayResultView> overlayItems(
      @RequestBody List<String> taskIds,
      @Valid ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter) {
    return service.overlayItems(taskIds, displayFilter, tableFilter, ScrollRequest.unconstrained());
  }

  @Operation(summary = "Overlay progress")
  @CommonErrors
  @GetMapping("/overlay/progress")
  @PreAuthorize(AUTHORITY_VIEW_VD_EXC_MANAGEMENT_RESULT)
  public CallActivityProgressView overlayProgress(
      @RequestParam
          @Parameter(
              description =
                  "key to identify which workflow-based dashboard to retrieve progress of",
              required = true)
          String key) {
    return service.overlayProgress(key);
  }

  @Operation(summary = "Overlay tasks break counts")
  @CommonErrors
  @PostMapping("/overlay/break-counts")
  @PreAuthorize(AUTHORITY_VIEW_VD_EXC_MANAGEMENT_RESULT)
  public BreakCountView overlayTaskBreakCounts(
      @RequestBody List<String> taskIds, @RequestParam("onlyCurvesWithBreaks") Boolean brokenOnly) {
    return service.overlayBreaksCount(taskIds, brokenOnly);
  }

  @Operation(summary = "Get overlay filters by task ids")
  @CommonErrors
  @PostMapping("/overlay/filters")
  @PreAuthorize(AUTHORITY_VIEW_VD_EXC_MANAGEMENT_RESULT)
  public IpvExceptionManagementCountedFiltersView getOverlayPermissibleTaskFilter(
      @RequestBody List<String> taskIds, @Valid TradeFilterForm filter) {
    return service.getTasksFilterForOverlay(taskIds, filter);
  }

  // NO LONGER USED
  @Operation(summary = "Get overlay break test values graphs by task ids")
  @CommonErrors
  @GetMapping("/overlay/graphs")
  @PreAuthorize(AUTHORITY_VIEW_VD_EXC_MANAGEMENT_RESULT)
  public List<IpvXmDistributionGraphView> getOverlayGraphs(
      @Valid TradeResultTasksBreaksFilterForm filter) {
    return graphsService.breaksDistributionGraphs(filter);
  }

  @Operation(summary = "Get overlay break test charts by task ids")
  @CommonErrors
  @PostMapping("/overlay/charts")
  @PreAuthorize(AUTHORITY_VIEW_VD_EXC_MANAGEMENT_RESULT)
  public IpvTaskChartData getOverlayCharts(@RequestBody List<String> taskIds) {
    return service.taskCharts(taskIds);
  }

  @Operation(summary = "Apply resolution for overlay")
  @FileUploadErrors
  @PostMapping(path = "/overlay/resolution", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @PreAuthorize(AUTHORITY_RUN_VD_TASK_EXECUTION)
  public ResponseEntity<List<EntityId>> applyOverlayResolution(
      @Validated @RequestPart("form") ApplyResolutionForm form,
      TableFilter tableFilter,
      @RequestPart(value = "evidence", required = false)
          @Valid
          @ValidDocumentSize
          @ValidDocumentType
          MultipartFile evidence,
      Authentication auth) {

    return eitherErrorItemResponse(service.applyResolution(auth, form, tableFilter, evidence));
  }

  @Operation(summary = "Verify resolution for overlay")
  @FileUploadErrors
  @PostMapping(path = "/overlay/verify-resolution", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @PreAuthorize(AUTHORITY_RUN_VD_TASK_EXECUTION)
  public ResponseEntity<List<EntityId>> verifyOverlayResolution(
      @Validated @RequestPart("form") ApplyVerificationForm form,
      @RequestPart(value = "evidence", required = false)
          @Valid
          @ValidDocumentSize
          @ValidDocumentType
          MultipartFile evidence,
      Authentication auth,
      TableFilter tableFilter) {
    return eitherErrorItemResponse(service.verifyResolutions(auth, form, evidence, tableFilter));
  }

  @Operation(summary = "Undo resolution for overlay")
  @CommonErrors
  @PostMapping("/overlay/undo-resolution")
  @PreAuthorize(AUTHORITY_RUN_VD_TASK_EXECUTION)
  public ResponseEntity<List<EntityId>> undoOverlayResolution(
      @Valid @RequestBody UndoResolutionForm form,
      @Valid ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter,
      Authentication auth) {
    return eitherErrorItemResponse(
        service.undoResolution(auth, form.taskIds(), displayFilter, tableFilter, form.resultIds()));
  }

  @Operation(summary = "Undo approval for overlay")
  @CommonErrors
  @PostMapping("/overlay/undo-approval")
  @PreAuthorize(AUTHORITY_RUN_VD_TASK_EXECUTION)
  public ResponseEntity<List<EntityId>> undoOverlayApproval(
      @Valid @RequestBody UndoApprovalForm form,
      @Valid ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter,
      Authentication auth) {
    return eitherErrorItemResponse(
        service.undoApproval(auth, form.taskIds(), displayFilter, tableFilter, form.resultIds()));
  }
}
