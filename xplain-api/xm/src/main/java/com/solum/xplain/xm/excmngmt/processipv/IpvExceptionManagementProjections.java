package com.solum.xplain.xm.excmngmt.processipv;

import static com.solum.xplain.core.common.AggregationUtils.AGGREGATION_VAR;
import static com.solum.xplain.core.common.AggregationUtils.AGGREGATION_VAR_REF;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.mongo.TernaryOperations.onlyWhenNotNull;
import static com.solum.xplain.xm.excmngmt.processipv.ThresholdLevelProjections.getIntegerAsThresholdLevelName;
import static org.springframework.data.mongodb.core.aggregation.AccumulatorOperators.Max.maxOf;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.ComparisonOperators.valueOf;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.IfNull.ifNull;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.when;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.VariableOperators.mapItemsOf;

import com.solum.xplain.core.portfolio.trade.OptionTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.value.WorkflowStatus;
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeResultBreakView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeResultResolutionView;
import com.solum.xplain.xm.excmngmt.processipv.view.TradeResultView;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import com.solum.xplain.xm.workflow.state.VdPhaseState;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.bson.Document;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IpvExceptionManagementProjections {

  public static ProjectionOperation wfIpvTradeOverlayResultViewProjection() {
    var tradePath = joinPaths(ProcessExecution.Fields.context, VdPhaseContext.Fields.trade);
    var breakTestsPath =
        joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.breakTestResults);

    ProjectionOperation baseFields =
        project(ProcessExecution.Fields.modifiedAt)
            .and(ProcessExecution.Fields.businessKey)
            .as(UNDERSCORE_ID)
            .and(ProcessExecution.Fields.businessKey)
            .as(IpvTradeResultOverlay.Fields.id)
            .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.entryStatus))
            .as(IpvTradeOverlayResultView.Fields.status)
            .and(
                when(valueOf(ProcessExecution.Fields.status).equalToValue(WorkflowStatus.HELD))
                    .thenValueOf(ProcessExecution.Fields.holdReason)
                    .otherwiseValueOf(
                        joinPaths(
                            ProcessExecution.Fields.currentState, VdPhaseState.Fields.entryStatus)))
            .as(IpvTradeOverlayResultView.Fields.statusDetail)
            .and(ProcessExecution.Fields.status)
            .as(IpvTradeOverlayResultView.Fields.stepStatus)
            .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.resolution))
            .as(
                joinPaths(
                    IpvTradeOverlayResultView.Fields.resolution,
                    IpvTradeResultResolutionView.Fields.resolution))
            .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.providerName))
            .as(
                joinPaths(
                    IpvTradeOverlayResultView.Fields.resolution,
                    IpvTradeResultResolutionView.Fields.providerName))
            .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.providerType))
            .as(
                joinPaths(
                    IpvTradeOverlayResultView.Fields.resolution,
                    IpvTradeResultResolutionView.Fields.providerType))
            .and(
                onlyWhenNotNull(
                        joinPaths(
                            ProcessExecution.Fields.currentState, VdPhaseState.Fields.resolution))
                    .thenValueOf(
                        // Try in order:
                        // 1) value pending approval (set after submitting resolution, cleared when
                        //    verified/rejected)
                        // 2) manual resolution value (set after applying resolution¹, if manual
                        //    override²),
                        // ¹: actually set on submit, but overridden using outcome when viewing as
                        //    resolver
                        // ²: null for other resolution types, so will display as empty
                        ifNull(
                                joinPaths(
                                    ProcessExecution.Fields.currentState,
                                    VdPhaseState.Fields.valuePendingApproval,
                                    AttributedValue.Fields.value))
                            .thenValueOf(
                                joinPaths(
                                    ProcessExecution.Fields.currentState,
                                    VdPhaseState.Fields.manualResolutionValue))))
            .as(
                joinPaths(
                    IpvTradeOverlayResultView.Fields.resolution,
                    IpvTradeResultResolutionView.Fields.value))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, VdPhaseState.Fields.resolutionComment))
            .as(
                joinPaths(
                    IpvTradeOverlayResultView.Fields.resolution,
                    IpvTradeResultResolutionView.Fields.resolutionComment))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, VdPhaseState.Fields.resolutionEvidence))
            .as(
                joinPaths(
                    IpvTradeOverlayResultView.Fields.resolution,
                    IpvTradeResultResolutionView.Fields.resolutionEvidence))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, VdPhaseState.Fields.approvalComment))
            .as(
                joinPaths(
                    IpvTradeOverlayResultView.Fields.resolution,
                    IpvTradeResultResolutionView.Fields.approvalComment))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, VdPhaseState.Fields.approvalEvidence))
            .as(
                joinPaths(
                    IpvTradeOverlayResultView.Fields.resolution,
                    IpvTradeResultResolutionView.Fields.approvalEvidence))
            .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.primary))
            .as(IpvTradeOverlayResultView.Fields.primaryProviderData)
            .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.secondary))
            .as(IpvTradeOverlayResultView.Fields.secondaryProviderData)
            .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.tertiary))
            .as(IpvTradeOverlayResultView.Fields.tertiaryProviderData)
            .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.quaternary))
            .as(IpvTradeOverlayResultView.Fields.quaternaryProviderData)
            .and(
                ifNull(
                        joinPaths(
                            ProcessExecution.Fields.currentState, VdPhaseState.Fields.approver))
                    .thenValueOf(
                        joinPaths(
                            ProcessExecution.Fields.currentState, VdPhaseState.Fields.resolver)))
            .as(IpvTradeOverlayResultView.Fields.modifiedBy)
            .and("[]") // do not return history as the front end doesn't want it and it is expensive
            .as(IpvTradeOverlayResultView.Fields.previousStatuses)
            .and(
                getIntegerAsThresholdLevelName(
                    maxOf(
                        joinPaths(
                            breakTestsPath,
                            TradeResultBreak.Fields.providerValue,
                            EntryResultBreakByProvider.Fields.triggeredThresholdLevel))))
            .as(IpvTradeOverlayResultView.Fields.maxTriggeredThresholdLevel)
            .and(joinPaths(ProcessExecution.Fields.context, VdPhaseContext.Fields.slaDeadline))
            .as(IpvTradeOverlayResultView.Fields.slaDeadline)
            .and(joinPaths(ProcessExecution.Fields.context, VdPhaseContext.Fields.pricingSlot))
            .as(IpvTradeOverlayResultView.Fields.pricingSlot)
            .and(
                joinPaths(
                    ProcessExecution.Fields.context, VdPhaseContext.Fields.calculationCurrency))
            .as(IpvTradeOverlayResultView.Fields.calculationCurrency);

    return addBreakTestFields(addTradeFields(baseFields, tradePath), breakTestsPath);
  }

  private static ProjectionOperation addTradeFields(
      ProjectionOperation baseFields, String tradePath) {
    var tradeOutPath = IpvTradeOverlayResultView.Fields.trade;
    return baseFields
        // Trade mapping
        .and(joinPaths(tradePath, Trade.Fields.externalTradeId))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.tradeInfoExternalTradeId))
        .and(joinPaths(tradePath, Trade.Fields.externalIdentifiers))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.tradeInfoExternalIdentifiers))
        .and(joinPaths(tradePath, Trade.Fields.customFields))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.tradeInfoCustomFields))
        .and(joinPaths(tradePath, Trade.Fields.entityId))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.entityId))
        .and(joinPaths(tradePath, Trade.Fields.portfolioId))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.portfolioId))
        .and(joinPaths(tradePath, Trade.Fields.productType))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.tradeInfoTradeType))
        .and(joinPaths(tradePath, Trade.Fields.productGroup))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.tradeInfoTradeTypeGroup))
        .and(joinPaths(tradePath, Trade.Fields.tradeDetails, TradeDetails.Fields.startDate))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.tradeInfoStartDate))
        .and(joinPaths(tradePath, Trade.Fields.tradeDetails, TradeDetails.Fields.endDate))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.tradeInfoEndDate))
        .and(
            joinPaths(
                tradePath,
                Trade.Fields.tradeDetails,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.expiryDate))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.tradeInfoExpiryDate))
        .and(joinPaths(tradePath, Trade.Fields.key))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.key))
        .and(joinPaths(tradePath, Trade.Fields.currency))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.currency))
        .and(joinPaths(tradePath, Trade.Fields.currencyPair))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.currencyPair))
        .and(joinPaths(tradePath, Trade.Fields.underlying))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.underlying))
        .and(joinPaths(tradePath, Trade.Fields.notional))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.notional))
        .and(joinPaths(tradePath, Trade.Fields.accountingCost))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.accountingCost))
        .and(joinPaths(tradePath, Trade.Fields.dealCost))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.dealCost))
        .and(joinPaths(tradePath, Trade.Fields.portfolioExternalId))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.portfolioExternalId))
        .and(joinPaths(tradePath, Trade.Fields.externalCompanyId))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.externalCompanyId))
        .and(joinPaths(tradePath, Trade.Fields.externalEntityId))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.externalEntityId))
        .and(joinPaths(tradePath, Trade.Fields.creditSector))
        .as(joinPaths(tradeOutPath, TradeResultView.Fields.creditSector));
  }

  private static ProjectionOperation addBreakTestFields(
      ProjectionOperation baseAndTradeFields, String breakTestsPath) {
    return baseAndTradeFields
        .and(ifNull(maxOf(joinPaths(breakTestsPath, TradeResultBreak.Fields.daysBreaking))).then(0))
        .as(IpvTradeOverlayResultView.Fields.maxDaysBreaking)
        // Break Test mapping
        .and(
            mapItemsOf(breakTestsPath)
                .as(AGGREGATION_VAR)
                .andApply(
                    aggregationOperationContext -> {
                      Document d = new Document();
                      d.append(
                          IpvTradeResultBreakView.Fields.breakTestName,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.breakTestName));
                      d.append(
                          IpvTradeResultBreakView.Fields.onboardingStatus,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.onboardingStatus));
                      d.append(
                          IpvTradeResultBreakView.Fields.breakTestType,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.breakTestType));
                      d.append(
                          IpvTradeResultBreakView.Fields.sequence,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.sequence));
                      d.append(
                          IpvTradeResultBreakView.Fields.hidden,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.hidden));
                      d.append(
                          IpvTradeResultBreakView.Fields.measureType,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.measureType));
                      d.append(
                          IpvTradeResultBreakView.Fields.operator,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.operator));
                      d.append(
                          IpvTradeResultBreakView.Fields.threshold,
                          ifNull(
                                  joinPaths(
                                      AGGREGATION_VAR_REF,
                                      TradeResultBreak.Fields.providerValue,
                                      EntryResultBreakByProvider.Fields.triggeredThreshold))
                              .thenValueOf(
                                  joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.threshold))
                              .toDocument(aggregationOperationContext));
                      d.append(
                          IpvTradeResultBreakView.Fields.thresholdLevel,
                          joinPaths(
                              AGGREGATION_VAR_REF,
                              TradeResultBreak.Fields.providerValue,
                              EntryResultBreakByProvider.Fields.triggeredThresholdLevel));
                      d.append(
                          IpvTradeResultBreakView.Fields.providerType,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.providerType));
                      d.append(
                          IpvTradeResultBreakView.Fields.triggered,
                          joinPaths(
                              AGGREGATION_VAR_REF,
                              TradeResultBreak.Fields.providerValue,
                              EntryResultBreakByProvider.Fields.triggered));
                      d.append(
                          IpvTradeResultBreakView.Fields.value,
                          joinPaths(
                              AGGREGATION_VAR_REF,
                              TradeResultBreak.Fields.providerValue,
                              EntryResultBreakByProvider.Fields.value));
                      d.append(
                          IpvTradeResultBreakView.Fields.daysBreaking,
                          joinPaths(AGGREGATION_VAR_REF, TradeResultBreak.Fields.daysBreaking));
                      return d;
                    }))
        .as(IpvTradeOverlayResultView.Fields.breakTests);
  }
}
