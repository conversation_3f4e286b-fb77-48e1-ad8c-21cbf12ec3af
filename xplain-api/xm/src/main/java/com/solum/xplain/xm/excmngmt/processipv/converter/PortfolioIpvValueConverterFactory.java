package com.solum.xplain.xm.excmngmt.processipv.converter;

import static com.opengamma.strata.basics.currency.Currency.USD;
import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;
import static com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementCalculationService.IPV_EXCEPTION_MANAGEMENT_RESULT_COLLECTION;
import static java.util.Collections.synchronizedList;
import static java.util.function.Function.identity;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.FxMatrix;
import com.opengamma.strata.basics.currency.FxMatrixBuilder;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.repository.CompanyLegalEntityValuationSettingsRepository;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView;
import com.solum.xplain.core.company.value.ValuationSettingsView;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView;
import com.solum.xplain.core.curvegroup.ratefx.CurveGroupFxRatesRepository;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeValueView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboardsteps.opvvaluations.DashboardMarketDataSourceResolver;
import com.solum.xplain.xm.excmngmt.processipv.value.PortfolioExceptionManagementData;
import com.solum.xplain.xm.settings.ExceptionManagementSettingsRepository;
import com.solum.xplain.xm.settings.IpvValueCurrencyType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NullMarked;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
@NullMarked
public class PortfolioIpvValueConverterFactory {
  private final CompanyLegalEntityValuationSettingsRepository valuationSettingsRepository;
  private final DashboardMarketDataSourceResolver mdSourceResolver;
  private final CurveConfigurationRepository curveConfigurationRepository;
  private final CurveGroupFxRatesRepository curveGroupFxRatesRepository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;
  private final ExceptionManagementSettingsRepository settingsRepository;
  private final AuditEntryService auditEntryService;

  public EntryStream<PortfolioIpvValueConverterParameters, PortfolioIpvValueConverter>
      allConverters(
          Set<PortfolioIpvValueConverterParameters> parameters, BitemporalDate stateDate) {
    var xmSettings = settingsRepository.exceptionManagementSettings(stateDate);
    var currencyType = xmSettings.getCurrencyType();
    var logs = synchronizedList(new ArrayList<ErrorItem>());
    return allConverters(currencyType, parameters, stateDate, logs::add);
  }

  public PortfolioIpvValueConverter converter(
      LocalDate valuationDate,
      CompanyEntityRecordData companyEntityRecordData,
      String externalPortfolioId,
      BitemporalDate stateDate) {
    var xmSettings = settingsRepository.exceptionManagementSettings(stateDate);
    var currencyType = xmSettings.getCurrencyType();
    var logs = synchronizedList(new ArrayList<ErrorItem>());
    var converter =
        converter(
            valuationDate,
            currencyType,
            companyEntityRecordData,
            externalPortfolioId,
            stateDate,
            logs::add);

    if (!logs.isEmpty()) {
      auditEntryService.newEntryWithLogs(
          AuditEntry.of(
              IPV_EXCEPTION_MANAGEMENT_RESULT_COLLECTION,
              "Failed to build NAV/NOTIONAL currency converter for portfolio %s"
                  .formatted(externalPortfolioId)),
          logs);
    }
    return converter;
  }

  PortfolioIpvValueConverter converter(
      LocalDate valuationDate,
      IpvValueCurrencyType currencyType,
      CompanyEntityRecordData companyEntityRecordData,
      String externalPortfolioId,
      BitemporalDate stateDate,
      Consumer<ErrorItem> logConsumer) {
    var settings =
        valuationSettingsRepository.getCompanyEntitySettingsView(
            companyEntityRecordData.companyId(),
            companyEntityRecordData.legalEntityId(),
            stateDate);

    return Steps.begin(validateSettings(externalPortfolioId, settings))
        .then(s -> curveConfigurationRepository.getView(s.getCurveConfigurationId(), stateDate))
        .yield(
            (s, cc) -> {
              var mdSource = mdSourceResolver.resolve(s.getMarketDataGroupId(), valuationDate);
              var curveConfigStateForm =
                  new CurveConfigMarketStateForm(
                      settings.getMarketDataGroupId(),
                      settings.getCurveConfigurationId(),
                      mdSource,
                      stateDate.getActualDate(),
                      valuationDate,
                      fxPriceRequirementsForm(settings.getPriceRequirements()));

              var marketData = marketDataQuotesSupport.getFullQuotes(curveConfigStateForm);

              var stateActualDate = stateDate.getActualDate();
              var fxRates =
                  curveGroupFxRatesRepository.getRatesNodesValuesViews(
                      cc.getCurveGroupId(), stateActualDate, marketData);
              return buildFxMatrix(Currency.of(settings.getReportingCurrency()), fxRates);
            })
        .map(fx -> converterByType(currencyType, settings, fx))
        .fold(
            err -> {
              logConsumer.accept(err);
              return converterByType(currencyType, settings, null);
            },
            converter -> converter);
  }

  static FxMatrix buildFxMatrix(
      Currency reportingCcy, VersionedList<CurveGroupFxRatesNodeValueView> ratesNodesValuesViews) {
    // Order is important here:
    // 1. Add USD rates (not including USD/reporting ccy rate) with USD as domestic currency.
    // 2. Add direct ReportingCcy/USD rate
    // 3. Add reporting ccy rates (not including USD/reporting ccy rate) with reporting ccy as
    // domestic currency.
    // This ensures that we have USD and reporting ccy rates in the matrix.
    // And that we triangulate with USD only if there is not a direct rate for the reporting ccy
    // and fails if none of these are available.
    FxMatrixBuilder fxMatrixBuilder = FxMatrix.builder();
    ratesNodesValuesViews.getList().stream()
        .filter(
            rate ->
                (fxRateContainsCurrency(rate, USD) || fxRateContainsCurrency(rate, reportingCcy))
                    && rate.getValue() != null)
        .sorted(prioritiseCcy(USD).thenComparing(prioritiseCcy(reportingCcy).reversed()))
        .forEach(
            rate -> {
              var domesticCcy = Currency.of(rate.getDomesticCurrency());
              var foreignCcy = Currency.of(rate.getForeignCurrency());
              double value = rate.getValue().doubleValue();
              if (reportingCcy.equals(foreignCcy)) {
                fxMatrixBuilder.addRate(foreignCcy, domesticCcy, 1.0d / value);
              } else if (reportingCcy.equals(domesticCcy)) {
                fxMatrixBuilder.addRate(domesticCcy, foreignCcy, value);
              } else if (USD.equals(foreignCcy)) {
                fxMatrixBuilder.addRate(foreignCcy, domesticCcy, 1.0d / value);
              } else {
                fxMatrixBuilder.addRate(domesticCcy, foreignCcy, value);
              }
            });
    return fxMatrixBuilder.build();
  }

  private PortfolioIpvValueConverter converterByType(
      @NonNull IpvValueCurrencyType currencyType,
      @NonNull CompanyLegalEntityValuationSettingsView settings,
      @Nullable FxMatrix fxMatrix) {
    var reportingCcy =
        settings.getReportingCurrency() == null
            ? null
            : Currency.of(settings.getReportingCurrency());
    return switch (currencyType) {
      case REPORTING_CCY -> new ReportingIpvValueCurrencyConverter(reportingCcy, fxMatrix);
      case TRADE_CCY -> new TradeCurrencyIpvValueConverter(reportingCcy, fxMatrix);
    };
  }

  private Either<ErrorItem, CompanyLegalEntityValuationSettingsView> validateSettings(
      String portfolioId, CompanyLegalEntityValuationSettingsView settings) {
    if (settings.getReportingCurrency() == null) {
      return buildError(portfolioId, "Reporting currency missing");
    }
    if (StringUtils.isEmpty(settings.getMarketDataGroupId())) {
      return buildError(portfolioId, "Market data group id missing");
    }
    if (settings.getCurveConfigurationId() == null) {
      return buildError(portfolioId, "Curve configuration id missing");
    }
    return Either.right(settings);
  }

  private Either<ErrorItem, CompanyLegalEntityValuationSettingsView> buildError(
      String portfolioId, String baseError) {
    return Either.left(
        OPERATION_NOT_ALLOWED.entity(
            "Cannot build NAV/NOTIONAL currency converter for portfolio %s: %s. NAV/NOTIONAL tests might be skipped"
                .formatted(portfolioId, baseError)));
  }

  private InstrumentPriceRequirementsForm fxPriceRequirementsForm(
      InstrumentPriceRequirements requirements) {
    var form = new InstrumentPriceRequirementsForm();
    form.setFxRatesPriceType(requirements.getFxRatesPriceType());
    return form;
  }

  private EntryStream<PortfolioIpvValueConverterParameters, PortfolioIpvValueConverter>
      allConverters(
          IpvValueCurrencyType currencyType,
          Collection<PortfolioIpvValueConverterParameters> parameters,
          BitemporalDate stateDate,
          Consumer<ErrorItem> logConsumer) {
    var valuationDate = stateDate.getActualDate();

    var allSettingsByLegalEntityId =
        valuationSettingsRepository.getCompanyLegalEntitySettings(
            parameters.stream().map(p -> p.data.legalEntityId).toList(), stateDate);

    var allSettingsByExternalPortfolioId =
        StreamEx.of(parameters)
            .mapToEntry(
                parameter -> parameter.portfolioExternalId,
                parameter -> allSettingsByLegalEntityId.get(parameter.data.legalEntityId()))
            .toMap();

    log.info(
        "Found {} settings for {} portfolios",
        allSettingsByExternalPortfolioId.size(),
        parameters.size());

    var validSettingsByExternalPortfolioId =
        EntryStream.of(allSettingsByExternalPortfolioId)
            .filter(entry -> validateSettings(entry.getKey(), entry.getValue()).isRight())
            .toMap();

    log.info(
        "Found {} valid settings for {} portfolios",
        validSettingsByExternalPortfolioId.size(),
        parameters.size());

    var curveConfigurationIds =
        StreamEx.of(validSettingsByExternalPortfolioId.values())
            .map(CompanyLegalEntityValuationSettingsView::getCurveConfigurationId)
            .toSet();
    var curveConfigurationViews =
        curveConfigurationRepository.getViews(curveConfigurationIds, stateDate);

    log.info(
        "Found {} curve configurations for {} portfolios",
        curveConfigurationViews.size(),
        parameters.size());

    var allMarketDataGroupIds =
        StreamEx.of(validSettingsByExternalPortfolioId.values())
            .map(ValuationSettingsView::getMarketDataGroupId)
            .toSet();

    log.info(
        "Found {} market data groups for {} portfolios",
        allMarketDataGroupIds.size(),
        parameters.size());

    var marketDataGroupTypeByMarketGroupId =
        mdSourceResolver.resolve(allMarketDataGroupIds, valuationDate);

    log.info(
        "Found {} market data sources for {} portfolios",
        marketDataGroupTypeByMarketGroupId.size(),
        parameters.size());

    Map<String, CurveConfigMarketStateForm> curveConfigMarketStateFormByExternalPortfolioId =
        EntryStream.of(validSettingsByExternalPortfolioId)
            .mapValues(
                setting ->
                    new CurveConfigMarketStateForm(
                        setting.getMarketDataGroupId(),
                        setting.getCurveConfigurationId(),
                        marketDataGroupTypeByMarketGroupId.get(setting.getMarketDataGroupId()),
                        valuationDate,
                        valuationDate,
                        fxPriceRequirementsForm(setting.getPriceRequirements())))
            .toMap();

    log.info(
        "Found {} curve config market state forms for {} portfolios",
        curveConfigMarketStateFormByExternalPortfolioId.size(),
        parameters.size());

    var distinctFormForMarketQuote =
        StreamEx.of(curveConfigMarketStateFormByExternalPortfolioId.values()).toSet();
    log.info(
        "Found {} distinct market data quote forms for {} portfolios",
        distinctFormForMarketQuote.size(),
        parameters.size());

    // TODO(SXSD-10632): Too complicated to properly batch it. (relies on the fact that there are
    // not that many
    // different CurveConfigMarketStateForm)
    var formToMarketQuote =
        StreamEx.of(distinctFormForMarketQuote)
            .mapToEntry(
                identity(),
                form -> {
                  try {
                    return marketDataQuotesSupport.getFullQuotes(form);
                  } catch (Exception e) {
                    throw new RuntimeException(e);
                  }
                })
            .toMap();
    log.info(
        "Done fetching market data quotes for {} portfolios... reassociating", parameters.size());

    Map<String, Map<String, CalculationMarketValueFullView>> marketDataQuoteByExternalPortfolioId =
        EntryStream.of(curveConfigMarketStateFormByExternalPortfolioId)
            .mapValues(formToMarketQuote::get)
            .toMap();

    log.info(
        "Found {} market data quotes for {} portfolios",
        marketDataQuoteByExternalPortfolioId.size(),
        parameters.size());

    var allCurveGroupIds =
        StreamEx.of(curveConfigurationViews.values())
            .map(it -> it.map(CurveConfigurationView::getCurveGroupId).getOrNull())
            .filter(Objects::nonNull)
            .toSet();

    log.info(
        "Found {} curve group ids for {} portfolios", allCurveGroupIds.size(), parameters.size());

    var fxRatesByCurveGroupId =
        curveGroupFxRatesRepository.findActiveRates(allCurveGroupIds, stateDate);

    log.info(
        "Found {} fx rates for {} portfolios", fxRatesByCurveGroupId.size(), parameters.size());

    return StreamEx.of(parameters)
        .mapToEntry(
            parameter -> {
              var externalPortfolioId = parameter.portfolioExternalId();
              var entityValuationSettings =
                  allSettingsByExternalPortfolioId.get(parameter.portfolioExternalId());
              return Steps.begin(validateSettings(externalPortfolioId, entityValuationSettings))
                  .then(s -> curveConfigurationViews.get(s.getCurveConfigurationId()))
                  .yield(
                      (s, cc) -> {
                        var marketData =
                            marketDataQuoteByExternalPortfolioId.get(externalPortfolioId);
                        var fxRates =
                            fxRatesByCurveGroupId
                                .get(cc.getCurveGroupId())
                                .map(
                                    curveGroupFxRates ->
                                        curveGroupFxRatesRepository.mergeCurveGroupFxRates(
                                            marketData, curveGroupFxRates))
                                .getOr(VersionedList::empty);
                        return buildFxMatrix(Currency.of(s.getReportingCurrency()), fxRates);
                      })
                  .map(fx -> converterByType(currencyType, entityValuationSettings, fx))
                  .fold(
                      err -> {
                        logConsumer.accept(err);
                        auditEntryService.newEntryWithLogs(
                            AuditEntry.of(
                                IPV_EXCEPTION_MANAGEMENT_RESULT_COLLECTION,
                                "Failed to build NAV/NOTIONAL currency converter for portfolio %s"
                                    .formatted(externalPortfolioId)),
                            List.of(err));
                        return converterByType(currencyType, entityValuationSettings, null);
                      },
                      converter -> converter);
            });
  }

  private static boolean fxRateContainsCurrency(CurveGroupFxRatesNodeValueView rate, Currency ccy) {
    return ccy.equals(Currency.of(rate.getDomesticCurrency()))
        || ccy.equals(Currency.of(rate.getForeignCurrency()));
  }

  private static Comparator<CurveGroupFxRatesNodeValueView> prioritiseCcy(Currency ccy) {
    return Comparator.comparing(
            (CurveGroupFxRatesNodeValueView rate) -> fxRateContainsCurrency(rate, ccy))
        .reversed();
  }

  public record CompanyEntityRecordData(String companyId, String legalEntityId) {

    public static CompanyEntityRecordData of(PortfolioExceptionManagementData xmData) {
      return new CompanyEntityRecordData(xmData.getCompanyId(), xmData.getLegalEntityId());
    }
  }

  public record PortfolioIpvValueConverterParameters(
      CompanyEntityRecordData data, String portfolioExternalId) {}
}
