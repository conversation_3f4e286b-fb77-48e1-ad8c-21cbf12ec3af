package com.solum.xplain.xm.excmngmt.processipv.data;

import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.data.WithBreakByProvider;
import com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType;
import com.solum.xplain.xm.excmngmt.rules.BreakTestParent;
import com.solum.xplain.xm.excmngmt.rules.value.Operator;
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.NonNull;

@Data
@FieldNameConstants
public class TradeResultBreak implements WithBreakByProvider, Serializable {

  private String breakTestId;
  private String breakTestName;
  private String breakTestType;
  private Integer sequence;
  private Boolean hidden;
  private Boolean onboardingTest;
  private OnboardingStatus onboardingStatus;

  private IpvMeasureType measureType;
  private Operator operator;
  private BigDecimal threshold;
  private IpvBreakProviderType providerType;
  private EntryResultBreakByProvider providerValue;
  private String parentBreakTestName;
  private int daysBreaking;

  public static TradeResultBreak tradeResult(
      @NonNull IpvBreakTest test,
      @NonNull IpvBreakProviderType providerType,
      @NonNull EntryResultBreakByProvider providerValue,
      @NonNull Trade trade,
      int daysBreaking,
      OnboardingStatus onboardingStatus) {
    var result = new TradeResultBreak();
    result.setBreakTestId(test.getId());
    result.setSequence(test.getSequence());
    result.setHidden(test.getSequence() == null);
    result.setOnboardingTest(Boolean.TRUE.equals(test.getOnboardingTest()));
    if (onboardingStatus != null) {
      result.setOnboardingStatus(onboardingStatus);
    }
    result.setBreakTestName(test.getName());
    result.setBreakTestType(test.getType().getName());
    result.setMeasureType(test.getMeasureType());
    result.setOperator(test.getOperator());
    result.setThreshold(test.resolveFirstThreshold(trade));
    result.setProviderType(providerType);
    result.setProviderValue(providerValue);
    Optional.ofNullable(test.getParentTest())
        .map(BreakTestParent::getName)
        .ifPresent(result::setParentBreakTestName);
    result.setDaysBreaking(daysBreaking);
    return result;
  }

  public static Comparator<TradeResultBreak> comparator() {
    return Comparator.comparing(
        TradeResultBreak::getSequence, Comparator.nullsFirst(Comparator.naturalOrder()));
  }

  @EqualsAndHashCode.Include(replaces = "threshold")
  private BigDecimal normalisedThreshold() {
    return threshold == null ? null : threshold.stripTrailingZeros();
  }
}
