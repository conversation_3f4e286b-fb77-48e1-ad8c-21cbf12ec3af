package com.solum.xplain.xm.excmngmt.processipv.value;

import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.PRIMARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.QUATERNARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.SECONDARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.TERTIARY;

import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak;
import com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType;
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest;
import com.solum.xplain.xm.excmngmt.value.EntryBreakHistory;
import java.math.BigDecimal;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class TradeChildBreakCalculator {

  @NonNull private final Map<IpvBreakProviderType, BigDecimal> parentTestValues;
  @NonNull private final Trade trade;
  @NonNull private final EntryBreakHistory breakHistory;
  @Nullable private final TradeBreakScalingData scalingData;

  public static TradeChildBreakCalculator newOf(
      @NonNull Map<IpvBreakProviderType, BigDecimal> providerValues,
      @NonNull Trade trade,
      @NonNull EntryBreakHistory breakHistory,
      @Nullable TradeBreakScalingData scalingData) {
    return new TradeChildBreakCalculator(providerValues, trade, breakHistory, scalingData);
  }

  public TradeResultBreak calculateChildTest(IpvBreakTest breakTest) {
    var entryResult = processTest(breakTest);
    var daysBreaking = breakHistory.daysBreaking(breakTest.getId(), entryResult.isTriggered());
    return TradeResultBreak.tradeResult(breakTest, PRIMARY, entryResult, trade, daysBreaking, null);
  }

  private EntryResultBreakByProvider processTest(IpvBreakTest test) {
    return switch (test.getType()) {
      case PRIMARY_VS_SECONDARY -> providerDiff(SECONDARY, test);
      case PRIMARY_VS_TERTIARY -> providerDiff(TERTIARY, test);
      case PRIMARY_VS_QUATERNARY -> providerDiff(QUATERNARY, test);
      default -> throw new IllegalStateException("Unexpected value: " + test.getType());
    };
  }

  private EntryResultBreakByProvider providerDiff(
      IpvBreakProviderType anotherProvider, IpvBreakTest test) {
    var resultResolver = test.resultResolver(trade);
    var providerValue = parentTestValues.get(PRIMARY);
    var anotherProviderValue = parentTestValues.get(anotherProvider);
    if (ObjectUtils.anyNull(providerValue, anotherProviderValue)) {
      return EntryResultBreakByProvider.ofEmpty();
    }

    return test.getMeasureType()
        .performMeasure(providerValue, anotherProviderValue, null, null, scalingData)
        .map(resultResolver::toBreakByProvider)
        .orElse(resultResolver.toBreakByProvider(null));
  }
}
