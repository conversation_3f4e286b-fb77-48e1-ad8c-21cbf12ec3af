package com.solum.xplain.xm.excmngmt.processipv.view;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView.FieldName;
import com.solum.xplain.xm.excmngmt.rules.value.Operator;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class IpvTradeResultBreakView {

  private String breakTestName;
  private String parentBreakTestName;
  private String breakTestType;

  @ConfigurableViewIgnore(comment = "Used to order break test columns for display")
  private Integer sequence;

  @ConfigurableViewIgnore(comment = "Used to hide a break test from display")
  private Boolean hidden;

  @ConfigurableViewIgnore(comment = "Used to display/colour rows based on onboarding test status")
  private String onboardingStatus;

  private IpvMeasureType measureType;
  private Operator operator;
  private BigDecimal threshold;

  @ConfigurableViewIgnore(
      comment = "Shared field with IpvTradeOverlayResultView.maxTriggeredThresholdLevel")
  private BigDecimal thresholdLevel;

  @ConfigurableViewField(FieldName.PARENT_BREAK_TEST_PROVIDER)
  private String providerType;

  @ConfigurableViewIgnore(comment = "Not included as a separate column any more")
  private boolean triggered;

  @ConfigurableViewField(FieldName.BREAK_TEST_COLUMNS)
  @ConfigurableViewField(FieldName.TEST_VALUE)
  private BigDecimal value;

  @ConfigurableViewIgnore(comment = "Shared field with IpvTradeOverlayResultView.maxDaysBreaking")
  private int daysBreaking;
}
