package com.solum.xplain.xm.workflow.repository.fragment;

import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.value.WorkflowStatus;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.process.view.BreakCountView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView;
import com.solum.xplain.xm.excmngmt.process.view.chart.TaskChartDataEntry;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import com.solum.xplain.xm.excmngmt.processipv.view.chart.IpvTaskChartDataEntry;
import com.solum.xplain.xm.tasks.entity.IpvTasksDefinition;
import com.solum.xplain.xm.tasks.entity.TasksDefinition;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;

public interface XmProcessExecutionQueries {

  BreakCountView getVdBreakCountView(
      List<String> taskGroups,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions,
      Boolean brokenOnly);

  BreakCountView getMdPreliminaryBreakCountView(
      List<String> taskGroups,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions,
      boolean breaksOnly);

  BreakCountView getMdOverlayBreakCountView(
      List<String> taskGroups, Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions);

  List<IpvTaskChartDataEntry> getVdChartData(
      List<String> taskGroups,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions);

  List<TaskChartDataEntry> getMdPreliminaryChartData(
      List<String> taskGroups, Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions);

  List<TaskChartDataEntry> getMdOverlayChartData(
      List<String> taskGroups, Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions);

  List<IpvTradeOverlayResultView> getIpvClearingViews(
      List<String> taskGroups,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions,
      boolean brokenOnly,
      boolean includeHeld,
      boolean onboardingOnly,
      Criteria postProjectionCriteria,
      Pageable page);

  List<InstrumentPreliminaryResultView> getPreliminaryClearingViews(
      List<String> taskGroups,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions,
      boolean brokenOnly,
      boolean includeHeld,
      Criteria postProjectionCriteria,
      Pageable page);

  List<InstrumentOverlayResultView> getOverlayClearingViews(
      List<String> taskGroups,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions,
      boolean brokenOnly,
      boolean includeHeld,
      Criteria postProjectionCriteria,
      Pageable page);

  List<ProcessExecution> findProcessExecutionsForTasks(
      List<String> taskGroups,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions,
      List<WorkflowStatus> excludeStatuses);

  public Stream<ProcessExecution> findDashboardsByStateDate(LocalDate stateDate);
}
