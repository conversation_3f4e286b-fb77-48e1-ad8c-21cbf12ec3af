package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView;
import com.solum.xplain.core.company.value.PortfolioSettings;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.workflow.value.SubprocessContextCreator;
import com.solum.xplain.xm.dashboardsteps.opvvaluations.DashboardMarketDataSourceResolver;
import com.solum.xplain.xm.excmngmt.processipv.CachingPortfolioSettingsService;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Calculation context creator which takes the collection of trades and creates one calculation
 * context for each portfolio with a trade which has Xplain as a provider.
 *
 * <p>TODO: this could be refined to pass the actual list of trades as part of the
 * DashboardCalculationForm instead of the product types. CalculationTradesFactory could then handle
 * this and that would go some way to satisfying ZOHO-249. One could then remove productTypes from
 * PricingSlotTradeWithProviders.
 */
@Component
@RequiredArgsConstructor
public class VdCalculationContextCreator
    implements SubprocessContextCreator<
        VdDashboardState, VdDashboardContext, VdCalculationState, VdCalculationContext> {
  private final CachingPortfolioSettingsService cachingPortfolioSettingsService;
  private final DashboardMarketDataSourceResolver marketDataSourceResolver;

  @Override
  public Stream<VdCalculationContext> subprocessContext(
      VdDashboardState parentState, VdDashboardContext parentContext) {
    var portfolioMap = parentContext.portfolioMap();

    cachingPortfolioSettingsService.prefetchPortfolioSettings(
        portfolioMap.values(), parentContext.stateDate());

    // Group the xplain provider trades by portfolio and identify the list of product types within
    // that portfolio
    return parentState.getTrades().stream()
        .filter(trade -> trade.providers().hasXplainProvider())
        .collect(
            Collectors.groupingBy(
                PricingSlotTradeWithProviders::portfolioId,
                Collectors.mapping(PricingSlotTradeWithProviders::productType, Collectors.toSet())))
        .entrySet()
        .stream()
        .map(
            entry -> {
              String portfolioId = entry.getKey();
              Set<ProductType> productTypes = entry.getValue();

              return toVdCalculationContext(
                  parentContext.stateDate(),
                  parentContext.principal(),
                  portfolioMap.get(portfolioId),
                  productTypes);
            });
  }

  private VdCalculationContext toVdCalculationContext(
      BitemporalDate stateDate,
      XplainPrincipal principal,
      PortfolioCondensedView portfolio,
      Set<ProductType> productTypes) {
    PortfolioSettings<CompanyLegalEntitySettingsView> settings =
        cachingPortfolioSettingsService
            .portfolioSettings(portfolio, stateDate)
            .getOrThrow(
                () -> new IllegalStateException("Failed to resolve portfolio valuation settings"));

    MarketDataSourceType dataSourceType =
        marketDataSourceResolver.resolve(
            settings.getSettings().getValuationSettings().getMarketDataGroupId(),
            stateDate.getActualDate());

    return new VdCalculationContext(
        AuditUser.of(principal),
        settings.getSettings().getValuationSettings(),
        portfolio,
        dataSourceType,
        stateDate,
        List.copyOf(productTypes));
  }

  @Override
  public Class<VdCalculationState> subprocessStateType() {
    return VdCalculationState.class;
  }

  @Override
  public String subprocessBusinessKey(String parentBusinessKey, VdCalculationContext context) {
    return parentBusinessKey + "-" + context.portfolio().getId();
  }
}
