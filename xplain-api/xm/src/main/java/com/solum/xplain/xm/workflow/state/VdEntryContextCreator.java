package com.solum.xplain.xm.workflow.state;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.SlaDeadlinePortfolioView;
import com.solum.xplain.workflow.value.SubprocessContextCreator;
import com.solum.xplain.xm.dashboards.CachingDashboardTradesService;
import com.solum.xplain.xm.excmngmt.processipv.CachingPortfolioIpvConverterService;
import com.solum.xplain.xm.excmngmt.processipv.CachingPortfolioSettingsService;
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverter;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import java.util.Map;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

/**
 * Initial state creator which takes the collection of trades from a dashboard and creates the
 * initial state for each individual trade workflow as a stream.
 */
@Component
@RequiredArgsConstructor
@NullMarked
public class VdEntryContextCreator
    implements SubprocessContextCreator<
        VdDashboardState, VdDashboardContext, VdEntryState, VdEntryContext> {

  private final CachingPortfolioIpvConverterService cachingPortfolioIpvConverterService;
  private final CachingDashboardTradesService dashboardTradesService;
  private final CachingPortfolioSettingsService cachingPortfolioSettingsService;

  @Override
  public Class<VdEntryState> subprocessStateType() {
    return VdEntryState.class;
  }

  @Override
  public String subprocessBusinessKey(String parentBusinessKey, VdEntryContext context) {
    return parentBusinessKey + "-" + context.trade().getKey();
  }

  @Override
  public Stream<VdEntryContext> subprocessContext(
      VdDashboardState dashboardState, VdDashboardContext dashboardContext) {
    Map<String, SlaDeadlinePortfolioView> portfolioMap = dashboardContext.portfolioMap();

    var cachedTrades =
        dashboardState.getTrades().stream()
            .map(
                pricingSlotTradeWithProviders ->
                    dashboardTradesService.dashboardTrade(
                        pricingSlotTradeWithProviders.portfolioId(),
                        pricingSlotTradeWithProviders.entityId(),
                        dashboardContext.stateDate()))
            .toList();
    cachingPortfolioIpvConverterService.preloadPortfolioIpvValueConverter(
        cachedTrades, dashboardContext.stateDate());

    return dashboardState.getTrades().stream()
        .map(
            trade ->
                toVdEntryContext(
                    dashboardContext.stateDate(), portfolioMap.get(trade.portfolioId()), trade));
  }

  private VdEntryContext toVdEntryContext(
      BitemporalDate stateDate,
      SlaDeadlinePortfolioView portfolio,
      PricingSlotTradeWithProviders trade) {
    var dashboardTrade =
        dashboardTradesService.dashboardTrade(trade.portfolioId(), trade.entityId(), stateDate);
    String marketDataGroupId;
    if (trade.providers().hasXplainProvider()) {
      marketDataGroupId =
          cachingPortfolioSettingsService
              .portfolioSettings(portfolio, stateDate)
              .map(s -> s.getSettings().getValuationSettings().getMarketDataGroupId())
              .getOrThrow(
                  () ->
                      new IllegalStateException("Failed to resolve portfolio valuation settings"));
    } else {
      marketDataGroupId = null;
    }
    return new VdEntryContext(
        stateDate,
        trade.vdg(),
        marketDataGroupId,
        trade.pricingSlot(),
        portfolio.getSlaDeadline(), // never null, see ChangeUnit033
        dashboardTrade,
        tradeCalculationCurrency(dashboardTrade, stateDate),
        trade.providers());
  }

  private String tradeCalculationCurrency(Trade trade, BitemporalDate stateDate) {
    PortfolioIpvValueConverter ipvValueConverter =
        cachingPortfolioIpvConverterService.getConverter(
            trade.getExternalCompanyId(),
            trade.getExternalEntityId(),
            trade.getPortfolioExternalId(),
            stateDate);

    return ipvValueConverter
        .xmCurrency(Currency.parse(trade.getCurrency()))
        .map(Currency::toString)
        .orElse(null);
  }
}
