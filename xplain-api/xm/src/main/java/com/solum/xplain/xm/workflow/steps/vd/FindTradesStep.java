package com.solum.xplain.xm.workflow.steps.vd;

import static com.solum.xplain.core.error.Error.OBJECT_ALREADY_EXISTS;
import static java.time.format.DateTimeFormatter.ofPattern;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.IpvSettingsRuleService;
import com.solum.xplain.core.company.mapper.IpvRuleFacts;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.core.company.value.ProvidersVo;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.error.WarningItem;
import com.solum.xplain.core.ipv.group.IpvDataGroupService;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupView;
import com.solum.xplain.core.rules.RulesService;
import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.dashboards.CachingDashboardTradesService;
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.workflow.repository.XmProcessExecutionQueryRepository;
import com.solum.xplain.xm.workflow.state.PricingSlotTradeWithProviders;
import com.solum.xplain.xm.workflow.state.VdDashboardContext;
import com.solum.xplain.xm.workflow.state.VdDashboardState;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import lombok.RequiredArgsConstructor;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.jspecify.annotations.NullMarked;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

/**
 * Workflow step to fetch all the trades for a dashboard using the portfolios and pricing slots on
 * the dashboard state. The list of trades with their associated pricing slots and providers is then
 * added to the state.
 */
@Component
@RequiredArgsConstructor
@NullMarked
public class FindTradesStep implements ServiceStepExecutor<VdDashboardState, VdDashboardContext> {
  private static final DateTimeFormatter FORMATTER = ofPattern("yyyy-MM-dd HH:mm:ss");

  private final IpvDataGroupService ipvDataGroupService;
  private final IpvSettingsRuleService ipvSettingsRuleService;
  private final CachingDashboardTradesService cachingDashboardTradesService;
  private final RulesService<Rule, Rules> rulesService;
  private final XmProcessExecutionQueryRepository xmProcessExecutionQueryRepository;
  private final AuditEntryService auditEntryService;

  /** Cache of VDG->PricingSlot since the rules only give us the VDG */
  private class VdgPricingSlotCache {
    Map<IpvDataGroupVo, PricingSlot> cache = new HashMap<>();

    /**
     * Check the cache for the VDG->PricingSlot mapping and add it if missing.
     *
     * @param vdg the valuation data group reference
     * @param stateDate relevant state date
     * @param principal user for access control
     * @return the pricing slot corresponding to the valuation data group, cached for performance.
     */
    PricingSlot vdgPricingSlot(
        IpvDataGroupVo vdg, BitemporalDate stateDate, XplainPrincipal principal) {
      return cache.computeIfAbsent(vdg, key -> pricingSlotForVdg(key, stateDate, principal));
    }

    private PricingSlot pricingSlotForVdg(
        IpvDataGroupVo vdg, BitemporalDate stateDate, XplainPrincipal principal) {
      // TODO: stateDate should ideally be used
      return ipvDataGroupService
          .get(principal, vdg.entityId())
          .map(IpvDataGroupView::getPricingSlot)
          .getOrNull();
    }
  }

  @Override
  public void runStep(StepStateOps<VdDashboardState, VdDashboardContext> ops) {
    final BitemporalDate stateDate = ops.getContext().stateDate();

    VdExceptionManagementPortfolioFilter portfolioFilter = ops.getContext().portfolioFilter();

    cachingDashboardTradesService.prefetchDashboardTrades(
        portfolioFilter.getPortfolios().keySet(), stateDate);

    // Get all the rules we need to refer to
    Rules vdgRules = ipvSettingsRuleService.getAllIpvDataGroupRules(stateDate);
    Rules providerRules = ipvSettingsRuleService.getAllIpvProvidersRules(stateDate);

    VdgPricingSlotCache vdgPricingSlotCache = new VdgPricingSlotCache();

    // find all trades for dashboards on stateDate, and remove duplicates from this dashboard
    Map<ProcessExecution<VdDashboardState, VdDashboardContext>, List<Trade>>
        removedTradesByDashboard = new HashMap<>();

    // TODO: This needs to be protected by a cluster lock to ensure we don't run 2 dashboards at the
    // same time - probably not here but at the workflow level
    var dashboardByExistingTradeId = collectDashboardsByExistingTradeId(ops);

    // Get all the trades for all the portfolios
    List<PricingSlotTradeWithProviders> trades =
        portfolioFilter.getPortfolios().keySet().stream()
            .mapMulti(
                (String portfolioId, Consumer<PricingSlotTradeWithProviders> out) ->
                    // For each portfolio, get all the trades for all product types
                    cachingDashboardTradesService
                        .dashboardTradesStream(portfolioId, stateDate)
                        .forEach(
                            trade -> {
                              // Run rules to find the ones which match the pricing slots for their
                              // portfolio
                              ipvDataGroupForTrade(trade, vdgRules)
                                  .ifPresent(
                                      vdg -> {
                                        XplainPrincipal principal = ops.getContext().principal();
                                        PricingSlot pricingSlot =
                                            vdgPricingSlotCache.vdgPricingSlot(
                                                vdg, stateDate, principal);
                                        if (portfolioFilter.matches(portfolioId, pricingSlot)) {
                                          if (dashboardByExistingTradeId.containsKey(
                                              trade.getEntityId())) {
                                            ProcessExecution dashboard =
                                                dashboardByExistingTradeId.get(trade.getEntityId());
                                            removedTradesByDashboard
                                                .computeIfAbsent(dashboard, k -> new ArrayList<>())
                                                .add(trade);
                                          } else {
                                            addTrade(out, trade, vdg, pricingSlot, providerRules);
                                          }
                                        }
                                      });
                            }))
            .toList();

    removedTradesByDashboard.entrySet().forEach(this::logOmittedTrades);

    boolean hasXplainProvider =
        trades.stream().anyMatch(trade -> trade.providers().hasXplainProvider());

    ops.setOutcome(
        new MutablePropertyValues()
            .add(VdDashboardState.Fields.trades, trades)
            .add(VdDashboardState.Fields.calculationNeeded, hasXplainProvider));
  }

  private Map<String, ProcessExecution<VdDashboardState, VdDashboardContext>>
      collectDashboardsByExistingTradeId(StepStateOps<VdDashboardState, VdDashboardContext> ops) {
    Map<String, ProcessExecution<VdDashboardState, VdDashboardContext>> dashboardByExistingTradeId =
        new HashMap<>();
    try (var dashboards =
        xmProcessExecutionQueryRepository.findDashboardsByStateDate(
            ops.getContext().stateDate().getActualDate())) {
      dashboards.forEach(
          dashboard -> {
            Collection<PricingSlotTradeWithProviders> dashboardTrades = getTrades(dashboard);
            dashboardTrades.forEach(
                trade -> {
                  dashboardByExistingTradeId.put(trade.entityId(), dashboard);
                });
          });
    }
    return dashboardByExistingTradeId;
  }

  private void logOmittedTrades(
      Entry<ProcessExecution<VdDashboardState, VdDashboardContext>, List<Trade>> entry) {
    List<? extends LogItem> logs =
        entry.getValue().stream()
            .map(
                trade -> {
                  return WarningItem.of(
                      OBJECT_ALREADY_EXISTS, trade.getExternalTradeId() + " omitted");
                })
            .toList();

    auditEntryService.newEntryWithLogs(
        AuditEntry.of(
            ProcessExecution.PROCESS_EXECUTION_COLLECTION,
            "Omitting duplicate trades from dashboard created by "
                + entry.getKey().getContext().principal().getName()
                + " at "
                + entry.getKey().getContext().stateDate().getRecordDate().format(FORMATTER)),
        logs);
  }

  private Collection<PricingSlotTradeWithProviders> getTrades(
      ProcessExecution<VdDashboardState, VdDashboardState> execution) {
    return execution.getCurrentState().getTrades();
  }

  /**
   * Add the trade to the output, first running the provider rules on the trade. If there are no
   * providers then it isn't added.
   *
   * @param out output from {@link java.util.stream.Stream#mapMulti(BiConsumer)}
   * @param trade the trade to add
   * @param vdg the valuation data group determined for the trade
   * @param pricingSlot the pricing slot determined for the trade
   * @param providerRules the rules to run to determine providers for the trade
   */
  private void addTrade(
      Consumer<PricingSlotTradeWithProviders> out,
      Trade trade,
      IpvDataGroupVo vdg,
      PricingSlot pricingSlot,
      Rules providerRules) {
    providersForTrade(trade, providerRules)
        .ifPresent(
            providers ->
                // If we've got providers, add the trade to
                // the list to be processed
                out.accept(
                    new PricingSlotTradeWithProviders(
                        vdg,
                        pricingSlot,
                        trade.getPortfolioId(),
                        trade.getEntityId(),
                        trade.getProductType(),
                        providers)));
  }

  /**
   * Use the rules to find the valuation data group for a trade.
   *
   * @param trade the trade to find the VDG for
   * @param rules the rules to execute to determine it
   * @return the reference to the valuation data group
   */
  private Optional<IpvDataGroupVo> ipvDataGroupForTrade(Trade trade, Rules rules) {
    return rulesService.execute(rules, new IpvRuleFacts(trade), IpvDataGroupVo.class);
  }

  /**
   * Use the rules to find the providers for a trade.
   *
   * @param trade the trade to find the providers for
   * @param rules the rules to execute to determine it
   * @return the valuation data providers for the trade
   */
  private Optional<ProvidersVo> providersForTrade(Trade trade, Rules rules) {
    return rulesService.execute(rules, new IpvRuleFacts(trade), ProvidersVo.class);
  }
}
