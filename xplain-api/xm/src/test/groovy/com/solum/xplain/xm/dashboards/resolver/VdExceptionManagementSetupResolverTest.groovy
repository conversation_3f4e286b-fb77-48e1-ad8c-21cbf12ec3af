package com.solum.xplain.xm.dashboards.resolver

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.COMPANY
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.ENTITY
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_SETTINGS
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VALUATION_DATA_GROUP
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VALUATION_DATA_GROUP_ANOTHER

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.CompanyPortfolioSettingsResolver
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedView
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter
import com.solum.xplain.xm.dashboards.validator.VdExceptionManagementSetupValidator
import spock.lang.Specification

class VdExceptionManagementSetupResolverTest extends Specification {

  private static final BitemporalDate BITEMPORAL_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  CompanyPortfolioSettingsResolver settingsResolver = Mock()
  IpvDataGroupRepository ipvDataGroupRepository = Mock()
  VdExceptionManagementSetupValidator vdSetupValidator = Mock()

  VdExceptionManagementSetupResolver resolver = new VdExceptionManagementSetupResolver(
  settingsResolver,
  ipvDataGroupRepository,
  vdSetupValidator
  )

  def "should resolve setup for portfolios filter"() {
    setup:
    1 * settingsResolver.portfoliosSettings([PORTFOLIO.entityId] as Set, BITEMPORAL_STATE_DATE) >> [PORTFOLIO_SETTINGS]
    1 * vdSetupValidator.validate(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, _) >> List.of()
    1 * ipvDataGroupRepository.allIpvDataGroupCondensedViews() >> [
      new IpvDataGroupCondensedView(id: VALUATION_DATA_GROUP.entityId, name: VALUATION_DATA_GROUP.name, pricingSlot: PricingSlot.LDN_1500),
      new IpvDataGroupCondensedView(id: VALUATION_DATA_GROUP_ANOTHER.entityId, name: VALUATION_DATA_GROUP_ANOTHER.name, pricingSlot: PricingSlot.LDN_1200),
    ]
    def filter = VdExceptionManagementPortfolioFilter.newOf(
      [(PORTFOLIO.entityId): [PricingSlot.LDN_1200, PricingSlot.LDN_1500]],
      [])
    when:
    def result = resolver.resolve(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, filter)

    then:
    result.isRight()

    result.getOrNull().portfoliosFilter == filter
    result.getOrNull().ipvValuationSettings.size() == 1

    def settings = result.getOrNull().ipvValuationSettings
    with(settings[0]) {
      company == COMPANY
      entity == ENTITY
      portfolio == PORTFOLIO
      slaDeadline == SlaDeadline.LDN_1615
      marketDataGroup.entityId == MARKET_DATA_GROUP.entityId
      marketDataGroup.name == MARKET_DATA_GROUP.name
      def sortedProducts = ipvDataGroupProducts.toSorted({ t -> t.ipvDataGroup.entityId })
      sortedProducts.size() == 2
      sortedProducts[0].products[CoreProductType.IRS] != null
      sortedProducts[0].products[CoreProductType.IRS].ipvDataGroupView.entityId == VALUATION_DATA_GROUP.entityId
      sortedProducts[0].products[CoreProductType.IRS].ipvDataGroupView.name == VALUATION_DATA_GROUP.name
      sortedProducts[1].products[CoreProductType.XCCY] != null
      sortedProducts[1].products[CoreProductType.XCCY].ipvDataGroupView.entityId == VALUATION_DATA_GROUP_ANOTHER.entityId
      sortedProducts[1].products[CoreProductType.XCCY].ipvDataGroupView.name == VALUATION_DATA_GROUP_ANOTHER.name
      hasXplainProvider()
    }
  }
}
