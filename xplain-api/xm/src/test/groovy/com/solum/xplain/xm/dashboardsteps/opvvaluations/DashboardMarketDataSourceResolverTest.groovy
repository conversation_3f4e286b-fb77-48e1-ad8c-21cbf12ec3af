package com.solum.xplain.xm.dashboardsteps.opvvaluations

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_BATCH_PRELIMINARY_CLEARING
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_OVERLAY_CLEARING
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_PRELIMINARY_CLEARING
import static com.solum.xplain.xm.dashboards.enums.StepStatus.COMPLETED
import static com.solum.xplain.xm.dashboards.enums.StepStatus.FAILED

import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.tasks.view.summary.TaskExecutionSummaryView
import com.solum.xplain.xm.workflow.repository.XmTasksDefinitionQueryRepository
import java.time.LocalDate
import one.util.streamex.EntryStream
import spock.lang.Specification

class DashboardMarketDataSourceResolverTest extends Specification {

  private static final String MARKET_DATA_GROUP_ID = MARKET_DATA_GROUP.entityId
  private static final LocalDate DATE = DASHBOARD_DATE

  DashboardRepository dashboardRepository = Mock()
  DashboardEntryRepository dashboardEntryRepository = Mock()
  XmTasksDefinitionQueryRepository xmTasksDefinitionQueryRepository = Mock()

  DashboardMarketDataSourceResolver resolver = new DashboardMarketDataSourceResolver(dashboardRepository, dashboardEntryRepository, xmTasksDefinitionQueryRepository)

  def "should correctly resolve legacy market data source #marketDataSource"() {
    setup:
    1 * dashboardRepository.getSingleDayMDDashboard(MARKET_DATA_GROUP_ID, DATE) >> Optional.of(new Dashboard(id: "ID"))
    1 * dashboardEntryRepository.getMdEntries("ID") >> steps
    1 * xmTasksDefinitionQueryRepository.getDashboardTimelineTaskViews(_, _, _, _) >> List.of()

    expect:
    resolver.resolve(MARKET_DATA_GROUP_ID, DATE) == marketDataSource

    where:
    steps                                                                          | marketDataSource
    [new DashboardEntryMd(step: MD_OVERLAY_CLEARING, status: COMPLETED)]           | MarketDataSourceType.OVERLAY
    [new DashboardEntryMd(step: MD_PRELIMINARY_CLEARING, status: COMPLETED)]       | MarketDataSourceType.RAW_PRIMARY
    [new DashboardEntryMd(step: MD_BATCH_PRELIMINARY_CLEARING, status: COMPLETED)] | MarketDataSourceType.RAW_PRIMARY
    []                                                                             | MarketDataSourceType.RAW_PRIMARY
    [new DashboardEntryMd(step: MD_BATCH_PRELIMINARY_CLEARING, status: FAILED)]    | MarketDataSourceType.RAW_PRIMARY
  }


  def "should correctly resolve market data source #marketDataSource"() {
    setup:
    1 * dashboardRepository.getSingleDayMDDashboard(MARKET_DATA_GROUP_ID, DATE) >> Optional.of(new Dashboard(id: "ID"))
    0 * dashboardEntryRepository.getMdEntries("ID") >> steps
    1 * xmTasksDefinitionQueryRepository.getDashboardTimelineTaskViews(_, _, _, _) >> tasks

    expect:
    resolver.resolve(MARKET_DATA_GROUP_ID, DATE) == marketDataSource

    where:
    steps                                                                          | tasks                                   | marketDataSource
    [new DashboardEntryMd(step: MD_PRELIMINARY_CLEARING, status: COMPLETED)]       | List.of(Mock(TaskExecutionSummaryView)) | MarketDataSourceType.OVERLAY
  }

  def "should resolve market data source types for multiple group IDs"() {
    given:
    def groupId1 = "group1"
    def groupId2 = "group2"
    def groupId3 = "group3"
    def date = LocalDate.now()
    def dashboard1 = new Dashboard(id: groupId1)
    def dashboard2 = new Dashboard(id: groupId2)

    // Mock dashboards returned for group1 and group2, none for group3
    1 * dashboardRepository.getSingleDayMDDashboard([groupId1, groupId2, groupId3] as Set, date) >>
    EntryStream.of([(groupId1): dashboard1, (groupId2): dashboard2])

    // group1 is overlay, group2 is not
    1 * xmTasksDefinitionQueryRepository.getDashboardTimelineTaskViews("urn:dashboard:$groupId1", _, _, _) >> [Mock(TaskExecutionSummaryView)]
    1 * xmTasksDefinitionQueryRepository.getDashboardTimelineTaskViews("urn:dashboard:$groupId2", _, _, _) >> []

    0 * dashboardEntryRepository.getMdEntries(groupId1) >> []
    1 * dashboardEntryRepository.getMdEntries(groupId2) >> []

    when:
    def result = resolver.resolve([groupId1, groupId2, groupId3] as Set, date)

    then:
    result.size() == 3
    result[groupId1] == MarketDataSourceType.OVERLAY
    result[groupId2] == MarketDataSourceType.RAW_PRIMARY
    result[groupId3] == MarketDataSourceType.RAW_PRIMARY // No dashboard exists, but still returns something.
  }
}
