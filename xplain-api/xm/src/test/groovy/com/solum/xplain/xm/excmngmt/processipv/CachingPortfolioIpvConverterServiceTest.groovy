package com.solum.xplain.xm.excmngmt.processipv

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.entity.CompanyLegalEntity
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverter
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverterFactory
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverterFactory.CompanyEntityRecordData
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverterFactory.PortfolioIpvValueConverterParameters
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.stream.Stream
import one.util.streamex.EntryStream
import org.springframework.data.util.Pair
import spock.lang.Specification

class CachingPortfolioIpvConverterServiceTest extends Specification {

  def converterFactory = Mock(PortfolioIpvValueConverterFactory)
  def companyRepository = Mock(CompanyRepository)
  def companyLegalEntityRepository = Mock(CompanyLegalEntityRepository)
  def service = new CachingPortfolioIpvConverterService(
  converterFactory, companyRepository, companyLegalEntityRepository, new SimpleMeterRegistry()
  )

  def "preloadPortfolioIpvValueConverter should load all caches and converters"() {
    given:
    def trade1 = Mock(Trade) {
      getExternalCompanyId() >> "ext-comp-1"
      getExternalEntityId() >> "ext-entity-1"
      getPortfolioExternalId() >> "portfolio-1"
    }
    def trade2 = Mock(Trade) {
      getExternalCompanyId() >> "ext-comp-2"
      getExternalEntityId() >> "ext-entity-2"
      getPortfolioExternalId() >> "portfolio-2"
    }
    def trades = [trade1, trade2]
    def stateDate = BitemporalDate.newOfNow()

    // CompanyRepository returns Company with id for each external id
    companyRepository.findByExternalIds(["ext-comp-1", "ext-comp-2"] as Set) >> Stream.of(
    new Company(id: "comp-1", externalCompanyId: "ext-comp-1"),
    new Company(id: "comp-2", externalCompanyId: "ext-comp-2")
    )

    // CompanyLegalEntityRepository returns LegalEntity with id for each company/entity id
    companyLegalEntityRepository.findByCompanyIdAndExternalIds(
      [Pair.of("comp-1", "ext-entity-1"), Pair.of("comp-2", "ext-entity-2")] as Set
      ) >> Stream.of(
      new CompanyLegalEntity(id: "le-1", companyId: "comp-1", externalId: "ext-entity-1"),
      new CompanyLegalEntity(id: "le-2", companyId: "comp-2", externalId: "ext-entity-2")
      )

    def converter1 = Mock(PortfolioIpvValueConverter)
    def converter2 = Mock(PortfolioIpvValueConverter)

    def params1 = new PortfolioIpvValueConverterParameters(
      new CompanyEntityRecordData("comp-1", "le-1"),
      "portfolio-1"
      )
    def params2 = new PortfolioIpvValueConverterParameters(
      new CompanyEntityRecordData("comp-2", "le-2"),
      "portfolio-2"
      )
    // Converter factory returns a mock converter for each key
    converterFactory.allConverters(_ as Set, stateDate) >> EntryStream.of([
      (params1): converter1,
      (params2): converter2])

    when:
    service.preloadPortfolioIpvValueConverter(trades, stateDate)
    def converterResult1 = service.getConverter("ext-comp-1", "ext-entity-1", "portfolio-1", stateDate)
    def converterResult2 = service.getConverter("ext-comp-2", "ext-entity-2", "portfolio-2", stateDate)

    then:
    noExceptionThrown()
    converterResult1 == converter1
    converterResult2 == converter2
  }
}
