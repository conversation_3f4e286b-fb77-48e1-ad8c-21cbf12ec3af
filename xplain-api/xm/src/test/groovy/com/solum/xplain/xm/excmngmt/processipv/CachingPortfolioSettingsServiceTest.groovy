package com.solum.xplain.xm.excmngmt.processipv

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.CompanyPortfolioSettingsResolver
import com.solum.xplain.core.company.value.CompanyLegalEntityIpvSettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.company.value.PortfolioSettings
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.value.PortfolioView
import io.atlassian.fugue.Either
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import spock.lang.Specification

class CachingPortfolioSettingsServiceTest extends Specification {

  def resolver = Mock(CompanyPortfolioSettingsResolver)
  def service = new CachingPortfolioSettingsService(resolver, new SimpleMeterRegistry())

  def "portfolioSettings returns cached value (single)"() {
    given:
    def portfolio = portfolioView(1)
    def stateDate = BitemporalDate.newOfNow()
    def companyLegalEntitySettings = CompanyLegalEntitySettingsView.newOf(new CompanyLegalEntityValuationSettingsView(), new CompanyLegalEntityIpvSettingsView())
    def settings = PortfolioSettings<CompanyLegalEntitySettingsView>.settings(portfolio, companyLegalEntitySettings)
    def result = Either.<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>right(settings)

    when:
    def first = service.portfolioSettings(portfolio, stateDate) // should call the solver
    def second = service.portfolioSettings(portfolio, stateDate) // should be in cache

    then:
    first == result
    second == result
    1 * resolver.portfolioSettings("C1", "E1", "P1", stateDate) >> result
  }

  def "portfolioSettings returns cached value (bulk)"() {
    given:
    def portfolio1 = portfolioView(1)
    def portfolio2 = portfolioView(2)
    def stateDate = BitemporalDate.newOfNow()
    def companyLegalEntitySettings = CompanyLegalEntitySettingsView.newOf(new CompanyLegalEntityValuationSettingsView(), new CompanyLegalEntityIpvSettingsView())
    def settings1 = PortfolioSettings<CompanyLegalEntitySettingsView>.settings(portfolio1, companyLegalEntitySettings)
    def settings2 = PortfolioSettings<CompanyLegalEntitySettingsView>.settings(portfolio2, companyLegalEntitySettings)
    def result1 = Either.<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>right(settings1)
    def result2 = Either.<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>right(settings2)

    when:
    service.prefetchPortfolioSettings([portfolio1, portfolio2], stateDate)
    def first = service.portfolioSettings(portfolio1, stateDate) // should call the solver
    def second = service.portfolioSettings(portfolio2, stateDate) // should be in cache

    then:
    first == result1
    second == result2
    0 * resolver.portfolioSettings(_, _, _, _) // should not call the single variant
    1 * resolver.portfoliosSettings([portfolio1.getId(), portfolio2.getId()] as Set, stateDate) >> [settings1, settings2]
  }

  def "portfolioSettings still be able to fetch singles after bulk"() {
    given:
    def portfolio1 = portfolioView(1)
    def portfolio2 = portfolioView(2)
    def portfolio3 = portfolioView(3)
    def stateDate = BitemporalDate.newOfNow()
    def companyLegalEntitySettings = CompanyLegalEntitySettingsView.newOf(new CompanyLegalEntityValuationSettingsView(), new CompanyLegalEntityIpvSettingsView())
    def settings1 = PortfolioSettings<CompanyLegalEntitySettingsView>.settings(portfolio1, companyLegalEntitySettings)
    def settings2 = PortfolioSettings<CompanyLegalEntitySettingsView>.settings(portfolio2, companyLegalEntitySettings)
    def settings3 = PortfolioSettings<CompanyLegalEntitySettingsView>.settings(portfolio3, companyLegalEntitySettings)
    def result1 = Either.<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>right(settings1)
    def result2 = Either.<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>right(settings2)
    def result3 = Either.<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>right(settings3)

    when:
    service.prefetchPortfolioSettings([portfolio1, portfolio2], stateDate) // prefetch 1 and 2, but not 3
    def first = service.portfolioSettings(portfolio1, stateDate) // should call the solver
    def second = service.portfolioSettings(portfolio2, stateDate) // should be in cache
    def third = service.portfolioSettings(portfolio3, stateDate) // should call the single variant

    then:
    first == result1
    second == result2
    third == result3
    1 * resolver.portfolioSettings("C3", "E3", "P3", stateDate) >> result3
    1 * resolver.portfoliosSettings([portfolio1.getId(), portfolio2.getId()] as Set, stateDate) >> [settings1, settings2]
  }

  private static PortfolioView portfolioView(int number) {
    new PortfolioView(
    id: "P" + number,
    companyId: "C" + number,
    entityId: "E" + number,
    externalCompanyId: "EC" + number,
    externalEntityId: "EE" + number,
    externalPortfolioId: "EP" + number
    )
  }
}
