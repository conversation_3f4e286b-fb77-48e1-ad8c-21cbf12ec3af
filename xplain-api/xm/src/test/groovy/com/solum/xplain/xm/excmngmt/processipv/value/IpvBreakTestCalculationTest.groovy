package com.solum.xplain.xm.excmngmt.processipv.value

import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN
import static java.math.BigDecimal.valueOf

import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.process.value.EntryResultCustomResolver
import com.solum.xplain.xm.excmngmt.process.value.EntryResultResolver
import com.solum.xplain.xm.excmngmt.processipv.data.OnboardingStatus
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType
import com.solum.xplain.xm.excmngmt.rules.BreakTestParent
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType
import com.solum.xplain.xm.excmngmt.value.BreakTestHistory
import com.solum.xplain.xm.excmngmt.value.EntryBreakHistory
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class IpvBreakTestCalculationTest extends Specification {

  private static final String BREAK_TEST_NAME = "TEST"
  def VALUATION_DATE = LocalDate.now()

  def "should correctly resolve no breaks when trade does not match"() {
    setup:
    def calc = Mock(TradeBreakCalculator)
    def trade = Mock(Trade)

    def breakTest = Mock(IpvBreakTest)
    breakTest.matches(trade) >> false

    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)

    when:
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 0
  }

  @Unroll
  def "should return applicable = #expected when enabled trade matches trade = #matches"() {
    setup:
    def trade = Mock(Trade)

    def breakTest = Mock(IpvBreakTest)
    breakTest.matches(trade) >> matches
    breakTest.getEnabled() >> Boolean.TRUE

    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)

    when:
    def applicable = calculation.isApplicable(trade)

    then:
    calculation.isRelevant()
    applicable == expected

    where:
    matches || expected
    false   || false
    true    || true
  }

  def "should return not applicable or relevant when test is not enabled and has no dependent tests"() {
    setup:
    def trade = Mock(Trade)

    def breakTest = Mock(IpvBreakTest)
    breakTest.matches(trade) >> true
    breakTest.getEnabled() >> Boolean.FALSE

    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)

    when:
    def relevant = calculation.isRelevant()
    def applicable = calculation.isApplicable(trade)

    then:
    !relevant
    !applicable
  }

  def "should return applicable and relevant when test is not enabled but has dependent tests"() {
    setup:
    def trade = Mock(Trade)

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEntityId() >> "EntityId"
    breakTest.matches(trade) >> true
    breakTest.getEnabled() >> Boolean.FALSE

    def dependentTest = Mock(IpvBreakTest)
    def parentTest = Mock(BreakTestParent)
    dependentTest.getParentTest() >> parentTest
    parentTest.getParentId() >> breakTest.getEntityId()
    dependentTest.getEnabled() >> Boolean.TRUE

    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    calculation.acceptDependant(dependentTest)

    when:
    def relevant = calculation.isRelevant()
    def applicable = calculation.isApplicable(trade)

    then:
    relevant
    applicable
  }

  def "should correctly resolve NULL_VALUE breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.NULL_VALUE
    breakTest.getSequence() >> sequence
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> null
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(IpvProvidersType.P2) >> true
    breakTest.isForProvider(IpvProvidersType.P3) >> false
    breakTest.isForProvider(IpvProvidersType.P4) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.SECONDARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.QUATERNARY) >> false

    def primaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.nullProvider(
      IpvProvidersType.P1,
      _ as EntryResultCustomResolver
      ) >> primaryResult

    def secondaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.nullProvider(
      IpvProvidersType.P2,
      EntryResultCustomResolver.calculationOnly()
      ) >> secondaryResult

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 2
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "NULL"
    breaks[0].sequence == sequence
    breaks[0].hidden == hidden
    breaks[0].measureType == null
    breaks[0].operator == null
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == primaryResult

    breaks[1].breakTestName == BREAK_TEST_NAME
    breaks[1].breakTestType == "NULL"
    breaks[1].sequence == sequence
    breaks[1].hidden == hidden
    breaks[1].measureType == null
    breaks[1].operator == null
    breaks[1].providerType == IpvBreakProviderType.SECONDARY
    breaks[1].providerValue == secondaryResult

    where:
    sequence || hidden
    1        || false
    null     || true
  }

  def "should correctly resolve ZERO_VALUE breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.ZERO_VALUE
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> null
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(IpvProvidersType.P2) >> true
    breakTest.isForProvider(IpvProvidersType.P3) >> false
    breakTest.isForProvider(IpvProvidersType.P4) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.SECONDARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.QUATERNARY) >> false

    def primaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.zeroValue(
      IpvProvidersType.P1,
      _ as EntryResultCustomResolver
      ) >> primaryResult

    def secondaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.zeroValue(
      IpvProvidersType.P2,
      EntryResultCustomResolver.calculationOnly()
      ) >> secondaryResult

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 2
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Zero"
    breaks[0].measureType == null
    breaks[0].operator == null
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == primaryResult

    breaks[1].breakTestName == BREAK_TEST_NAME
    breaks[1].breakTestType == "Zero"
    breaks[1].measureType == null
    breaks[1].operator == null
    breaks[1].providerType == IpvBreakProviderType.SECONDARY
    breaks[1].providerValue == secondaryResult
  }

  def "should correctly resolve STALE breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.STALE_VALUE
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> null
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(IpvProvidersType.P2) >> true
    breakTest.isForProvider(IpvProvidersType.P3) >> true
    breakTest.getOperator() >> null
    breakTest.resolveFirstThreshold(trade) >> ONE
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.SECONDARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.TERTIARY) >> true

    def primaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.stale(
      IpvProvidersType.P1,
      ONE.intValue(),
      _ as EntryResultCustomResolver
      ) >> primaryResult

    def secondaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.stale(
      IpvProvidersType.P2,
      ONE.intValue(),
      EntryResultCustomResolver.calculationOnly()
      ) >> secondaryResult

    def tertiaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.stale(
      IpvProvidersType.P3,
      ONE.intValue(),
      EntryResultCustomResolver.calculationOnly()
      ) >> tertiaryResult

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 3
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Stale"
    breaks[0].measureType == null
    breaks[0].operator == null
    breaks[0].threshold == ONE
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == primaryResult

    breaks[1].breakTestName == BREAK_TEST_NAME
    breaks[1].breakTestType == "Stale"
    breaks[1].measureType == null
    breaks[1].operator == null
    breaks[1].threshold == ONE
    breaks[1].providerType == IpvBreakProviderType.SECONDARY
    breaks[1].providerValue == secondaryResult

    breaks[2].breakTestName == BREAK_TEST_NAME
    breaks[2].breakTestType == "Stale"
    breaks[2].measureType == null
    breaks[2].operator == null
    breaks[2].threshold == ONE
    breaks[2].providerType == IpvBreakProviderType.TERTIARY
    breaks[2].providerValue == tertiaryResult
  }

  def "should correctly resolve VALUE breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.VALUE
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(IpvProvidersType.P2) >> true
    breakTest.getOperator() >> Operator.EQ
    2 * breakTest.resolveFirstThreshold(trade) >> ONE
    def resolver = Operator.EQ.resolver([ONE])
    1 * breakTest.resultResolver(trade) >> resolver
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.SECONDARY) >> true

    def primaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.value(
      IpvProvidersType.P1,
      IpvMeasureType.ABSOLUTE_DIFF,
      resolver
      ) >> primaryResult

    def secondaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.value(
      IpvProvidersType.P2,
      IpvMeasureType.ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> secondaryResult

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 2
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Value"
    breaks[0].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[0].operator == Operator.EQ
    breaks[0].threshold == ONE
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == primaryResult

    breaks[1].breakTestName == BREAK_TEST_NAME
    breaks[1].breakTestType == "Value"
    breaks[1].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[1].operator == Operator.EQ
    breaks[1].threshold == ONE
    breaks[1].providerType == IpvBreakProviderType.SECONDARY
    breaks[1].providerValue == secondaryResult
  }

  def "should correctly resolve DAY_TO_DAY breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.DAY_TO_DAY
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(IpvProvidersType.P2) >> true
    breakTest.getOperator() >> Operator.EQ
    2 * breakTest.resolveFirstThreshold(trade) >> ONE
    def resolver = Operator.EQ.resolver([ONE])
    1 * breakTest.resultResolver(trade) >> resolver
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.SECONDARY) >> true

    def primaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.dayOnDay(
      IpvProvidersType.P1,
      IpvMeasureType.ABSOLUTE_DIFF,
      resolver
      ) >> primaryResult

    def secondaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.dayOnDay(
      IpvProvidersType.P2,
      IpvMeasureType.ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> secondaryResult

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 2
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Day-on-day"
    breaks[0].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[0].operator == Operator.EQ
    breaks[0].threshold == ONE
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == primaryResult

    breaks[1].breakTestName == BREAK_TEST_NAME
    breaks[1].breakTestType == "Day-on-day"
    breaks[1].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[1].operator == Operator.EQ
    breaks[1].threshold == ONE
    breaks[1].providerType == IpvBreakProviderType.SECONDARY
    breaks[1].providerValue == secondaryResult
  }

  def "should correctly resolve DAY_TO_DAY_SIGN breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.DAY_TO_DAY_SIGN
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(IpvProvidersType.P2) >> true
    breakTest.getMeasureType() >> null
    breakTest.getOperator() >> null
    2 * breakTest.resolveFirstThreshold(trade) >> null
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.SECONDARY) >> true

    def primaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.dayToDaySign(IpvProvidersType.P1, _ as EntryResultCustomResolver) >> primaryResult

    def secondaryResult = Mock(EntryResultBreakByProvider)
    1 * calc.dayToDaySign(IpvProvidersType.P2, EntryResultCustomResolver.calculationOnly()) >> secondaryResult

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 2
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Day-on-day Sign"
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == primaryResult
    breaks[0].measureType == null
    breaks[0].operator == null
    breaks[0].threshold == null

    breaks[1].breakTestName == BREAK_TEST_NAME
    breaks[1].breakTestType == "Day-on-day Sign"
    breaks[1].providerType == IpvBreakProviderType.SECONDARY
    breaks[1].providerValue == secondaryResult
    breaks[1].measureType == null
    breaks[1].operator == null
    breaks[1].threshold == null
  }

  def "should correctly resolve PRIMARY_VS_SECONDARY breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.PRIMARY_VS_SECONDARY
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.getOperator() >> Operator.EQ
    1 * breakTest.resolveFirstThreshold(trade) >> ONE
    def resolver = Operator.EQ.resolver([ONE])
    1 * breakTest.resultResolver(trade) >> resolver

    def result = Mock(EntryResultBreakByProvider)
    1 * calc.providerDiff(
      IpvProvidersType.P2,
      IpvMeasureType.ABSOLUTE_DIFF,
      resolver
      ) >> result

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 1
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Primary vs Secondary Provider"
    breaks[0].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[0].operator == Operator.EQ
    breaks[0].threshold == ONE
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == result
  }

  def "should correctly resolve PRIMARY_VS_ACCOUNTING_COST breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade
    calc.getTrade().getVendorOnboardingDate() >> VALUATION_DATE
    calc.getTrade().getOnboardingPeriodEndDate() >> VALUATION_DATE
    calc.getTrade().getVendorCheck() >> true

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.PRIMARY_VS_ACCOUNTING_COST
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.getOperator() >> Operator.EQ
    1 * breakTest.resolveFirstThreshold(trade) >> ONE
    def resolver = Operator.EQ.resolver([ONE])
    1 * breakTest.resultResolver(trade) >> resolver

    def result = Mock(EntryResultBreakByProvider)
    1 * calc.providerDiff(
      IpvProvidersType.ACCOUNTING_COST,
      IpvMeasureType.ABSOLUTE_DIFF,
      resolver
      ) >> result

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 1
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Primary vs Accounting Cost (Onboarding Test)"
    breaks[0].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[0].operator == Operator.EQ
    breaks[0].threshold == ONE
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == result
  }

  def "should correctly resolve PRIMARY_VS_ACCOUNTING_COST breaks only when onboarding conditions are met"() {
    setup:
    def calc = Mock(TradeBreakCalculator)
    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.PRIMARY_VS_ACCOUNTING_COST
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.getOperator() >> Operator.EQ

    if (expectedOnboardingStatus == OnboardingStatus.FAILED) {
      1 * breakTest.resolveFirstThreshold(trade) >> ONE
      def resolver = Operator.EQ.resolver([ONE])
      1 * breakTest.resultResolver(trade) >> resolver

      def result = Mock(EntryResultBreakByProvider)
      1 * calc.providerDiff(
        IpvProvidersType.ACCOUNTING_COST,
        IpvMeasureType.ABSOLUTE_DIFF,
        resolver
        ) >> result
    }

    trade.getVendorOnboardingDate() >> vendorOnboardingDate
    trade.getOnboardingPeriodEndDate() >> onboardingPeriodEndDate
    trade.getVendorCheck() >> vendorCheck

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, stateDate)

    then:
    breaks.first().onboardingStatus == expectedOnboardingStatus

    where:
    vendorOnboardingDate        | onboardingPeriodEndDate  | vendorCheck | stateDate               | expectedOnboardingStatus
    LocalDate.of(2024,1,1)      | LocalDate.of(2024,12,31) | true        | LocalDate.of(2024,6,1)  | OnboardingStatus.FAILED               // break test applied
    LocalDate.of(2024,1,1)      | LocalDate.of(2024,12,31) | false       | LocalDate.of(2024,6,1)  | OnboardingStatus.NOT_REQUIRED               // vendorCheck is false
    LocalDate.of(2024,1,1)      | LocalDate.of(2024,12,31) | true        | LocalDate.of(2023,12,31)| OnboardingStatus.NOT_REQUIRED               // stateDate before vendorOnboardingDate
    LocalDate.of(2024,1,1)      | LocalDate.of(2024,12,31) | true        | LocalDate.of(2025,1,1)  | OnboardingStatus.NOT_REQUIRED               // stateDate after onboardingPeriodEndDate
  }

  def "should correctly resolve PRIMARY_VS_TERTIARY breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.PRIMARY_VS_TERTIARY
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.getOperator() >> Operator.EQ
    1 * breakTest.resolveFirstThreshold(trade) >> ONE
    def resolver = Operator.EQ.resolver([ONE])
    1 * breakTest.resultResolver(trade) >> resolver

    def result = Mock(EntryResultBreakByProvider)
    1 * calc.providerDiff(
      IpvProvidersType.P3,
      IpvMeasureType.ABSOLUTE_DIFF,
      resolver
      ) >> result

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 1
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Primary vs Tertiary Provider"
    breaks[0].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[0].operator == Operator.EQ
    breaks[0].threshold == ONE
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == result
  }

  def "should correctly resolve PRIMARY_VS_QUATERNARY breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.PRIMARY_VS_QUATERNARY
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.getOperator() >> Operator.EQ
    1 * breakTest.resolveFirstThreshold(trade) >> ONE
    def resolver = Operator.EQ.resolver([ONE])
    1 * breakTest.resultResolver(trade) >> resolver

    def result = Mock(EntryResultBreakByProvider)
    1 * calc.providerDiff(
      IpvProvidersType.P4,
      IpvMeasureType.ABSOLUTE_DIFF,
      resolver
      ) >> result

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 1
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == "Primary vs Quaternary Provider"
    breaks[0].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[0].operator == Operator.EQ
    breaks[0].threshold == ONE
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].providerValue == result
  }

  @Unroll
  def "should correctly resolve #providerType child breaks"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade
    calc.toChildBreakTestCalculator(_) >> { args ->
      TradeChildBreakCalculator.newOf(
        args[0],
        trade,
        new EntryBreakHistory(null, [new BreakTestHistory("dependentId", 2)]),
        new TradeBreakScalingData(null, null))
    }

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEntityId() >> "entityId"
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.DAY_TO_DAY
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(providerType) >> true
    breakTest.getOperator() >> Operator.EQ
    2 * breakTest.resolveFirstThreshold(trade) >> ONE
    def resolver = Operator.EQ.resolver([ONE])
    1 * breakTest.resultResolver(trade) >> resolver
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(breakProviderType) >> true

    def dependentTest = Mock(IpvBreakTest)
    dependentTest.getId() >> "dependentId"
    dependentTest.getParentTest() >> BreakTestParent.parentTest("entityId", BREAK_TEST_NAME)
    dependentTest.getEnabled() >> true
    dependentTest.matches(trade) >> true
    dependentTest.getType() >> childTestType
    dependentTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    dependentTest.getName() >> "TEST2"
    dependentTest.getOperator() >> Operator.GT
    1 * dependentTest.resolveFirstThreshold(trade) >> ONE
    1 * dependentTest.resultResolver(trade) >> Operator.GT.resolver([ONE])

    1 * calc.dayOnDay(
      IpvProvidersType.P1,
      IpvMeasureType.ABSOLUTE_DIFF,
      resolver
      ) >> EntryResultBreakByProvider.ofCalculationOnly(ONE)

    1 * calc.dayOnDay(
      providerType,
      IpvMeasureType.ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> EntryResultBreakByProvider.ofCalculationOnly(TEN)

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    calculation.acceptDependant(dependentTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 3
    breaks[0].breakTestName == BREAK_TEST_NAME
    breaks[0].breakTestType == IpvTestType.DAY_TO_DAY.name
    breaks[0].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[0].operator == Operator.EQ
    breaks[0].threshold == ONE
    breaks[0].providerType == IpvBreakProviderType.PRIMARY
    breaks[0].parentBreakTestName == null
    breaks[0].providerValue == EntryResultBreakByProvider.ofCalculationOnly(ONE)
    breaks[0].daysBreaking == 0

    breaks[1].breakTestName == BREAK_TEST_NAME
    breaks[1].breakTestType == IpvTestType.DAY_TO_DAY.name
    breaks[1].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[1].operator == Operator.EQ
    breaks[1].threshold == ONE
    breaks[1].providerType == breakProviderType
    breaks[1].parentBreakTestName == null
    breaks[1].providerValue == EntryResultBreakByProvider.ofCalculationOnly(TEN)
    breaks[1].daysBreaking == 0

    breaks[2].breakTestName == dependentTest.name
    breaks[2].breakTestType == childTestType.name
    breaks[2].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[2].operator == Operator.GT
    breaks[2].threshold == ONE
    breaks[2].providerType == IpvBreakProviderType.PRIMARY
    breaks[2].parentBreakTestName == BREAK_TEST_NAME
    breaks[2].providerValue == new EntryResultBreakByProvider(triggered: true, triggeredThreshold: ONE, triggeredThresholdLevel: 1, value: valueOf(9))
    breaks[2].daysBreaking == 3

    where:
    childTestType                     | providerType        | breakProviderType
    IpvTestType.PRIMARY_VS_TERTIARY   | IpvProvidersType.P3 | IpvBreakProviderType.TERTIARY
    IpvTestType.PRIMARY_VS_SECONDARY  | IpvProvidersType.P2 | IpvBreakProviderType.SECONDARY
    IpvTestType.PRIMARY_VS_QUATERNARY | IpvProvidersType.P4 | IpvBreakProviderType.QUATERNARY
  }

  @Unroll
  def "should correctly resolve child test with null provider value"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade
    calc.toChildBreakTestCalculator(_) >> { args ->
      TradeChildBreakCalculator.newOf(
        args[0],
        trade,
        new EntryBreakHistory(null, []),
        new TradeBreakScalingData(null, null))
    }

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEntityId() >> "entityId"
    breakTest.getEnabled() >> true
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.DAY_TO_DAY
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(IpvProvidersType.P3) >> true
    breakTest.getOperator() >> Operator.EQ
    2 * breakTest.resolveFirstThreshold(trade) >> ONE
    def resolver = Operator.EQ.resolver([ONE])
    1 * breakTest.resultResolver(trade) >> resolver
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.SECONDARY) >> true
    calc.hasMappedProviderType(IpvBreakProviderType.TERTIARY) >> true

    def dependentTest = Mock(IpvBreakTest)
    dependentTest.getParentTest() >> BreakTestParent.parentTest("entityId", BREAK_TEST_NAME)
    dependentTest.getEnabled() >> true
    dependentTest.matches(trade) >> true
    dependentTest.getType() >> IpvTestType.PRIMARY_VS_TERTIARY
    dependentTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    dependentTest.getName() >> "TEST2"
    dependentTest.getOperator() >> Operator.GT
    1 * dependentTest.resolveFirstThreshold(trade) >> ONE
    1 * dependentTest.resultResolver(trade) >> Operator.GT.resolver([ONE])

    1 * calc.dayOnDay(
      IpvProvidersType.P1,
      IpvMeasureType.ABSOLUTE_DIFF,
      resolver
      ) >> EntryResultBreakByProvider.ofCalculationOnly(null)

    1 * calc.dayOnDay(
      IpvProvidersType.P3,
      IpvMeasureType.ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> EntryResultBreakByProvider.ofCalculationOnly(TEN)

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    calculation.acceptDependant(dependentTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 3
    breaks[2].breakTestName == dependentTest.name
    breaks[2].breakTestType == IpvTestType.PRIMARY_VS_TERTIARY.name
    breaks[2].measureType == IpvMeasureType.ABSOLUTE_DIFF
    breaks[2].operator == Operator.GT
    breaks[2].threshold == ONE
    breaks[2].providerType == IpvBreakProviderType.PRIMARY
    breaks[2].parentBreakTestName == BREAK_TEST_NAME
    breaks[2].providerValue == new EntryResultBreakByProvider(triggered: false)
    breaks[2].daysBreaking == 0
  }

  @Unroll
  def "should correctly resolve #childTestType child breaks with archived parent test"() {
    setup:
    def calc = Mock(TradeBreakCalculator)

    def trade = Mock(Trade)
    calc.getTrade() >> trade
    calc.toChildBreakTestCalculator(_) >> { args ->
      TradeChildBreakCalculator.newOf(
        args[0],
        trade,
        new EntryBreakHistory(null, []),
        new TradeBreakScalingData(null, null))
    }

    def breakTest = Mock(IpvBreakTest)
    breakTest.getEntityId() >> "entityId"
    breakTest.matches(trade) >> true
    breakTest.getType() >> IpvTestType.DAY_TO_DAY
    breakTest.getName() >> BREAK_TEST_NAME
    breakTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    breakTest.isForProvider(IpvProvidersType.P1) >> true
    breakTest.isForProvider(providerType) >> true
    breakTest.getOperator() >> Operator.EQ
    2 * breakTest.resolveFirstThreshold(trade) >> ONE
    breakTest.getEnabled() >> false
    0 * breakTest.resultResolver(_)
    calc.hasMappedProviderType(IpvBreakProviderType.PRIMARY) >> true
    calc.hasMappedProviderType(breakProviderType) >> true

    def dependentTest = Mock(IpvBreakTest)
    dependentTest.getParentTest() >> BreakTestParent.parentTest("entityId", BREAK_TEST_NAME)
    dependentTest.getEnabled() >> true
    dependentTest.matches(trade) >> true
    dependentTest.getType() >> childTestType
    dependentTest.getMeasureType() >> IpvMeasureType.ABSOLUTE_DIFF
    dependentTest.getName() >> "TEST2"
    dependentTest.getOperator() >> Operator.GT
    1 * dependentTest.resolveFirstThreshold(trade) >> ONE
    1 * dependentTest.resultResolver(trade) >> Operator.GT.resolver([ONE])

    1 * calc.dayOnDay(
      IpvProvidersType.P1,
      IpvMeasureType.ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> EntryResultBreakByProvider.ofCalculationOnly(ONE)

    1 * calc.dayOnDay(
      providerType,
      IpvMeasureType.ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> EntryResultBreakByProvider.ofCalculationOnly(TEN)

    when:
    def calculation = IpvBreakTestCalculation.ofBreakTest(breakTest)
    calculation.acceptDependant(dependentTest)
    def breaks = calculation.resolveBreak(calc, VALUATION_DATE)

    then:
    breaks.size() == 3
    breaks[0].providerValue == EntryResultBreakByProvider.ofCalculationOnly(ONE)
    breaks[0].daysBreaking == 0

    breaks[1].providerValue == EntryResultBreakByProvider.ofCalculationOnly(TEN)
    breaks[1].daysBreaking == 0

    breaks[2].providerValue == new EntryResultBreakByProvider(triggered: true, triggeredThreshold: ONE, triggeredThresholdLevel: 1, value: valueOf(9))
    breaks[2].daysBreaking == 1

    where:
    childTestType                     | providerType        | breakProviderType
    IpvTestType.PRIMARY_VS_TERTIARY   | IpvProvidersType.P3 | IpvBreakProviderType.TERTIARY
    IpvTestType.PRIMARY_VS_SECONDARY  | IpvProvidersType.P2 | IpvBreakProviderType.SECONDARY
    IpvTestType.PRIMARY_VS_QUATERNARY | IpvProvidersType.P4 | IpvBreakProviderType.QUATERNARY
  }
}
