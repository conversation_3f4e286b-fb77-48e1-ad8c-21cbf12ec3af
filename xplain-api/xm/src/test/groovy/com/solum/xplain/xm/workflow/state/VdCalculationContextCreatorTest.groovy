package com.solum.xplain.xm.workflow.state

import static org.apache.commons.collections.CollectionUtils.isEqualCollection

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.company.value.PortfolioSettings
import com.solum.xplain.core.company.value.ProvidersVo
import com.solum.xplain.core.company.value.SlaDeadlinePortfolioView
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter
import com.solum.xplain.xm.dashboardsteps.opvvaluations.DashboardMarketDataSourceResolver
import com.solum.xplain.xm.excmngmt.processipv.CachingPortfolioSettingsService
import io.atlassian.fugue.Either
import spock.lang.Specification

class VdCalculationContextCreatorTest extends Specification {
  CachingPortfolioSettingsService cachingPortfolioSettingsService = Mock()
  DashboardMarketDataSourceResolver marketDataSourceResolver = Mock()
  VdCalculationContextCreator vdCalculationContextCreator = new VdCalculationContextCreator(cachingPortfolioSettingsService, marketDataSourceResolver)

  def "should group trades by portfolio to define calculation contexts"() {
    given:
    def stateDate = BitemporalDate.newOfNow()
    def providers = new ProvidersVo("XPLAIN", "SEC", "TER", "QUA")
    def providersNoXplain = new ProvidersVo("PRI", "SEC", "TER", "QUA")
    def dashboardContext = new VdDashboardContext(VdExceptionManagementPortfolioFilter.empty(),
      [
        SlaDeadlinePortfolioView.newOf("portfolioId1", "externalPortfolioId1", "companyId", "externalCompanyId", "entityId", "externalEntityId", SlaDeadline.LDN_1600),
        SlaDeadlinePortfolioView.newOf("portfolioId2", "externalPortfolioId2", "companyId", "externalCompanyId", "entityId", "externalEntityId", SlaDeadline.LDN_1200)
      ], stateDate, UserBuilder.user())
    def dashboardState = new VdDashboardState(
      trades: [
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1600, "portfolioId1", "tradeId1", CoreProductType.CAP_FLOOR, providers),
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1600, "portfolioId1", "tradeId2", CoreProductType.FXOPT, providersNoXplain),
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1600, "portfolioId1", "tradeId3", CoreProductType.CAP_FLOOR, providersNoXplain),
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1600, "portfolioId1", "tradeId4", CoreProductType.INFLATION, providers),
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1500, "portfolioId2", "tradeId5", CoreProductType.CAP_FLOOR, providers),
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1615, "portfolioId2", "tradeId6", CoreProductType.CAP_FLOOR, providers),
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1500, "portfolioId2", "tradeId7", CoreProductType.SWAPTION, providers)
      ],
      calculationNeeded: true
      )
    def valuationSettings = new CompanyLegalEntityValuationSettingsView(marketDataGroupId: "mdg1")
    marketDataSourceResolver.resolve("mdg1", dashboardContext.stateDate().actualDate) >> MarketDataSourceType.OVERLAY

    def allPortfolios = dashboardContext.portfolioMap().values()
    1 * cachingPortfolioSettingsService.prefetchPortfolioSettings({ isEqualCollection(it, allPortfolios) }, stateDate)
    cachingPortfolioSettingsService.portfolioSettings(dashboardContext.portfolios()[0], _ as BitemporalDate) >>
      Either.right(PortfolioSettings.settings(new PortfolioView(id: "portfolioId1"), CompanyLegalEntitySettingsView.newOf(valuationSettings, null)))
    cachingPortfolioSettingsService.portfolioSettings(dashboardContext.portfolios()[1], _ as BitemporalDate) >>
      Either.right(PortfolioSettings.settings(new PortfolioView(id: "portfolioId2"), CompanyLegalEntitySettingsView.newOf(valuationSettings, null)))

    when:
    def contexts = vdCalculationContextCreator.subprocessContext(dashboardState, dashboardContext).toList()

    then:
    contexts.size() == 2
    with (contexts.find { it.portfolio().id == "portfolioId1" }) {
      it.createdBy().userId == dashboardContext.principal().id
      it.settings() == valuationSettings
      with (it.portfolio()) {
        it.id == "portfolioId1"
        it.externalPortfolioId == "externalPortfolioId1"
        it.companyId == "companyId"
        it.externalCompanyId == "externalCompanyId"
        it.entityId == "entityId"
        it.externalEntityId == "externalEntityId"
      }
      it.sourceType() == MarketDataSourceType.OVERLAY
      it.stateDate() == dashboardContext.stateDate()
      it.productTypes().size() == 2
      it.productTypes().containsAll([CoreProductType.CAP_FLOOR, CoreProductType.INFLATION])
    }
    with (contexts.find { it.portfolio().id == "portfolioId2" }) {
      it.createdBy().userId == dashboardContext.principal().id
      it.settings() == valuationSettings
      with (it.portfolio()) {
        it.id == "portfolioId2"
        it.externalPortfolioId == "externalPortfolioId2"
        it.companyId == "companyId"
        it.externalCompanyId == "externalCompanyId"
        it.entityId == "entityId"
        it.externalEntityId == "externalEntityId"
      }
      it.sourceType() == MarketDataSourceType.OVERLAY
      it.stateDate() == dashboardContext.stateDate()
      it.productTypes().size() == 2
      it.productTypes().containsAll([CoreProductType.CAP_FLOOR, CoreProductType.SWAPTION])
    }
  }

  def "should create unique business key"() {
    given:
    VdCalculationContext context = new VdCalculationContext(
      AuditUser.of(UserBuilder.user()),
      new CompanyLegalEntityValuationSettingsView(),
      PortfolioCondensedView.newOf("portfolioId", "externalPortfolioId", "companyId", "externalCompanyId", "entityId", "externalEntityId"),
      MarketDataSourceType.RAW_PRIMARY,
      BitemporalDate.newOfNow(),
      [CoreProductType.CAP_FLOOR, CoreProductType.INFLATION]
      )

    when:
    def key = vdCalculationContextCreator.subprocessBusinessKey("parent", context)

    then:
    key == "parent-portfolioId"
  }

  def "should return correct state type"() {
    expect:
    vdCalculationContextCreator.subprocessStateType() == VdCalculationState
  }
}
