package com.solum.xplain.xva.config

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.xva.proxy.messages.XvaPartyExposureFile
import java.time.LocalDate
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Test to verify that we can deserialize XVA scalar results when allowScalarsInResults is set to true
 * TODO: SXSD-10497 Investigate and Refactor Scalar Field Handling in XVA Results
 * Refactor test to remove ACCEPT_SINGLE_VALUE_AS_ARRAY to verify SXSD-10497
 */
@SpringBootTest
@ContextConfiguration(classes = [XvaJacksonConfig.class])
class XvaScalarDeserializationTest extends Specification {

  @Autowired
  @Qualifier("xvaObjectMapper")
  ObjectMapper xvaMapper



  def "should deserialize array format for XvaPartyExposureFile"() {
    given:
    def jsonWithArrays = '''
    {
      "PFEStatus": ["OK"],
      "NumTrades": [2],
      "NumWhatIfTrades": [0],
      "AnchorDate": ["2022-11-30"],
      "EPE": [4353163.37624546, 4354147.57789525],
      "Time": [0, 0.0833333333333333]
    }
    '''

    when:
    def result = xvaMapper.readValue(jsonWithArrays, XvaPartyExposureFile.class)

    then:
    result.pfeStatus == ["OK"]
    result.numTrades == [2]
    result.numWhatIfTrades == [0]
    result.anchorDate == [LocalDate.parse("2022-11-30")]
    result.epe == [4353163.37624546, 4354147.57789525]
    result.time == [0, 0.0833333333333333]
  }

  def "should deserialize scalar format for XvaPartyExposureFile with ACCEPT_SINGLE_VALUE_AS_ARRAY"() {
    given:
    def jsonWithScalars = '''
    {
      "PFEStatus": "OK",
      "NumTrades": 2,
      "NumWhatIfTrades": 0,
      "AnchorDate": "2022-11-30",
      "EPE": [4353163.37624546, 4354147.57789525],
      "Time": [0, 0.0833333333333333]
    }
    '''

    when:
    def result = xvaMapper.readValue(jsonWithScalars, XvaPartyExposureFile.class)

    then:
    result.pfeStatus == ["OK"]
    result.numTrades == [2]
    result.numWhatIfTrades == [0]
    result.anchorDate == [LocalDate.parse("2022-11-30")]
    result.epe == [4353163.37624546, 4354147.57789525]
    result.time == [0, 0.0833333333333333]
  }

  def "should deserialize mixed format with some scalars and some arrays"() {
    given:
    def jsonMixed = '''
    {
      "PFEStatus": "OK",
      "NumTrades": 2,
      "NumWhatIfTrades": [0],
      "AnchorDate": "2022-11-30",
      "EPE": [4353163.37624546, 4354147.57789525],
      "Time": [0, 0.0833333333333333]
    }
    '''

    when:
    def result = xvaMapper.readValue(jsonMixed, XvaPartyExposureFile.class)

    then:
    result.pfeStatus == ["OK"]
    result.numTrades == [2]
    result.numWhatIfTrades == [0]
    result.anchorDate == [LocalDate.parse("2022-11-30")]
    result.epe == [4353163.37624546, 4354147.57789525]
    result.time == [0, 0.0833333333333333]
  }

  def "should fail without ACCEPT_SINGLE_VALUE_AS_ARRAY feature"() {
    given:
    def mapper = new ObjectMapper()
    def jsonWithScalars = '''
    {
      "PFEStatus": "OK",
      "NumTrades": 2,
      "NumWhatIfTrades": 0,
      "AnchorDate": "2022-11-30"
    }
    '''

    when:
    mapper.readValue(jsonWithScalars, XvaPartyExposureFile.class)

    then:
    thrown(Exception)
  }

  def "should verify XVA mapper has ACCEPT_SINGLE_VALUE_AS_ARRAY enabled"() {
    expect:
    xvaMapper.isEnabled(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
  }

  def "should verify standard mapper has ACCEPT_SINGLE_VALUE_AS_ARRAY disabled"() {
    given:
    def mapper = new ObjectMapper()
    expect:
    !mapper.isEnabled(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
  }
}
