package com.solum.xplain.xva.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * XVA-specific Jackson configuration to handle scalar field deserialization. This configuration
 * enables ACCEPT_SINGLE_VALUE_AS_ARRAY specifically for XVA
 *
 * <p>TODO: SXSD-10497 - Investigate and Refactor Scalar Field Handling in XVA Results
 */
@Configuration
public class XvaJacksonConfig {

  @Bean
  @Qualifier("xvaObjectMapper")
  public ObjectMapper xvaObjectMapper() {
    return new ObjectMapper()
        .registerModule(new JavaTimeModule())
        .enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
  }
}
